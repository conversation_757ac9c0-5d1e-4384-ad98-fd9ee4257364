{"name": "BuddyChipUtimate", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "check": "biome ci .", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate"}, "devDependencies": {"@biomejs/biome": "^2.0.0", "turbo": "^2.5.4"}, "packageManager": "pnpm@10.12.1", "dependencies": {"import-in-the-middle": "^1.14.2", "require-in-the-middle": "^7.5.2"}}