{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "biome check src/", "lint:fix": "biome check --write src/", "format": "biome format --write src/", "check-types": "tsc --noEmit"}, "dependencies": {"@clerk/elements": "^0.23.33", "@clerk/nextjs": "^6.22.0", "@clerk/themes": "^2.2.50", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@sentry/nextjs": "^9.30.0", "@sentry/react": "^9.30.0", "@tanstack/react-form": "^1.3.2", "@tanstack/react-query": "^5.80.5", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "next": "15.3.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.1.10", "typescript": "^5"}}