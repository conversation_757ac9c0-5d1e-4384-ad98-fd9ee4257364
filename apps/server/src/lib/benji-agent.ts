/**
 * Benji AI Agent - Core Implementation
 * 
 * The main AI agent that orchestrates all tools and generates responses
 */

import { streamText, generateId } from 'ai';
import { getModel, getModelByPlan, type ModelName } from './ai-providers';
import { xaiSearchTool } from './tools/xai-search';
import { exaSearchTool } from './tools/exa-search';
import { imageGenerationTool } from './tools/openai-image';
import { prisma } from './db-utils';

export interface BenjiConfig {
  model?: ModelName;
  userId?: string;
  userPlan?: string;
  maxTokens?: number;
  temperature?: number;
  enableTools?: boolean;
  maxSteps?: number;
}

export interface BenjiContext {
  mentionId?: string;
  mentionContent?: string;
  authorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

export class BenjiAgent {
  private config: BenjiConfig;
  
  constructor(config: BenjiConfig = {}) {
    this.config = {
      model: config.model || 'gemini25Flash',
      userId: config.userId,
      userPlan: config.userPlan || 'reply-guy',
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.7,
      enableTools: config.enableTools ?? true,
      maxSteps: config.maxSteps || 5,
    };
    
    // Auto-select model based on user plan if not specified
    if (!config.model && config.userPlan) {
      this.config.model = getModelByPlan(config.userPlan);
    }
  }

  /**
   * Generate a response for a Twitter mention
   */
  async generateMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt('mention', context);
    
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: `Please analyze this tweet and generate an appropriate response:\n\n"${mentionContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Generate a response for any tweet (Quick Reply feature)
   */
  async generateQuickReply(
    tweetContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt('quick-reply', context);
    
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: `Generate a thoughtful response to this tweet:\n\n"${tweetContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Calculate bullish score for a tweet
   */
  async calculateBullishScore(content: string): Promise<number> {
    const model = getModel(this.config.model!);
    
    const result = await streamText({
      model,
      messages: [
        {
          role: 'system',
          content: `You are a sentiment analysis expert. Analyze the sentiment and positivity of tweets and return a "bullish score" from 1-100 where:
          - 1-20: Very negative, bearish, pessimistic
          - 21-40: Somewhat negative, skeptical
          - 41-60: Neutral, mixed sentiment
          - 61-80: Positive, optimistic
          - 81-100: Very positive, bullish, enthusiastic
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: 'user',
          content: `Analyze this tweet and give it a bullish score: "${content}"`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = '';
    for await (const chunk of result.textStream) {
      text += chunk;
    }
    
    const score = parseInt(text.trim().replace(/\D/g, ''));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Core streaming response method
   */
  private async streamResponse(
    messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
    context: BenjiContext = {}
  ) {
    const model = getModel(this.config.model!);
    
    const tools = this.config.enableTools ? {
      searchWeb: xaiSearchTool,
      searchKnowledge: exaSearchTool,
      generateImage: imageGenerationTool,
    } : undefined;

    try {
      const result = streamText({
        model,
        messages,
        tools,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        maxSteps: this.config.maxSteps,
      });

      return result;
    } catch (error) {
      console.error('Benji Agent error:', error);
      throw new Error(`AI generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build system prompt based on context
   */
  private buildSystemPrompt(type: 'mention' | 'quick-reply', context: BenjiContext): string {
    const basePrompt = `You are Benji, an advanced AI assistant specialized in social media engagement. You help users craft thoughtful, engaging responses to tweets and social media posts.

Your personality:
- Professional yet personable
- Knowledgeable and helpful
- Concise but thorough when needed
- Always respectful and positive

Your capabilities:
- Real-time web search for current information
- Knowledge retrieval for detailed research
- Image generation for visual content
- Context awareness for personalized responses

Guidelines:
- Keep responses conversational and authentic
- Avoid overly promotional language
- Match the tone of the original tweet when appropriate
- Provide value in your responses (insights, questions, resources)
- Stay within Twitter's character limits (280 characters) unless specifically asked for longer content
- Use tools when they can provide better, more current, or more detailed information`;

    if (type === 'mention') {
      return basePrompt + `\n\nYou are responding to a tweet that mentioned one of the user's monitored accounts. Generate a professional and engaging response that:
- Acknowledges the mention appropriately
- Provides helpful information or insights
- Encourages further engagement
- Represents the monitored account professionally`;
    }
    
    return basePrompt + `\n\nYou are helping generate a quick reply to any tweet. Focus on:
- Creating an authentic, valuable response
- Adding to the conversation meaningfully
- Being genuinely helpful or insightful
- Maintaining a friendly, professional tone`;
  }

}

// Convenience function for quick usage
export function createBenjiAgent(config: BenjiConfig = {}) {
  return new BenjiAgent(config);
}

// Helper function to get agent for user
export async function getBenjiForUser(userId: string) {
  // Get user's plan from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { plan: true },
  });

  if (!user) {
    throw new Error('User not found');
  }

  return new BenjiAgent({
    userId,
    userPlan: user.plan.name,
    enableTools: true,
  });
}