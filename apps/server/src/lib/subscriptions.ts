/**
 * Subscription Plan Configuration for BuddyChip
 * 
 * Manages user subscription plans and their associated model access
 */

import type { ModelName } from './ai-providers';

export const subscriptionPlans = {
  'reply-guy': {
    name: '<PERSON>ly <PERSON>',
    price: '$9/month',
    features: [
      'Basic AI replies',
      'Gemini 2.5 Flash model',
      '1,000 replies/month',
      'Email support'
    ],
    modelAccess: ['gemini25Flash'] as ModelName[],
    defaultModel: 'gemini25Flash' as ModelName,
    limits: {
      repliesPerMonth: 1000,
      rateLimitPerMinute: 10,
    }
  },
  'reply-god': {
    name: 'Reply God',
    price: '$29/month',
    features: [
      'Advanced AI replies',
      'Gemini 2.5 Pro model',
      'Unlimited replies',
      'Priority support',
      'Custom personas'
    ],
    modelAccess: ['gemini25Pro', 'gemini25Flash'] as ModelName[],
    defaultModel: 'gemini25Pro' as ModelName,
    limits: {
      repliesPerMonth: -1, // unlimited
      rateLimitPerMinute: 30,
    }
  },
  'team-plan': {
    name: 'Team Plan',
    price: '$99/month',
    features: [
      'Everything in Reply God',
      'Team collaboration',
      'Admin dashboard',
      'Dedicated support',
      'Custom integrations'
    ],
    modelAccess: ['openaiO3', 'gemini25Flash'] as ModelName[],
    defaultModel: 'openaiO3' as ModelName,
    limits: {
      repliesPerMonth: -1, // unlimited
      rateLimitPerMinute: 60,
    }
  }
} as const;

export type PlanName = keyof typeof subscriptionPlans;

// Helper function to get model by user plan
export function getModelByPlan(planName: string): ModelName {
  const plan = subscriptionPlans[planName as PlanName];
  return plan?.defaultModel || 'gemini25Flash';
}

// Check if user has access to a specific model
export function hasModelAccess(planName: string, modelName: ModelName): boolean {
  const plan = subscriptionPlans[planName as PlanName];
  return plan?.modelAccess.includes(modelName) || false;
}

// Get plan details
export function getPlanDetails(planName: string) {
  return subscriptionPlans[planName as PlanName] || subscriptionPlans['reply-guy'];
}

// Get subscription plan (alias for getPlanDetails for compatibility)
export function getSubscriptionPlan(planName: string) {
  return getPlanDetails(planName);
}

// Check usage limits
export function checkUsageLimit(planName: string, currentUsage: number): boolean {
  const plan = subscriptionPlans[planName as PlanName];
  if (!plan) return false;
  
  const limit = plan.limits.repliesPerMonth;
  return limit === -1 || currentUsage < limit;
}