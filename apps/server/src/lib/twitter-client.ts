/**
 * Twitter API Client for BuddyChip
 * 
 * Integrates with TwitterAPI.io for fetching mentions, replies, and tweet data
 * Includes rate limiting, caching, and error handling
 */

import { z } from 'zod';

// API Configuration
const TWITTER_API_BASE_URL = 'https://api.twitterapi.io';
const TWITTER_API_KEY = process.env.TWITTER_API_KEY;

if (!TWITTER_API_KEY) {
  throw new Error('TWITTER_API_KEY environment variable is required');
}

// Response schemas for type safety
const TwitterUserSchema = z.object({
  type: z.string().optional(),
  userName: z.string(),
  url: z.string().optional(),
  id: z.string(),
  name: z.string(),
  isBlueVerified: z.boolean().optional(),
  verifiedType: z.string().optional(),
  profilePicture: z.string().optional(),
  coverPicture: z.string().optional(),
  description: z.string().optional(),
  location: z.string().optional(),
  followers: z.number().optional(),
  following: z.number().optional(),
  canDm: z.boolean().optional(),
  createdAt: z.string().optional(),
  favouritesCount: z.number().optional(),
  hasCustomTimelines: z.boolean().optional(),
  isTranslator: z.boolean().optional(),
  mediaCount: z.number().optional(),
  statusesCount: z.number().optional(),
  withheldInCountries: z.array(z.string()).optional(),
  affiliatesHighlightedLabel: z.any().optional(),
  possiblySensitive: z.boolean().optional(),
  pinnedTweetIds: z.array(z.string()).optional(),
  isAutomated: z.boolean().optional(),
  automatedBy: z.string().optional(),
  unavailable: z.boolean().optional(),
  message: z.string().optional(),
  unavailableReason: z.string().optional(),
  profile_bio: z.object({
    description: z.string(),
    entities: z.object({
      description: z.object({
        urls: z.array(z.object({
          display_url: z.string(),
          expanded_url: z.string(),
          indices: z.array(z.number()),
          url: z.string(),
        })),
      }),
      url: z.object({
        urls: z.array(z.object({
          display_url: z.string(),
          expanded_url: z.string(),
          indices: z.array(z.number()),
          url: z.string(),
        })),
      }),
    }),
  }).optional(),
});

const TwitterTweetSchema = z.object({
  type: z.string().optional(),
  id: z.string(),
  url: z.string().optional(),
  text: z.string(),
  source: z.string().optional(),
  retweetCount: z.number().optional(),
  replyCount: z.number().optional(),
  likeCount: z.number().optional(),
  quoteCount: z.number().optional(),
  viewCount: z.number().optional(),
  createdAt: z.string(),
  lang: z.string().optional(),
  bookmarkCount: z.number().optional(),
  isReply: z.boolean().optional(),
  inReplyToId: z.string().optional(),
  conversationId: z.string().optional(),
  inReplyToUserId: z.string().optional(),
  inReplyToUsername: z.string().optional(),
  author: TwitterUserSchema,
  entities: z.object({
    hashtags: z.array(z.object({
      indices: z.array(z.number()),
      text: z.string(),
    })).optional(),
    urls: z.array(z.object({
      display_url: z.string(),
      expanded_url: z.string(),
      indices: z.array(z.number()),
      url: z.string(),
    })).optional(),
    user_mentions: z.array(z.object({
      id_str: z.string(),
      name: z.string(),
      screen_name: z.string(),
    })).optional(),
  }).optional(),
  quoted_tweet: z.any().optional(),
  retweeted_tweet: z.any().optional(),
});

// API Response schemas matching TwitterAPI.io spec
const MentionsResponseSchema = z.object({
  tweets: z.array(TwitterTweetSchema),
  has_next_page: z.boolean(),
  next_cursor: z.string(),
  status: z.enum(['success', 'error']),
  message: z.string(),
});

const RepliesResponseSchema = z.object({
  tweets: z.array(TwitterTweetSchema),
  has_next_page: z.boolean(),
  next_cursor: z.string(),
  status: z.enum(['success', 'error']),
  message: z.string(),
});

export type TwitterUser = z.infer<typeof TwitterUserSchema>;
export type TwitterTweet = z.infer<typeof TwitterTweetSchema>;
export type MentionsResponse = z.infer<typeof MentionsResponseSchema>;
export type RepliesResponse = z.infer<typeof RepliesResponseSchema>;

// Simple in-memory cache for reducing API calls
const cache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

class TwitterAPIClient {
  private async makeRequest(endpoint: string, params: Record<string, any> = {}): Promise<any> {
    const url = new URL(`${TWITTER_API_BASE_URL}${endpoint}`);
    
    // Add query parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });

    // Check cache first
    const cacheKey = url.toString();
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      console.log(`✅ Cache hit for ${endpoint}`);
      return cached.data;
    }

    try {
      console.log(`🔄 Making Twitter API request: ${endpoint}`);
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'X-API-Key': TWITTER_API_KEY!,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Twitter API error (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      
      // Cache successful responses
      cache.set(cacheKey, { data, timestamp: Date.now() });
      
      console.log(`✅ Twitter API success: ${endpoint} (${data?.data?.length || 0} items)`);
      return data;
      
    } catch (error) {
      console.error(`❌ Twitter API error for ${endpoint}:`, error);
      throw new Error(`Failed to fetch from Twitter API: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get mentions for a specific Twitter user
   * Matches TwitterAPI.io spec: GET /twitter/user/mentions
   */
  async getUserMentions(username: string, options: {
    cursor?: string;
    sinceTime?: number;
    untilTime?: number;
    limit?: number;
  } = {}): Promise<{
    tweets: TwitterTweet[];
    has_next_page: boolean;
    next_cursor: string;
    status: 'success' | 'error';
    message: string;
  }> {
    const params: Record<string, any> = {
      userName: username.replace('@', ''), // API expects userName (capital N)
    };

    // Add optional parameters according to API spec
    if (options.cursor) {
      params.cursor = options.cursor;
    }
    if (options.sinceTime) {
      params.sinceTime = options.sinceTime;
    }
    if (options.untilTime) {
      params.untilTime = options.untilTime;
    }
    if (options.limit) {
      params.limit = options.limit;
    }

    const response = await this.makeRequest('/twitter/user/mentions', params);
    
    // API returns exactly this format according to spec
    return {
      tweets: response.tweets || [],
      has_next_page: response.has_next_page || false,
      next_cursor: response.next_cursor || '',
      status: response.status || 'success',
      message: response.message || ''
    };
  }

  /**
   * Get replies to a specific tweet
   */
  async getTweetReplies(tweetId: string, options: {
    cursor?: string;
    limit?: number;
  } = {}): Promise<RepliesResponse> {
    const params: Record<string, any> = {
      tweet_id: tweetId,
      count: options.limit || 20,
    };

    if (options.cursor) {
      params.cursor = options.cursor;
    }

    const response = await this.makeRequest('/twitter/tweet/replies', params);
    return RepliesResponseSchema.parse(response);
  }

  /**
   * Get user information by username
   */
  async getUserInfo(username: string): Promise<TwitterUser> {
    console.log('🐦 TwitterClient: Getting user info for:', username);
    const params = {
      userName: username.replace('@', ''), // API expects userName with capital N
    };
    console.log('📝 TwitterClient: Request params:', params);

    const response = await this.makeRequest('/twitter/user/info', params);
    console.log('📄 TwitterClient: Raw response:', response);

    const parsedUser = TwitterUserSchema.parse(response.data);
    console.log('✅ TwitterClient: Parsed user data:', parsedUser);
    return parsedUser;
  }

  /**
   * Extract tweet ID from Twitter/X URL
   */
  extractTweetId(url: string): string | null {
    // Handle both twitter.com and x.com URLs
    const tweetRegex = /(?:twitter\.com|x\.com)\/\w+\/status\/(\d+)/;
    const match = url.match(tweetRegex);
    return match ? match[1] : null;
  }

  /**
   * Extract username from Twitter/X URL
   */
  extractUsername(url: string): string | null {
    // Handle both twitter.com and x.com URLs
    const userRegex = /(?:twitter\.com|x\.com)\/(\w+)(?:\/|$)/;
    const match = url.match(userRegex);
    return match ? match[1] : null;
  }

  /**
   * Validate if a string is a valid Twitter handle
   */
  validateTwitterHandle(handle: string): boolean {
    const cleanHandle = handle.replace('@', '');
    // Twitter handles: 1-15 characters, alphanumeric and underscores only
    const handleRegex = /^[a-zA-Z0-9_]{1,15}$/;
    return handleRegex.test(cleanHandle);
  }

  /**
   * Validate if a URL is a valid Twitter/X URL
   */
  validateTwitterUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return ['twitter.com', 'x.com', 'www.twitter.com', 'www.x.com'].includes(urlObj.hostname);
    } catch {
      return false;
    }
  }

  /**
   * Convert twitter.com URLs to x.com and vice versa
   */
  normalizeTwitterUrl(url: string): string {
    return url.replace(/(?:www\.)?twitter\.com/g, 'x.com');
  }

  /**
   * Get tweet content from URL (for Quick Reply feature)
   */
  async getTweetFromUrl(url: string): Promise<TwitterTweet | null> {
    const tweetId = this.extractTweetId(url);
    if (!tweetId) {
      throw new Error('Invalid Twitter URL - could not extract tweet ID');
    }

    try {
      const response = await this.makeRequest('/twitter/tweet/info', {
        tweet_id: tweetId,
      });
      
      return TwitterTweetSchema.parse(response.data);
    } catch (error) {
      console.error('Failed to fetch tweet from URL:', error);
      return null;
    }
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    cache.clear();
    console.log('🗑️ Twitter API cache cleared');
  }

  /**
   * Get cache stats
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: cache.size,
      keys: Array.from(cache.keys()),
    };
  }
}

// Export singleton instance
export const twitterClient = new TwitterAPIClient();

// Export types for use in other files
export {
  TwitterAPIClient,
  TwitterUserSchema,
  TwitterTweetSchema,
  MentionsResponseSchema,
  RepliesResponseSchema,
};