/**
 * xAI Live Search Tool
 * 
 * Provides real-time web search capabilities using xAI's Live Search API
 */

import { tool } from 'ai';
import { z } from 'zod';

export const xaiSearchTool = tool({
  description: 'Search the web in real-time using xAI Live Search for current information and trends',
  parameters: z.object({
    query: z.string().describe('Search query for current information'),
    maxResults: z.number().optional().default(5).describe('Maximum number of results to return'),
    safeSearch: z.enum(['strict', 'moderate', 'off']).optional().default('moderate'),
  }),
  execute: async ({ query, maxResults, safeSearch }) => {
    try {
      if (!process.env.XAI_API_KEY) {
        return {
          query,
          results: [],
          error: 'XAI_API_KEY not configured',
          timestamp: new Date().toISOString(),
          source: 'xAI Live Search',
        };
      }

      // Use xAI's chat completions endpoint with live search parameters
      const response = await fetch('https://api.x.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.XAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'grok-3-latest',
          messages: [
            {
              role: 'user',
              content: `Search for: ${query}. Please provide relevant, up-to-date information and include specific details, sources, and links where possible.`,
            },
          ],
          search_parameters: {
            mode: 'on',
            max_search_results: maxResults,
            return_citations: true,
            sources: [
              { 
                type: 'web', 
                safe_search: safeSearch !== 'off'
              },
              { type: 'news' },
            ],
          },
          max_tokens: 1000,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('xAI Search API error:', response.status, errorText);
        
        if (response.status === 401) {
          throw new Error('xAI API key is invalid or expired');
        } else if (response.status === 429) {
          throw new Error('xAI Search rate limit exceeded');
        } else {
          throw new Error(`xAI Search failed with status ${response.status}`);
        }
      }

      const data = await response.json();
      
      // Extract the chat response and citations
      const content = data.choices?.[0]?.message?.content || '';
      const citations = data.citations || [];
      
      // Transform citations to results format
      const results = citations.map((citation: string, index: number) => {
        let hostname = 'Unknown';
        try {
          hostname = new URL(citation).hostname;
        } catch {
          // Invalid URL, use citation as is
        }
        
        return {
          title: `Source ${index + 1}`,
          url: citation,
          snippet: content, // The full response content
          source: hostname,
        };
      });

      return {
        query,
        results,
        content, // Include the full AI response
        citations,
        totalResults: citations.length,
        timestamp: new Date().toISOString(),
        source: 'xAI Live Search',
        usage: data.usage,
      };
    } catch (error) {
      console.error('xAI Search tool error:', error);
      
      // Return a graceful error response instead of throwing
      return {
        query,
        results: [],
        error: error instanceof Error ? error.message : 'Failed to search with xAI Live Search',
        timestamp: new Date().toISOString(),
        source: 'xAI Live Search',
      };
    }
  },
});