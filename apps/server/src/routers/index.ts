import {
  publicProcedure,
  createTRPCRouter,
} from "../lib/trpc";
import { benjiRouter } from "./benji";
import { userRouter } from "./user";
import { accountsRouter } from "./accounts";
import { mentionsRouter } from "./mentions";
import { twitterRouter } from "./twitter";

export const appRouter = createTRPCRouter({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  benji: benjiRouter,
  user: userRouter,
  accounts: accountsRouter,
  mentions: mentionsRouter,
  twitter: twitterRouter,
});

export type AppRouter = typeof appRouter;
