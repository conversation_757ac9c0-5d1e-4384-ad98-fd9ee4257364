'use client'

// Force dynamic rendering to avoid static generation issues
export const dynamic = 'force-dynamic'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  // Simple error logging without React hooks to avoid SSR issues
  if (typeof window !== 'undefined') {
    console.error('Page error:', error)
  }

  return (
    <div>
      <h2>Something went wrong!</h2>
      <p>An error occurred while loading this page.</p>
      <button onClick={() => reset()}>Try again</button>
    </div>
  )
}
