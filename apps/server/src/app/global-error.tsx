'use client'

import * as Sentry from '@sentry/nextjs'
import { useEffect } from 'react'

// Force dynamic rendering to avoid static generation issues
export const dynamic = 'force-dynamic'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Only capture exception on client side to avoid SSR issues
    if (typeof window !== 'undefined') {
      Sentry.captureException(error)
    }
  }, [error])

  return (
    <html>
      <body>
        <h2>Something went wrong!</h2>
        <p>Error ID: {error.digest}</p>
        <button onClick={() => reset()}>Try again</button>
      </body>
    </html>
  )
}