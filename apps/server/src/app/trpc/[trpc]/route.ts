import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { appRouter } from '@/routers';
import { createContext } from '@/lib/context';
import { NextRequest } from 'next/server';

// Force dynamic rendering for auth compatibility
export const dynamic = 'force-dynamic';

const corsHeaders = {
  'Access-Control-Allow-Origin': process.env.CORS_ORIGIN || 'http://localhost:3001',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-trpc-source',
  'Access-Control-Allow-Credentials': 'true',
};

export function OPTIONS() {
  return new Response(null, { status: 200, headers: corsHeaders });
}

function handler(req: NextRequest) {
  if (process.env.ENABLE_TRPC_REQUEST_LOGS === "true") {
    console.log('🔄 tRPC request received:', {
      method: req.method,
      url: req.url,
      origin: req.headers.get('origin'),
      userAgent: req.headers.get('user-agent')?.substring(0, 50)
    });
  }

  return fetchRequestHandler({
    endpoint: '/trpc',
    req,
    router: appRouter,
    createContext: () => createContext(req),
    responseMeta() {
      return {
        headers: corsHeaders,
      };
    },
  });
}

export { handler as GET, handler as POST };
