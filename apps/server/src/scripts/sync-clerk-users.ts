import { createClerkClient } from '@clerk/backend';
import { prisma } from '../lib/db-utils';

async function syncClerkUsers() {
  console.log('🔄 Starting Clerk user sync...');
  
  // Initialize Clerk client
  const secretKey = process.env.CLERK_SECRET_KEY;
  if (!secretKey) {
    throw new Error('CLERK_SECRET_KEY is not set in environment variables');
  }
  
  const clerkClient = createClerkClient({ secretKey });
  
  try {
    // Get default plan
    const defaultPlan = await prisma.subscriptionPlan.findFirst({
      where: { name: 'reply-guy' }
    });
    
    if (!defaultPlan) {
      console.error('❌ Default subscription plan "reply-guy" not found!');
      console.log('Creating default subscription plans...');
      
      // Create default plans if they don't exist
      // First create the reply-guy plan
      const replyGuyPlan = await prisma.subscriptionPlan.create({
        data: {
          name: 'reply-guy',
          displayName: 'Reply Guy',
          description: 'Basic plan for casual Twitter monitoring',
          price: 0,
          baseUsers: 1,
          isActive: true
        }
      });

      // Create features for reply-guy plan
      await prisma.planFeature.createMany({
        data: [
          { planId: replyGuyPlan.id, feature: 'MONITORED_ACCOUNTS', limit: 1 },
          { planId: replyGuyPlan.id, feature: 'AI_CALLS', limit: 10 },
          { planId: replyGuyPlan.id, feature: 'TEAM_MEMBERS', limit: 1 }
        ]
      });

      // Create reply-god plan
      const replyGodPlan = await prisma.subscriptionPlan.create({
        data: {
          name: 'reply-god',
          displayName: 'Reply God',
          description: 'Advanced plan for power users',
          price: 49,
          baseUsers: 1,
          isActive: true
        }
      });

      // Create features for reply-god plan
      await prisma.planFeature.createMany({
        data: [
          { planId: replyGodPlan.id, feature: 'MONITORED_ACCOUNTS', limit: 10 },
          { planId: replyGodPlan.id, feature: 'AI_CALLS', limit: 1000 },
          { planId: replyGodPlan.id, feature: 'TEAM_MEMBERS', limit: 1 }
        ]
      });

      // Create team plan
      const teamPlan = await prisma.subscriptionPlan.create({
        data: {
          name: 'team',
          displayName: 'Team',
          description: 'Enterprise plan for teams',
          price: 149,
          baseUsers: 10,
          additionalUserPrice: 15,
          isActive: true
        }
      });

      // Create features for team plan
      await prisma.planFeature.createMany({
        data: [
          { planId: teamPlan.id, feature: 'MONITORED_ACCOUNTS', limit: 50 },
          { planId: teamPlan.id, feature: 'AI_CALLS', limit: -1 }, // unlimited
          { planId: teamPlan.id, feature: 'TEAM_MEMBERS', limit: 10 }
        ]
      });

      const plans = { count: 3 };
      
      console.log(`✅ Created ${plans.count} subscription plans`);
    }
    
    // Get the default plan again
    const plan = await prisma.subscriptionPlan.findFirst({
      where: { name: 'reply-guy' }
    });
    
    if (!plan) {
      throw new Error('Failed to create default plan');
    }
    
    // Get all users from Clerk
    const clerkUsers = await clerkClient.users.getUserList({ limit: 100 });
    console.log(`📊 Found ${clerkUsers.data.length} users in Clerk`);
    
    // Get all existing users from database
    const existingUsers = await prisma.user.findMany({
      select: { id: true }
    });
    const existingUserIds = new Set(existingUsers.map(u => u.id));
    console.log(`📊 Found ${existingUsers.length} users in database`);
    
    // Sync missing users
    let syncedCount = 0;
    let skippedCount = 0;
    for (const clerkUser of clerkUsers.data) {
      if (!existingUserIds.has(clerkUser.id)) {
        const primaryEmail = clerkUser.emailAddresses.find(
          email => email.id === clerkUser.primaryEmailAddressId
        );
        
        try {
          // Check if email already exists
          if (primaryEmail?.emailAddress) {
            const existingUserWithEmail = await prisma.user.findUnique({
              where: { email: primaryEmail.emailAddress }
            });
            
            if (existingUserWithEmail) {
              console.log(`⚠️  Skipping user ${clerkUser.id} - email ${primaryEmail.emailAddress} already exists for user ${existingUserWithEmail.id}`);
              skippedCount++;
              continue;
            }
          }
          
          await prisma.user.create({
            data: {
              id: clerkUser.id,
              email: primaryEmail?.emailAddress || null,
              name: clerkUser.firstName && clerkUser.lastName 
                ? `${clerkUser.firstName} ${clerkUser.lastName}` 
                : clerkUser.firstName || clerkUser.username || null,
              avatar: clerkUser.imageUrl || null,
              planId: plan.id,
              lastActiveAt: new Date(),
            }
          });
          
          console.log(`✅ Synced user: ${clerkUser.id} (${primaryEmail?.emailAddress})`);
          syncedCount++;
        } catch (error: any) {
          if (error.code === 'P2002') {
            console.log(`⚠️  Skipping user ${clerkUser.id} - duplicate constraint`);
            skippedCount++;
          } else {
            throw error;
          }
        }
      }
    }
    
    console.log(`\n🎉 Sync complete! Synced ${syncedCount} new users, skipped ${skippedCount}.`);
    console.log(`📊 Total users in database: ${existingUsers.length + syncedCount}`);
    
  } catch (error) {
    console.error('❌ Error during sync:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the sync
syncClerkUsers().catch(console.error);