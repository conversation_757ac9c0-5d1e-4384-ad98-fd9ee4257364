/**
 * Unit tests for Database Utils
 * 
 * Tests database utility functions including rate limiting,
 * usage recording, and query optimizations
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { checkRateLimit, recordUsage, getCurrentBillingPeriod } from '@/lib/db-utils'
import { FeatureType } from '../../../prisma/generated'
import { testDb, seedTestData } from '../../helpers/db-helpers'
import { createTestUserInDb, createTestUsageLogInDb } from '../../helpers/test-data'

describe('Database Utils', () => {
  beforeEach(async () => {
    console.log('🧪 Setting up database utils test...')
    await seedTestData()
  })

  afterEach(async () => {
    console.log('🧹 Cleaning up database utils test...')
    await testDb.resetData()
  })

  describe('checkRateLimit', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = await createTestUserInDb('reply-guy', {
        id: 'rate-limit-user-123',
        email: '<EMAIL>',
        name: 'Rate Limit User',
      })
    })

    it('should allow usage when within limits', async () => {
      console.log('🔍 Testing checkRateLimit within limits...')
      
      // Create usage that's within limits (50 out of 100)
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 50 })

      const result = await checkRateLimit(testUser.id, FeatureType.AI_CALLS, 1)

      expect(result.allowed).toBe(true)
      expect(result.currentUsage).toBe(50)
      expect(result.limit).toBe(100)
      expect(result.remaining).toBe(50)
      
      console.log('✅ Rate limit check passed within limits')
    })

    it('should block usage when exceeding limits', async () => {
      console.log('🔍 Testing checkRateLimit exceeding limits...')
      
      // Create usage that's at the limit (100 out of 100)
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 100 })

      const result = await checkRateLimit(testUser.id, FeatureType.AI_CALLS, 1)

      expect(result.allowed).toBe(false)
      expect(result.currentUsage).toBe(100)
      expect(result.limit).toBe(100)
      expect(result.remaining).toBe(0)
      
      console.log('✅ Rate limit check correctly blocked when at limit')
    })

    it('should handle unlimited features', async () => {
      console.log('🔍 Testing checkRateLimit for unlimited feature...')
      
      // Create team plan user with unlimited team members
      const teamUser = await createTestUserInDb('team-plan', {
        id: 'team-unlimited-user-456',
        email: '<EMAIL>',
        name: 'Team Unlimited User',
      })

      const result = await checkRateLimit(teamUser.id, FeatureType.TEAM_MEMBERS, 100)

      expect(result.allowed).toBe(true)
      expect(result.currentUsage).toBe(0)
      expect(result.limit).toBe(-1)
      expect(result.remaining).toBe(-1)
      
      console.log('✅ Unlimited feature handled correctly')
    })

    it('should check requested amount against remaining limit', async () => {
      console.log('🔍 Testing checkRateLimit with requested amount...')
      
      // Create usage that leaves only 5 remaining (95 out of 100)
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 95 })

      // Request 3 - should be allowed
      const allowedResult = await checkRateLimit(testUser.id, FeatureType.AI_CALLS, 3)
      expect(allowedResult.allowed).toBe(true)

      // Request 10 - should be blocked
      const blockedResult = await checkRateLimit(testUser.id, FeatureType.AI_CALLS, 10)
      expect(blockedResult.allowed).toBe(false)
      
      console.log('✅ Requested amount correctly checked against remaining limit')
    })

    it('should throw error for non-existent user', async () => {
      console.log('🔍 Testing checkRateLimit for non-existent user...')
      
      try {
        await checkRateLimit('non-existent-user-789', FeatureType.AI_CALLS, 1)
        throw new Error('Should have thrown error for non-existent user')
      } catch (error: any) {
        expect(error.message).toBe('User not found')
        console.log('✅ Correctly threw error for non-existent user')
      }
    })

    it('should throw error for unconfigured feature', async () => {
      console.log('🔍 Testing checkRateLimit for unconfigured feature...')
      
      // Remove all features from user's plan
      await testDb.getClient().planFeature.deleteMany({
        where: { planId: testUser.planId }
      })

      try {
        await checkRateLimit(testUser.id, FeatureType.AI_CALLS, 1)
        throw new Error('Should have thrown error for unconfigured feature')
      } catch (error: any) {
        expect(error.message).toContain('Feature AI_CALLS not configured')
        console.log('✅ Correctly threw error for unconfigured feature')
      }
    })

    it('should only count current billing period usage', async () => {
      console.log('🔍 Testing checkRateLimit billing period filtering...')
      
      // Create usage for different billing period
      const lastMonth = new Date()
      lastMonth.setMonth(lastMonth.getMonth() - 1)
      const lastMonthPeriod = `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`
      
      await createTestUsageLogInDb(testUser.id, { 
        feature: 'AI_CALLS', 
        amount: 90,
        billingPeriod: lastMonthPeriod 
      })
      
      // Create current month usage
      await createTestUsageLogInDb(testUser.id, { feature: 'AI_CALLS', amount: 10 })

      const result = await checkRateLimit(testUser.id, FeatureType.AI_CALLS, 1)

      // Should only count current month usage (10), not last month (90)
      expect(result.currentUsage).toBe(10)
      expect(result.allowed).toBe(true)
      
      console.log('✅ Usage correctly filtered by billing period')
    })
  })

  describe('recordUsage', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = await createTestUserInDb('reply-guy', {
        id: 'record-usage-user-101',
        email: '<EMAIL>',
        name: 'Record Usage User',
      })
    })

    it('should record usage successfully', async () => {
      console.log('🔍 Testing recordUsage...')
      
      const metadata = { source: 'test', requestId: 'req-456' }
      
      await recordUsage(testUser.id, FeatureType.AI_CALLS, 3, metadata)

      // Verify the usage was recorded
      const usageLog = await testDb.getClient().usageLog.findFirst({
        where: {
          userId: testUser.id,
          feature: FeatureType.AI_CALLS,
        },
        orderBy: { createdAt: 'desc' },
      })

      expect(usageLog).toBeDefined()
      expect(usageLog!.userId).toBe(testUser.id)
      expect(usageLog!.feature).toBe(FeatureType.AI_CALLS)
      expect(usageLog!.amount).toBe(3)
      expect(usageLog!.metadata).toEqual(metadata)
      
      console.log('✅ Usage recorded successfully')
    })

    it('should default amount to 1 when not provided', async () => {
      console.log('🔍 Testing recordUsage with default amount...')
      
      await recordUsage(testUser.id, FeatureType.IMAGE_GENERATIONS)

      const usageLog = await testDb.getClient().usageLog.findFirst({
        where: {
          userId: testUser.id,
          feature: FeatureType.IMAGE_GENERATIONS,
        },
      })

      expect(usageLog!.amount).toBe(1)
      
      console.log('✅ Default amount applied correctly')
    })

    it('should set correct billing period', async () => {
      console.log('🔍 Testing recordUsage billing period...')
      
      await recordUsage(testUser.id, FeatureType.MONITORED_ACCOUNTS, 2)

      const usageLog = await testDb.getClient().usageLog.findFirst({
        where: {
          userId: testUser.id,
          feature: FeatureType.MONITORED_ACCOUNTS,
        },
      })

      const now = new Date()
      const expectedPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
      
      expect(usageLog!.billingPeriod).toBe(expectedPeriod)
      
      console.log(`✅ Billing period set correctly: ${expectedPeriod}`)
    })

    it('should handle empty metadata', async () => {
      console.log('🔍 Testing recordUsage with empty metadata...')
      
      await recordUsage(testUser.id, FeatureType.STORAGE_GB, 1, {})

      const usageLog = await testDb.getClient().usageLog.findFirst({
        where: {
          userId: testUser.id,
          feature: FeatureType.STORAGE_GB,
        },
      })

      expect(usageLog!.metadata).toEqual({})
      
      console.log('✅ Empty metadata handled correctly')
    })

    it('should handle complex metadata', async () => {
      console.log('🔍 Testing recordUsage with complex metadata...')
      
      const complexMetadata = {
        endpoint: '/benji/generateResponse',
        model: 'gemini25Flash',
        tokensUsed: 250,
        processingTime: 3500,
        confidence: 0.87,
        tools: ['web-search', 'knowledge-search'],
        userAgent: 'BuddyChip/1.0',
        ipAddress: '***********',
      }

      await recordUsage(testUser.id, FeatureType.AI_CALLS, 1, complexMetadata)

      const usageLog = await testDb.getClient().usageLog.findFirst({
        where: {
          userId: testUser.id,
          feature: FeatureType.AI_CALLS,
        },
        orderBy: { createdAt: 'desc' },
      })

      expect(usageLog!.metadata).toEqual(complexMetadata)
      
      console.log('✅ Complex metadata stored correctly')
    })
  })

  describe('getCurrentBillingPeriod', () => {
    it('should return current billing period in correct format', async () => {
      console.log('🔍 Testing getCurrentBillingPeriod...')
      
      const billingPeriod = getCurrentBillingPeriod()
      
      const now = new Date()
      const expectedPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
      
      expect(billingPeriod).toBe(expectedPeriod)
      expect(billingPeriod).toMatch(/^\d{4}-\d{2}$/)
      
      console.log(`✅ Billing period format correct: ${billingPeriod}`)
    })

    it('should handle month transitions correctly', async () => {
      console.log('🔍 Testing getCurrentBillingPeriod month transitions...')
      
      // Test with different months
      const testDates = [
        new Date('2024-01-15'), // January
        new Date('2024-12-31'), // December
        new Date('2024-02-29'), // Leap year February
      ]

      testDates.forEach(date => {
        // Mock the current date
        const originalDate = Date
        global.Date = class extends Date {
          constructor() {
            super()
            return date
          }
          static now() {
            return date.getTime()
          }
        } as any

        const billingPeriod = getCurrentBillingPeriod()
        const expectedPeriod = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        
        expect(billingPeriod).toBe(expectedPeriod)
        
        // Restore original Date
        global.Date = originalDate
      })
      
      console.log('✅ Month transitions handled correctly')
    })
  })
})
