/**
 * Unit tests for main router (index.ts)
 * 
 * Tests the health check endpoint and router structure
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { createTestCaller } from '../../helpers/trpc-test-client'
import { trpcTestUtils } from '../../helpers/trpc-test-client'

describe('Main Router', () => {
  let caller: ReturnType<typeof createTestCaller>

  beforeEach(() => {
    console.log('🧪 Setting up main router test...')
    caller = createTestCaller()
  })

  describe('healthCheck', () => {
    it('should return "OK" for health check', async () => {
      console.log('🔍 Testing health check endpoint...')
      
      const result = await trpcTestUtils.expectSuccess(
        caller.healthCheck(),
        (result) => {
          expect(result).toBe('OK')
          console.log('✅ Health check returned expected "OK" response')
          return true
        }
      )

      expect(result).toBe('OK')
    })

    it('should be accessible without authentication', async () => {
      console.log('🔍 Testing health check without authentication...')
      
      // Create unauthenticated caller
      const unauthenticatedCaller = createTestCaller()
      
      const result = await trpcTestUtils.expectSuccess(
        unauthenticatedCaller.healthCheck(),
        (result) => {
          expect(result).toBe('OK')
          console.log('✅ Health check accessible without authentication')
          return true
        }
      )

      expect(result).toBe('OK')
    })

    it('should have consistent response format', async () => {
      console.log('🔍 Testing health check response format consistency...')
      
      // Call multiple times to ensure consistency
      const results = await Promise.all([
        caller.healthCheck(),
        caller.healthCheck(),
        caller.healthCheck(),
      ])

      results.forEach((result, index) => {
        expect(result).toBe('OK')
        expect(typeof result).toBe('string')
        console.log(`✅ Health check call ${index + 1} returned consistent format`)
      })
    })

    it('should respond quickly', async () => {
      console.log('🔍 Testing health check response time...')
      
      const startTime = Date.now()
      await caller.healthCheck()
      const endTime = Date.now()
      
      const responseTime = endTime - startTime
      
      // Health check should respond within 100ms
      expect(responseTime).toBeLessThan(100)
      console.log(`✅ Health check responded in ${responseTime}ms (under 100ms threshold)`)
    })
  })

  describe('Router Structure', () => {
    it('should have all expected routers mounted', async () => {
      console.log('🔍 Testing router structure...')
      
      // Test that all expected endpoints exist by checking they don't throw "procedure not found" errors
      expect(() => caller.healthCheck).not.toThrow()
      expect(() => caller.user).toBeDefined()
      expect(() => caller.benji).toBeDefined()
      
      console.log('✅ All expected routers are properly mounted')
    })

    it('should handle invalid procedure calls gracefully', async () => {
      console.log('🔍 Testing invalid procedure handling...')
      
      try {
        // @ts-expect-error - Testing invalid procedure
        await caller.nonExistentProcedure()
        throw new Error('Should have thrown an error for non-existent procedure')
      } catch (error: any) {
        // Should throw a tRPC error about procedure not found
        expect(error.message).toContain('No "query"-procedure on path "nonExistentProcedure"')
        console.log('✅ Invalid procedure calls handled gracefully')
      }
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed requests gracefully', async () => {
      console.log('🔍 Testing malformed request handling...')
      
      // This test ensures the router can handle edge cases
      // The health check should still work even with unusual context
      const result = await caller.healthCheck()
      expect(result).toBe('OK')
      
      console.log('✅ Router handles requests gracefully')
    })

    it('should maintain consistent error format', async () => {
      console.log('🔍 Testing error format consistency...')
      
      try {
        // @ts-expect-error - Testing invalid procedure
        await caller.invalidProcedure()
      } catch (error: any) {
        // tRPC errors should have consistent structure
        expect(error).toHaveProperty('code')
        expect(error).toHaveProperty('message')
        console.log('✅ Error format is consistent')
      }
    })
  })
})
