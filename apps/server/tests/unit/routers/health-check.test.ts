/**
 * Simple health check test without database dependencies
 */

import { describe, it, expect } from 'vitest'
import { appRouter } from '@/routers'

describe('Health Check', () => {
  it('should return "OK" for health check', async () => {
    console.log('🔍 Testing health check endpoint...')
    
    // Create a simple context for the health check
    const ctx = {
      userId: null,
      prisma: null as any,
      req: {} as any,
    }
    
    // Create caller with the context
    const caller = appRouter.createCaller(ctx)
    
    // Test health check
    const result = await caller.healthCheck()
    
    expect(result).toBe('OK')
    console.log('✅ Health check returned "OK"')
  })
})
