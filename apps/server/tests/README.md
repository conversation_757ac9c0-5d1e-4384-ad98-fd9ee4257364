# BuddyChip API Testing Documentation

## Overview

This directory contains comprehensive unit and integration tests for all BuddyChip API endpoints. The test suite covers authentication, authorization, rate limiting, database operations, and external service integrations.

## Test Structure

```
tests/
├── setup.ts                    # Global test setup and teardown
├── helpers/                    # Test utilities and helpers
│   ├── db-helpers.ts           # Database setup and seeding utilities
│   ├── mock-auth.ts            # Authentication mocking utilities
│   ├── test-data.ts            # Test data factories and utilities
│   └── trpc-test-client.ts     # tRPC test client and validators
├── unit/                       # Unit tests for individual components
│   ├── routers/                # Router-level unit tests
│   │   ├── index.test.ts       # Health check and main router tests
│   │   ├── user.test.ts        # User router tests
│   │   └── benji.test.ts       # Benji AI router tests
│   └── lib/                    # Service layer unit tests
│       ├── user-service.test.ts # User service function tests
│       └── db-utils.test.ts    # Database utility function tests
└── integration/                # Integration tests for full workflows
    └── api/                    # API endpoint integration tests
        ├── user-endpoints.test.ts    # User API integration tests
        ├── benji-endpoints.test.ts   # Benji API integration tests
        └── auth-flows.test.ts        # Authentication flow tests
```

## Test Coverage

### API Endpoints Tested

#### Health Check
- ✅ Basic health check functionality
- ✅ Response format consistency
- ✅ Performance requirements

#### User Router (`/user`)
- ✅ `getProfile` - User profile retrieval with plan data
- ✅ `getUsage` - Current usage for all features
- ✅ `canUseFeature` - Feature availability checking
- ✅ `logFeatureUsage` - Usage logging and tracking

#### Benji Router (`/benji`)
- ✅ `generateMentionResponse` - AI response generation for mentions
- ✅ `generateQuickReply` - Quick reply generation for tweets
- ✅ `getUsageStats` - AI usage statistics retrieval
- ✅ `getCapabilities` - Available models and tools

### Test Categories

#### Unit Tests
- **Router Tests**: Individual tRPC procedure testing
- **Service Tests**: Business logic and data processing
- **Utility Tests**: Helper functions and database operations

#### Integration Tests
- **API Workflows**: Complete request/response cycles
- **Authentication Flows**: User authentication and authorization
- **Database Operations**: Data persistence and retrieval
- **Rate Limiting**: Usage tracking and limit enforcement

#### Test Scenarios
- **Happy Path**: Valid inputs and successful responses
- **Error Handling**: Invalid inputs and error responses
- **Edge Cases**: Boundary values and unusual conditions
- **Authentication**: Protected and public endpoint access
- **Authorization**: Plan-based feature access control

## Running Tests

### Prerequisites

1. **Database Setup**: Ensure PostgreSQL is running with a test database
2. **Environment**: Copy `.env.test` and configure test database URL
3. **Dependencies**: Install test dependencies with `npm install`

### Test Commands

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage

# Run only unit tests
npm run test -- tests/unit

# Run only integration tests
npm run test -- tests/integration

# Run specific test file
npm run test -- tests/unit/routers/user.test.ts

# Run tests matching pattern
npm run test -- --grep "authentication"
```

### Database Setup for Tests

```bash
# Setup test database
npm run test:db:setup

# Reset test database
npm run db:reset

# Generate Prisma client
npm run db:generate
```

## Test Configuration

### Environment Variables

The test suite uses `.env.test` for configuration:

- `TEST_DATABASE_URL`: Test database connection string
- `NODE_ENV=test`: Ensures test environment
- Mock API keys for external services
- Test-specific configuration flags

### Vitest Configuration

Key configuration in `vitest.config.ts`:

- **Environment**: Node.js environment for server-side testing
- **Setup Files**: Global setup and teardown
- **Test Timeout**: 30 seconds for integration tests
- **Coverage**: V8 provider with HTML reports
- **Single Thread**: Prevents database conflicts

## Test Utilities

### Database Helpers (`db-helpers.ts`)

```typescript
// Initialize test database
await testDb.initialize()

// Reset data between tests
await testDb.resetData()

// Seed test data
await seedTestData()

// Create test user
const user = await createTestUserInDb('reply-guy', userData)
```

### Authentication Mocking (`mock-auth.ts`)

```typescript
// Mock authenticated user
mockAuthenticatedUser(mockUser)

// Mock unauthenticated state
mockUnauthenticatedUser()

// Create test context
const context = createTestContext(user)
```

### tRPC Test Client (`trpc-test-client.ts`)

```typescript
// Create authenticated caller
const caller = createAuthenticatedTestCaller(mockUser)

// Test successful operation
await trpcTestUtils.expectSuccess(
  caller.user.getProfile(),
  (result) => {
    expect(result.id).toBe(userId)
    return true
  }
)

// Test error handling
await trpcTestUtils.expectUnauthorized(
  caller.user.getProfile()
)
```

### Test Data Factories (`test-data.ts`)

```typescript
// Create test user data
const userData = createTestUserData({ planName: 'reply-guy' })

// Create test mention
const mention = await createTestMentionInDb(mentionData)

// Create test usage log
const usage = await createTestUsageLogInDb(userId, usageData)
```

## Writing New Tests

### Test Structure Template

```typescript
describe('Feature Name', () => {
  let testUser: any
  let mockUser: MockUser
  let caller: ReturnType<typeof createAuthenticatedTestCaller>

  beforeEach(async () => {
    console.log('🧪 Setting up test...')
    testUser = await createTestUserInDb('reply-guy')
    mockUser = { id: testUser.id, email: testUser.email!, name: testUser.name! }
    caller = createAuthenticatedTestCaller(mockUser)
  })

  afterEach(async () => {
    console.log('🧹 Cleaning up test...')
    await testDb.resetData()
  })

  describe('endpoint name', () => {
    it('should handle happy path scenario', async () => {
      console.log('🔍 Testing happy path...')
      
      const result = await trpcTestUtils.expectSuccess(
        caller.endpoint.method(validInput),
        (result) => {
          expect(result).toHaveProperty('expectedField')
          console.log('✅ Happy path successful')
          return true
        }
      )
    })

    it('should handle error scenario', async () => {
      console.log('🔍 Testing error scenario...')
      
      await trpcTestUtils.expectTRPCError(
        caller.endpoint.method(invalidInput),
        'EXPECTED_ERROR_CODE'
      )
    })
  })
})
```

### Best Practices

1. **Descriptive Test Names**: Use clear, descriptive test names that explain the scenario
2. **Console Logging**: Add console logs for debugging difficult test failures
3. **Data Isolation**: Reset test data between tests to ensure independence
4. **Error Testing**: Test both success and failure scenarios
5. **Edge Cases**: Include boundary conditions and unusual inputs
6. **Authentication**: Test both authenticated and unauthenticated access
7. **Plan-Based Testing**: Test different subscription plan behaviors

### Common Patterns

```typescript
// Test authentication requirement
await trpcTestUtils.expectUnauthorized(
  unauthenticatedCaller.protectedEndpoint()
)

// Test input validation
await trpcTestUtils.expectTRPCError(
  caller.endpoint({ invalidField: 'value' }),
  'BAD_REQUEST'
)

// Test rate limiting
const { checkRateLimit } = require('@/lib/db-utils')
checkRateLimit.mockResolvedValueOnce({ allowed: false })
await trpcTestUtils.expectTRPCError(
  caller.endpoint(input),
  'TOO_MANY_REQUESTS'
)

// Test database operations
const dbRecord = await testDb.getClient().model.findUnique({
  where: { id: result.id }
})
expect(dbRecord).toBeDefined()
```

## Debugging Tests

### Common Issues

1. **Database Connection**: Ensure test database is running and accessible
2. **Authentication Mocks**: Verify mock authentication is properly set up
3. **Data Conflicts**: Check that test data is properly reset between tests
4. **Async Operations**: Ensure all async operations are properly awaited
5. **Mock Cleanup**: Verify mocks are cleared between tests

### Debug Commands

```bash
# Run single test with verbose output
npm run test -- --reporter=verbose tests/unit/routers/user.test.ts

# Run tests with debug logging
DEBUG=* npm run test

# Run tests with coverage to identify untested code
npm run test:coverage
```

### Console Logging

The test suite includes extensive console logging for debugging:

- 🧪 Test setup messages
- 🔍 Test execution steps
- ✅ Success confirmations
- 🧹 Cleanup messages
- ❌ Error details

## Continuous Integration

### GitHub Actions

The test suite is designed to run in CI environments:

```yaml
- name: Run Tests
  run: |
    npm run test:setup
    npm run test:run
  env:
    DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
    NODE_ENV: test
```

### Test Database

For CI environments, use a separate test database or in-memory database to avoid conflicts with development data.

## Contributing

When adding new features:

1. **Write Tests First**: Follow TDD principles
2. **Cover All Scenarios**: Include happy path, errors, and edge cases
3. **Update Documentation**: Add new endpoints to this README
4. **Run Full Suite**: Ensure all existing tests still pass
5. **Check Coverage**: Maintain high test coverage

## Troubleshooting

### Common Error Messages

- **"User not found"**: Check that test user is properly created
- **"UNAUTHORIZED"**: Verify authentication mocks are set up
- **"Database connection failed"**: Check test database configuration
- **"Procedure not found"**: Verify tRPC router imports and setup

### Getting Help

1. Check console logs for detailed error messages
2. Run tests with `--reporter=verbose` for more details
3. Verify test database setup and connectivity
4. Check that all mocks are properly configured
5. Ensure test data is properly seeded and reset
