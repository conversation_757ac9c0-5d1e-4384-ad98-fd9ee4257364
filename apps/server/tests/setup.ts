/**
 * Global test setup for BuddyChip API tests
 * 
 * This file runs before all tests and sets up the testing environment
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { testDb, cleanupTestDb, seedTestData } from './helpers/db-helpers'
import { setupMockAuth, cleanupMockAuth } from './helpers/mock-auth'
import '@testing-library/jest-dom'

// Global test database instance
let testDbInstance: any

beforeAll(async () => {
  console.log('🧪 Setting up test environment...')
  
  // Initialize test database
  testDbInstance = await testDb.initialize()
  console.log('✅ Test database initialized')
  
  // Setup authentication mocks
  setupMockAuth()
  console.log('✅ Authentication mocks configured')
  
  // Seed initial test data
  await seedTestData()
  console.log('✅ Test data seeded')
  
  console.log('🚀 Test environment ready!')
})

afterAll(async () => {
  console.log('🧹 Cleaning up test environment...')
  
  // Cleanup authentication mocks
  cleanupMockAuth()
  console.log('✅ Authentication mocks cleaned up')
  
  // Cleanup test database
  await cleanupTestDb()
  console.log('✅ Test database cleaned up')
  
  // Disconnect from database
  if (testDbInstance) {
    await testDbInstance.disconnect()
    console.log('✅ Database connection closed')
  }
  
  console.log('✨ Test environment cleaned up!')
})

beforeEach(async () => {
  // Clean up data between tests but keep schema and seed data
  await testDb.resetData()
  console.log('🔄 Test data reset for new test')
})

afterEach(async () => {
  // Additional cleanup if needed per test
  console.log('✅ Test completed')
})

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})
