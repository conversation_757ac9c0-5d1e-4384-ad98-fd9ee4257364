/**
 * Test data factories and utilities
 * 
 * Provides functions to create consistent test data
 */

import { randomUUID } from 'crypto'
import { testPrisma } from './db-helpers'
import type { User, SubscriptionPlan, PlanFeature, MonitoredAccount, Mention, AIResponse, FeatureType } from '../../prisma/generated'

/**
 * Test user factory
 */
export function createTestUserData(overrides?: Partial<{
  id: string
  email: string
  name: string
  avatar: string
  planName: string
}>) {
  const id = overrides?.id || `test-user-${randomUUID()}`
  
  return {
    id,
    email: overrides?.email || `test-${id}@example.com`,
    name: overrides?.name || `Test User ${id.slice(-8)}`,
    avatar: overrides?.avatar || null,
    planName: overrides?.planName || 'reply-guy',
  }
}

/**
 * Test monitored account factory
 */
export function createTestMonitoredAccountData(userId: string, overrides?: Partial<{
  twitterHandle: string
  twitterId: string
  displayName: string
  avatarUrl: string
  isActive: boolean
}>) {
  const handle = overrides?.twitterHandle || `testuser${Date.now()}`
  
  return {
    twitterHandle: handle,
    twitterId: overrides?.twitterId || `${Math.floor(Math.random() * **********)}`,
    displayName: overrides?.displayName || `Test User @${handle}`,
    avatarUrl: overrides?.avatarUrl || `https://example.com/avatar/${handle}.jpg`,
    isActive: overrides?.isActive ?? true,
    userId,
  }
}

/**
 * Test mention factory
 */
export function createTestMentionData(overrides?: Partial<{
  id: string
  content: string
  link: string
  authorName: string
  authorHandle: string
  authorId: string
  authorAvatarUrl: string
  authorVerified: boolean
  mentionedAt: Date
  replyCount: number
  retweetCount: number
  likeCount: number
  isReply: boolean
  parentTweetId: string
  bullishScore: number
  keywords: string[]
  accountId: string
  userId: string
  processed: boolean
}>) {
  const tweetId = overrides?.id || `${Date.now()}${Math.floor(Math.random() * 1000)}`
  const authorHandle = overrides?.authorHandle || `testauthor${Date.now()}`
  
  return {
    id: tweetId,
    content: overrides?.content || `This is a test tweet mentioning something interesting! #crypto #test`,
    link: overrides?.link || `https://twitter.com/${authorHandle}/status/${tweetId}`,
    authorName: overrides?.authorName || `Test Author @${authorHandle}`,
    authorHandle: authorHandle,
    authorId: overrides?.authorId || `${Math.floor(Math.random() * **********)}`,
    authorAvatarUrl: overrides?.authorAvatarUrl || `https://example.com/avatar/${authorHandle}.jpg`,
    authorVerified: overrides?.authorVerified ?? false,
    mentionedAt: overrides?.mentionedAt || new Date(),
    replyCount: overrides?.replyCount ?? Math.floor(Math.random() * 10),
    retweetCount: overrides?.retweetCount ?? Math.floor(Math.random() * 50),
    likeCount: overrides?.likeCount ?? Math.floor(Math.random() * 100),
    isReply: overrides?.isReply ?? false,
    parentTweetId: overrides?.parentTweetId || null,
    bullishScore: overrides?.bullishScore ?? Math.floor(Math.random() * 100) + 1,
    keywords: overrides?.keywords || ['crypto', 'test', 'mention'],
    accountId: overrides?.accountId || null,
    userId: overrides?.userId || null,
    processed: overrides?.processed ?? false,
  }
}

/**
 * Test AI response factory
 */
export function createTestAIResponseData(mentionId: string, userId?: string, overrides?: Partial<{
  content: string
  model: string
  prompt: string
  tokensUsed: number
  confidence: number
  rating: number
  used: boolean
  processingTime: number
  version: string
}>) {
  return {
    content: overrides?.content || `This is a great point! Here's my thoughtful response about the topic. What do you think?`,
    model: overrides?.model || 'gemini25Flash',
    prompt: overrides?.prompt || 'Generate a helpful response to this mention',
    tokensUsed: overrides?.tokensUsed ?? Math.floor(Math.random() * 500) + 100,
    confidence: overrides?.confidence ?? Math.random() * 0.5 + 0.5, // 0.5-1.0
    rating: overrides?.rating ?? null,
    used: overrides?.used ?? false,
    processingTime: overrides?.processingTime ?? Math.floor(Math.random() * 5000) + 1000,
    version: overrides?.version || '1.0.0',
    mentionId,
    userId: userId || null,
  }
}

/**
 * Test usage log factory
 */
export function createTestUsageLogData(userId: string, overrides?: Partial<{
  feature: FeatureType
  amount: number
  metadata: any
  billingPeriod: string
}>) {
  const now = new Date()
  const billingPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  
  return {
    userId,
    feature: overrides?.feature || 'AI_CALLS' as FeatureType,
    amount: overrides?.amount ?? 1,
    metadata: overrides?.metadata || { source: 'test' },
    billingPeriod: overrides?.billingPeriod || billingPeriod,
  }
}

/**
 * Create a complete test user with plan in database
 */
export async function createTestUserInDb(planName: string = 'reply-guy', userData?: {
  id?: string
  email?: string
  name?: string
  avatar?: string
}) {
  const plan = await testPrisma.subscriptionPlan.findFirst({
    where: { name: planName },
    include: { features: true },
  })

  if (!plan) {
    throw new Error(`Test plan '${planName}' not found`)
  }

  const testUserData = createTestUserData({
    ...userData,
    planName,
  })

  const user = await testPrisma.user.create({
    data: {
      id: testUserData.id,
      email: testUserData.email,
      name: testUserData.name,
      avatar: testUserData.avatar,
      planId: plan.id,
      lastActiveAt: new Date(),
    },
    include: {
      plan: {
        include: {
          features: true,
        },
      },
    },
  })

  console.log(`✅ Created test user in DB: ${user.id} with plan: ${planName}`)
  return user
}

/**
 * Create test monitored account in database
 */
export async function createTestMonitoredAccountInDb(userId: string, accountData?: Partial<{
  twitterHandle: string
  twitterId: string
  displayName: string
  avatarUrl: string
  isActive: boolean
}>) {
  const data = createTestMonitoredAccountData(userId, accountData)
  
  const account = await testPrisma.monitoredAccount.create({
    data,
  })

  console.log(`✅ Created test monitored account: ${account.twitterHandle}`)
  return account
}

/**
 * Create test mention in database
 */
export async function createTestMentionInDb(mentionData?: Partial<{
  id: string
  content: string
  link: string
  authorName: string
  authorHandle: string
  accountId: string
  userId: string
  processed: boolean
}>) {
  const data = createTestMentionData(mentionData)
  
  const mention = await testPrisma.mention.create({
    data,
  })

  console.log(`✅ Created test mention: ${mention.id}`)
  return mention
}

/**
 * Create test AI response in database
 */
export async function createTestAIResponseInDb(mentionId: string, userId?: string, responseData?: Partial<{
  content: string
  model: string
  used: boolean
  rating: number
}>) {
  const data = createTestAIResponseData(mentionId, userId, responseData)
  
  const response = await testPrisma.aIResponse.create({
    data,
  })

  console.log(`✅ Created test AI response: ${response.id}`)
  return response
}

/**
 * Create test usage log in database
 */
export async function createTestUsageLogInDb(userId: string, usageData?: Partial<{
  feature: FeatureType
  amount: number
  billingPeriod: string
}>) {
  const data = createTestUsageLogData(userId, usageData)
  
  const usageLog = await testPrisma.usageLog.create({
    data,
  })

  console.log(`✅ Created test usage log: ${usageLog.id}`)
  return usageLog
}
