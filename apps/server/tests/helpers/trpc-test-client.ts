/**
 * tRPC test client utilities
 * 
 * Provides utilities to create tRPC clients for testing
 */

import { createTRPCMsw } from 'msw-trpc'
import { appRouter } from '@/routers'
import type { AppRouter } from '@/routers'
import { createTestContext, createUnauthenticatedTestContext } from './mock-auth'
import { testPrisma } from './db-helpers'
import type { MockUser } from './mock-auth'

/**
 * Create a tRPC caller for testing with authenticated context
 */
export function createTestCaller(user?: MockUser) {
  const ctx = user ? createTestContext(user) : createUnauthenticatedTestContext()
  
  // Create the tRPC caller with test context
  const caller = appRouter.createCaller(ctx)
  
  console.log(`🔧 Created tRPC test caller for user: ${user?.id || 'unauthenticated'}`)
  
  return caller
}

/**
 * Create authenticated test caller with user data
 */
export function createAuthenticatedTestCaller(user: MockU<PERSON>) {
  return createTestCaller(user)
}

/**
 * Create unauthenticated test caller
 */
export function createUnauthenticatedTestCaller() {
  return createTestCaller()
}

/**
 * Test utilities for common tRPC testing patterns
 */
export const trpcTestUtils = {
  /**
   * Test that a procedure throws UNAUTHORIZED error
   */
  async expectUnauthorized(procedureCall: Promise<any>) {
    try {
      await procedureCall
      throw new Error('Expected UNAUTHORIZED error but procedure succeeded')
    } catch (error: any) {
      if (error.code !== 'UNAUTHORIZED') {
        console.error('Expected UNAUTHORIZED but got:', error)
        throw new Error(`Expected UNAUTHORIZED error but got: ${error.code || error.message}`)
      }
      console.log('✅ Correctly threw UNAUTHORIZED error')
    }
  },

  /**
   * Test that a procedure throws a specific tRPC error
   */
  async expectTRPCError(procedureCall: Promise<any>, expectedCode: string) {
    try {
      await procedureCall
      throw new Error(`Expected ${expectedCode} error but procedure succeeded`)
    } catch (error: any) {
      if (error.code !== expectedCode) {
        console.error(`Expected ${expectedCode} but got:`, error)
        throw new Error(`Expected ${expectedCode} error but got: ${error.code || error.message}`)
      }
      console.log(`✅ Correctly threw ${expectedCode} error`)
    }
  },

  /**
   * Test that a procedure succeeds and returns expected data
   */
  async expectSuccess<T>(procedureCall: Promise<T>, validator?: (result: T) => boolean | void) {
    try {
      const result = await procedureCall
      console.log('✅ Procedure succeeded')
      
      if (validator) {
        const isValid = validator(result)
        if (isValid === false) {
          throw new Error('Result validation failed')
        }
        console.log('✅ Result validation passed')
      }
      
      return result
    } catch (error: any) {
      console.error('Procedure failed unexpectedly:', error)
      throw error
    }
  },

  /**
   * Create test context with database transaction
   */
  async withTransaction<T>(callback: (prisma: typeof testPrisma) => Promise<T>): Promise<T> {
    return testPrisma.$transaction(async (tx) => {
      return callback(tx as typeof testPrisma)
    })
  },
}

/**
 * Mock external services for testing
 */
export const mockExternalServices = {
  /**
   * Mock AI service responses
   */
  mockAIResponse: {
    success: {
      content: 'This is a mocked AI response for testing purposes.',
      model: 'gemini25Flash',
      tokensUsed: 150,
      confidence: 0.85,
    },
    error: new Error('AI service temporarily unavailable'),
  },

  /**
   * Mock rate limiting responses
   */
  mockRateLimit: {
    allowed: {
      allowed: true,
      currentUsage: 5,
      limit: 100,
      remaining: 95,
    },
    exceeded: {
      allowed: false,
      currentUsage: 100,
      limit: 100,
      remaining: 0,
    },
  },

  /**
   * Mock Clerk user data
   */
  mockClerkUser: {
    id: 'test-clerk-user-123',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
    firstName: 'Test',
    lastName: 'User',
    imageUrl: 'https://example.com/avatar.jpg',
  },
}

/**
 * Test data validation helpers
 */
export const testValidators = {
  /**
   * Validate user profile response
   */
  validateUserProfile: (profile: any) => {
    if (!profile.id || !profile.email || !profile.plan) {
      throw new Error('Invalid user profile structure')
    }
    if (!profile.plan.name || !profile.plan.features) {
      throw new Error('Invalid user plan structure')
    }
    console.log('✅ User profile structure is valid')
    return true
  },

  /**
   * Validate usage response
   */
  validateUsageResponse: (usage: any) => {
    if (!Array.isArray(usage)) {
      throw new Error('Usage response should be an array')
    }
    for (const item of usage) {
      if (!item.feature || typeof item.currentUsage !== 'number' || typeof item.limit !== 'number') {
        throw new Error('Invalid usage item structure')
      }
    }
    console.log('✅ Usage response structure is valid')
    return true
  },

  /**
   * Validate AI response
   */
  validateAIResponse: (response: any) => {
    if (!response.content || !response.model) {
      throw new Error('Invalid AI response structure')
    }
    if (typeof response.tokensUsed !== 'number' || response.tokensUsed <= 0) {
      throw new Error('Invalid token usage in AI response')
    }
    console.log('✅ AI response structure is valid')
    return true
  },

  /**
   * Validate capabilities response
   */
  validateCapabilities: (capabilities: any) => {
    if (!capabilities.plan || !Array.isArray(capabilities.models) || !Array.isArray(capabilities.tools)) {
      throw new Error('Invalid capabilities structure')
    }
    console.log('✅ Capabilities structure is valid')
    return true
  },
}
