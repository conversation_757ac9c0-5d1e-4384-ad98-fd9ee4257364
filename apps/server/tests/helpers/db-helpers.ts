/**
 * Database helpers for testing
 * 
 * Provides utilities for setting up, seeding, and cleaning test database
 */

import { PrismaClient } from '../../prisma/generated'
import { execSync } from 'child_process'
import { randomUUID } from 'crypto'

// Test database URL - uses a separate test database
const TEST_DATABASE_URL = process.env.TEST_DATABASE_URL || 
  process.env.DATABASE_URL?.replace('/buddychip', '/buddychip_test') ||
  'postgresql://user:password@localhost:5432/buddychip_test'

console.log('🔧 Test Database URL:', TEST_DATABASE_URL.replace(/\/\/.*@/, '//***:***@'))

// Test Prisma client instance
export const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: TEST_DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'test' ? [] : ['error'],
})

export const testDb = {
  /**
   * Initialize test database
   */
  async initialize() {
    try {
      console.log('🔧 Initializing test database...')
      
      // Run migrations on test database
      execSync(`DATABASE_URL="${TEST_DATABASE_URL}" DIRECT_URL="${TEST_DATABASE_URL}" npx prisma migrate deploy --schema ./prisma/schema/schema.prisma`, {
        stdio: 'inherit',
        cwd: process.cwd(),
      })
      
      console.log('✅ Test database migrations completed')
      return testPrisma
    } catch (error) {
      console.error('❌ Failed to initialize test database:', error)
      throw error
    }
  },

  /**
   * Reset data between tests (keeps schema, removes data)
   */
  async resetData() {
    try {
      console.log('🔄 Resetting test data...')
      
      // Delete all data in reverse dependency order
      await testPrisma.usageLog.deleteMany()
      await testPrisma.image.deleteMany()
      await testPrisma.aIResponse.deleteMany()
      await testPrisma.mention.deleteMany()
      await testPrisma.monitoredAccount.deleteMany()
      await testPrisma.user.deleteMany()
      await testPrisma.planFeature.deleteMany()
      await testPrisma.subscriptionPlan.deleteMany()
      
      console.log('✅ Test data reset completed')
    } catch (error) {
      console.error('❌ Failed to reset test data:', error)
      throw error
    }
  },

  /**
   * Get fresh Prisma client for tests
   */
  getClient() {
    return testPrisma
  }
}

/**
 * Seed test data with subscription plans and features
 */
export async function seedTestData() {
  try {
    console.log('🌱 Seeding test data...')
    
    // Create subscription plans
    const replyGuyPlan = await testPrisma.subscriptionPlan.create({
      data: {
        name: 'reply-guy',
        displayName: 'Reply Guy',
        description: 'Basic plan for getting started',
        price: 20.00,
        baseUsers: 1,
        isActive: true,
      },
    })

    const replyGodPlan = await testPrisma.subscriptionPlan.create({
      data: {
        name: 'reply-god',
        displayName: 'Reply God',
        description: 'Advanced plan for power users',
        price: 50.00,
        baseUsers: 1,
        isActive: true,
      },
    })

    const teamPlan = await testPrisma.subscriptionPlan.create({
      data: {
        name: 'team-plan',
        displayName: 'Team Plan',
        description: 'Enterprise plan for teams',
        price: 79.00,
        baseUsers: 1,
        additionalUserPrice: 50.00,
        isActive: true,
      },
    })

    // Create plan features for Reply Guy
    await testPrisma.planFeature.createMany({
      data: [
        { planId: replyGuyPlan.id, feature: 'AI_CALLS', limit: 100 },
        { planId: replyGuyPlan.id, feature: 'IMAGE_GENERATIONS', limit: 20 },
        { planId: replyGuyPlan.id, feature: 'MONITORED_ACCOUNTS', limit: 3 },
        { planId: replyGuyPlan.id, feature: 'MENTIONS_PER_MONTH', limit: 1000 },
        { planId: replyGuyPlan.id, feature: 'STORAGE_GB', limit: 1 },
        { planId: replyGuyPlan.id, feature: 'TEAM_MEMBERS', limit: 1 },
      ],
    })

    // Create plan features for Reply God
    await testPrisma.planFeature.createMany({
      data: [
        { planId: replyGodPlan.id, feature: 'AI_CALLS', limit: 500 },
        { planId: replyGodPlan.id, feature: 'IMAGE_GENERATIONS', limit: 50 },
        { planId: replyGodPlan.id, feature: 'MONITORED_ACCOUNTS', limit: 10 },
        { planId: replyGodPlan.id, feature: 'MENTIONS_PER_MONTH', limit: 5000 },
        { planId: replyGodPlan.id, feature: 'STORAGE_GB', limit: 5 },
        { planId: replyGodPlan.id, feature: 'TEAM_MEMBERS', limit: 1 },
      ],
    })

    // Create plan features for Team Plan
    await testPrisma.planFeature.createMany({
      data: [
        { planId: teamPlan.id, feature: 'AI_CALLS', limit: 1000 },
        { planId: teamPlan.id, feature: 'IMAGE_GENERATIONS', limit: 100 },
        { planId: teamPlan.id, feature: 'MONITORED_ACCOUNTS', limit: 25 },
        { planId: teamPlan.id, feature: 'MENTIONS_PER_MONTH', limit: 10000 },
        { planId: teamPlan.id, feature: 'STORAGE_GB', limit: 25 },
        { planId: teamPlan.id, feature: 'TEAM_MEMBERS', limit: -1 }, // Unlimited
      ],
    })

    console.log('✅ Test data seeded successfully')
    
    return {
      plans: {
        replyGuy: replyGuyPlan,
        replyGod: replyGodPlan,
        team: teamPlan,
      }
    }
  } catch (error) {
    console.error('❌ Failed to seed test data:', error)
    throw error
  }
}

/**
 * Create test user with specified plan
 */
export async function createTestUser(planName: string = 'reply-guy', userData?: {
  id?: string
  email?: string
  name?: string
  avatar?: string
}) {
  const plan = await testPrisma.subscriptionPlan.findFirst({
    where: { name: planName },
    include: { features: true },
  })

  if (!plan) {
    throw new Error(`Test plan '${planName}' not found`)
  }

  const userId = userData?.id || `test-user-${randomUUID()}`
  
  const user = await testPrisma.user.create({
    data: {
      id: userId,
      email: userData?.email || `test-${userId}@example.com`,
      name: userData?.name || `Test User ${userId.slice(-8)}`,
      avatar: userData?.avatar || null,
      planId: plan.id,
      lastActiveAt: new Date(),
    },
    include: {
      plan: {
        include: {
          features: true,
        },
      },
    },
  })

  console.log(`✅ Created test user: ${user.id} with plan: ${planName}`)
  return user
}

/**
 * Cleanup test database
 */
export async function cleanupTestDb() {
  try {
    console.log('🧹 Cleaning up test database...')
    await testDb.resetData()
    await testPrisma.$disconnect()
    console.log('✅ Test database cleanup completed')
  } catch (error) {
    console.error('❌ Failed to cleanup test database:', error)
    throw error
  }
}
