# BuddyChip Database Schema Documentation

## Overview

This document provides comprehensive documentation for the BuddyChip database schema, built with **Prisma 6.9.0** and **PostgreSQL**. The schema implements modern best practices for performance, scalability, and maintainability.

## Key Features

### 🚀 Modern Prisma 6.9.0 Features
- **PostgreSQL Extensions**: `pgvector`, `pgcrypto`, `pg_trgm`
- **Full-Text Search**: Advanced search capabilities with `fullTextSearchPostgres`
- **Relation Joins**: Optimized query performance with `relationJoins`
- **Advanced Indexing**: GIN, BRIN, and compound indexes for optimal performance

### 🔍 PostgreSQL Optimizations
- **Strategic Indexing**: Compound indexes for common query patterns
- **Full-Text Search**: GIN indexes for content and keyword search
- **Array Support**: Native PostgreSQL arrays for keywords and tags
- **JSON Support**: Flexible metadata storage with proper indexing
- **Decimal Types**: Precise financial calculations for pricing

### 🏗️ Schema Architecture
- **Clerk Integration**: Seamless authentication with Clerk user IDs
- **Flexible Subscription System**: Configurable plans and features
- **Rate Limiting**: Built-in usage tracking and enforcement
- **Audit Trail**: Comprehensive timestamps and metadata
- **Soft Deletes**: Proper cascade relationships and data integrity

## Core Models

### 1. User Model
```prisma
model User {
  id String @id // Clerk user ID
  // ... fields
}
```

**Key Features:**
- Uses Clerk user ID as primary key (no UUID generation needed)
- Tracks user activity with `lastActiveAt`
- Soft relationship with subscription plans via `onDelete: Restrict`
- Comprehensive indexing for performance

**Indexes:**
- Primary: `id` (Clerk user ID)
- Secondary: `email`, `planId`, `createdAt`, `lastActiveAt`

### 2. SubscriptionPlan Model
```prisma
model SubscriptionPlan {
  id String @id @default(cuid())
  price Decimal @db.Decimal(10,2)
  // ... fields
}
```

**Key Features:**
- Uses `Decimal` type for precise financial calculations
- Supports both individual and team pricing models
- Flexible feature configuration via `PlanFeature` relationship
- Plan activation/deactivation support

**Business Logic:**
- **Reply Guy**: $20/month, 1 user, 100 AI calls, 20 images
- **Reply God**: $50/month, 1 user, 500 AI calls, 50 images
- **Team Plan**: $79 + $50/user, unlimited team members, 1000 AI calls, 100 images

### 3. PlanFeature Model
```prisma
model PlanFeature {
  feature FeatureType // Enum for type safety
  limit Int // -1 for unlimited
  // ... fields
}
```

**Key Features:**
- Type-safe feature definitions with enum
- Configurable limits per plan
- Support for unlimited features (`limit: -1`)
- Compound unique constraint prevents duplicate features per plan

**Supported Features:**
- `AI_CALLS`: AI response generations
- `IMAGE_GENERATIONS`: AI image creations
- `MONITORED_ACCOUNTS`: Twitter accounts to watch
- `MENTIONS_PER_MONTH`: Tweet mentions processed
- `STORAGE_GB`: File storage limit
- `TEAM_MEMBERS`: Team size limit

### 4. MonitoredAccount Model
```prisma
model MonitoredAccount {
  twitterHandle String // Without @ symbol
  twitterId String? // Twitter API numeric ID
  // ... fields
}
```

**Key Features:**
- Stores both handle and numeric ID for API flexibility
- Caches Twitter profile information (display name, avatar)
- Tracks monitoring status and last check times
- Prevents duplicate monitoring with unique constraint

**Indexing Strategy:**
- GIN index on `twitterHandle` for fuzzy search
- Compound unique constraint: `[userId, twitterHandle]`
- Performance indexes on `isActive` and `lastCheckedAt`

### 5. Mention Model
```prisma
model Mention {
  id String @id // Twitter Tweet ID
  content String // Tweet text
  keywords String[] // Extracted keywords
  // ... fields
}
```

**Key Features:**
- Uses Twitter tweet ID as primary key
- Rich metadata including engagement metrics
- AI analysis with bullish score (1-100)
- Full-text search capabilities with GIN indexes
- Support for reply threading

**Advanced Features:**
- **Keyword Extraction**: PostgreSQL array for searchable keywords
- **Full-Text Search**: GIN index on content for fast text search
- **Sentiment Analysis**: AI-generated bullish score
- **Rich Metadata**: Author verification, engagement metrics
- **Processing Status**: Tracks AI processing state

**Indexing Strategy:**
```prisma
@@index([content], type: Gin) // Full-text search
@@index([keywords], type: Gin) // Array search
@@index([accountId, mentionedAt(sort: Desc)]) // Common queries
```

### 6. AIResponse Model
```prisma
model AIResponse {
  model String? // Track which AI model was used
  tokensUsed Int? // Usage tracking
  confidence Float? // AI confidence score
  // ... fields
}
```

**Key Features:**
- Tracks AI model usage for analytics
- Token consumption monitoring for billing
- Quality metrics (confidence, user rating)
- Processing time tracking for optimization

### 7. Image Model
```prisma
model Image {
  url String @unique // UploadThing URL
  fileSize Int? // Size tracking
  mimeType String? // File type validation
  // ... fields
}
```

**Key Features:**
- Flexible relationship (can belong to AIResponse or Mention)
- File metadata tracking for storage management
- Accessibility support with `altText`
- Processing status for async operations

### 8. UsageLog Model
```prisma
model UsageLog {
  feature FeatureType // What was used
  billingPeriod String // Monthly tracking
  amount Int @default(1) // Usage quantity
  // ... fields
}
```

**Key Features:**
- Type-safe feature tracking with enum
- Billing period segmentation for monthly limits
- Flexible usage amounts (not just binary)
- Efficient querying with compound indexes

**Rate Limiting Strategy:**
```typescript
// Example usage check
const currentUsage = await prisma.usageLog.aggregate({
  where: {
    userId: user.id,
    feature: FeatureType.AI_CALLS,
    billingPeriod: getCurrentBillingPeriod(),
  },
  _sum: { amount: true },
});
```

## Indexing Strategy

### 1. Single-Column Indexes
```prisma
@@index([userId]) // Foreign key indexes
@@index([createdAt(sort: Desc)]) // Timestamp ordering
@@index([bullishScore(sort: Desc)]) // Score ranking
```

### 2. Compound Indexes
```prisma
@@index([userId, feature, billingPeriod]) // Rate limiting
@@index([accountId, mentionedAt(sort: Desc)]) // Timeline queries
@@index([userId, createdAt(sort: Desc)]) // User activity
```

### 3. Specialized Indexes
```prisma
@@index([content], type: Gin) // Full-text search
@@index([keywords], type: Gin) // Array search
@@index([twitterHandle], type: Gin) // Fuzzy matching
```

### 4. Unique Constraints
```prisma
@@unique([userId, twitterHandle]) // Prevent duplicates
@@unique([planId, feature]) // One feature per plan
```

## PostgreSQL Extensions

### 1. pgvector Extension
```sql
CREATE EXTENSION IF NOT EXISTS vector;
```
**Purpose**: Vector similarity search for Mem0 AI memory storage
**Usage**: Stores conversation embeddings for context retrieval
**Integration**: Automatic via Mem0 library configuration

### 2. pgcrypto Extension
```sql
CREATE EXTENSION IF NOT EXISTS pgcrypto;
```
**Purpose**: Cryptographic functions and secure ID generation
**Usage**: Enhanced security for sensitive operations
**Benefits**: Better random number generation for IDs

### 3. pg_trgm Extension
```sql
CREATE EXTENSION IF NOT EXISTS pg_trgm;
```
**Purpose**: Trigram similarity and fuzzy text search
**Usage**: Twitter handle fuzzy matching and search
**Benefits**: Better user experience for account discovery

## Performance Optimizations

### 1. Query Optimization
- **Relation Joins**: Use `relationLoadStrategy: 'join'` for efficient data fetching
- **Index Coverage**: All foreign keys and common query fields are indexed
- **Compound Indexes**: Optimized for multi-column WHERE clauses

### 2. Data Types
- **Decimal**: Precise financial calculations instead of Float
- **Array**: Native PostgreSQL arrays for keywords
- **JSON**: Flexible metadata storage with proper indexing
- **Timestamp**: Proper timezone handling with DateTime

### 3. Caching Strategy
- **Mention Counts**: Cached in `MonitoredAccount.totalMentions`
- **Last Activity**: Tracked in `User.lastActiveAt`
- **Processing Status**: Prevents duplicate AI processing

## Rate Limiting Implementation

### 1. Usage Tracking
```typescript
// Record usage
await prisma.usageLog.create({
  data: {
    userId: user.id,
    feature: FeatureType.AI_CALLS,
    amount: 1,
    billingPeriod: getCurrentBillingPeriod(),
    metadata: { model: 'gpt-4', tokensUsed: 150 },
  },
});
```

### 2. Limit Checking
```typescript
// Check current usage against plan limits
const planLimit = await prisma.planFeature.findUnique({
  where: {
    planId_feature: {
      planId: user.planId,
      feature: FeatureType.AI_CALLS,
    },
  },
});

const currentUsage = await prisma.usageLog.aggregate({
  where: {
    userId: user.id,
    feature: FeatureType.AI_CALLS,
    billingPeriod: getCurrentBillingPeriod(),
  },
  _sum: { amount: true },
});

const isWithinLimit = planLimit.limit === -1 || 
  (currentUsage._sum.amount || 0) < planLimit.limit;
```

### 3. Billing Period Management
```typescript
function getCurrentBillingPeriod(): string {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
}
```

## Migration Strategy

### 1. Initial Setup
```bash
# Run initial migration with extensions
pnpm prisma migrate dev --name init

# Generate Prisma client
pnpm db:generate

# Seed subscription plans
pnpm db:seed
```

### 2. Production Deployment
```bash
# Deploy schema changes
pnpm prisma migrate deploy

# Validate database
pnpm prisma validate
```

### 3. Data Management
```bash
# View data in Prisma Studio
pnpm db:studio

# Reset database (development only)
pnpm prisma migrate reset
```

## Security Considerations

### 1. Authentication
- All models require proper Clerk authentication
- User ID validation on all operations
- Admin-only operations for plan management

### 2. Data Validation
- Enum types for type safety
- Unique constraints prevent duplicates
- Foreign key relationships ensure data integrity

### 3. Rate Limiting
- Usage tracking prevents abuse
- Plan-based feature restrictions
- Graceful degradation for over-limit users

## Future Enhancements

### 1. Analytics
- Add analytics tables for usage patterns
- Implement data retention policies
- Create reporting dashboards

### 2. Performance
- Consider read replicas for high-traffic queries
- Implement connection pooling
- Add query caching layers

### 3. Features
- Multi-tenant support for enterprise
- Advanced AI model selection
- Enhanced image processing capabilities

## Environment Configuration

### Required Environment Variables
```bash
# Database
DATABASE_URL="********************************/db?schema=public"
DIRECT_URL="********************************/db"

# Clerk Authentication
CLERK_PUBLISHABLE_KEY="pk_..."
CLERK_SECRET_KEY="sk_..."

# Supabase (for Mem0)
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_SERVICE_ROLE_KEY="eyJ..."
```

### Development Commands
```bash
# Database operations
pnpm db:push              # Push schema changes
pnpm db:generate          # Generate Prisma client
pnpm db:studio            # Open Prisma Studio
pnpm db:migrate          # Run migrations
pnpm db:seed             # Seed initial data

# Type checking
pnpm check-types         # Validate TypeScript
```

This schema provides a robust foundation for the BuddyChip application with modern PostgreSQL features, comprehensive indexing, and flexible subscription management.