-- Initial migration to set up PostgreSQL extensions for BuddyChip
-- This migration creates necessary extensions before <PERSON><PERSON><PERSON> generates the schema

-- Enable pgvector extension for Mem0 vector search and AI embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Enable pgcrypto for cryptographic functions and secure random generation
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Enable pg_trgm for fuzzy text search and similarity matching
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Create a custom function for updating the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create indexes for optimal performance on common query patterns
-- These will be created automatically by <PERSON>rism<PERSON> but we can add custom ones here

-- Partial index for active monitored accounts only
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_monitored_accounts_active 
-- ON monitored_accounts (user_id, twitter_handle) 
-- WHERE is_active = true;

-- Partial index for processed mentions
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mentions_processed 
-- ON mentions (account_id, mentioned_at DESC) 
-- WHERE processed = true;

-- GIN index for full-text search on mention content
-- This will be handled by Prisma schema but documented here for reference
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mentions_content_search 
-- ON mentions USING gin(to_tsvector('english', content));

COMMENT ON EXTENSION vector IS 'pgvector extension for vector similarity search - used by Mem0 for AI memory storage';
COMMENT ON EXTENSION pgcrypto IS 'pgcrypto extension for cryptographic functions and secure ID generation';
COMMENT ON EXTENSION pg_trgm IS 'pg_trgm extension for trigram similarity and fuzzy text search';