import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    clientTraceMetadata: ["test-header"],
  },
  // Disable static optimization globally to fix Clerk auth issues
  output: 'standalone',
  // Force all routes to be dynamic by default
  async generateStaticParams() {
    return []
  },
};

// Proper Sentry configuration for production
const finalConfig = process.env.NODE_ENV === "production" 
  ? withSentryConfig(nextConfig, {
      org: "francesco-oddo",
      project: "buddychip-server",
      silent: !process.env.CI,
      widenClientFileUpload: true,
      disableLogger: true,
      automaticVercelMonitors: true,
    })
  : nextConfig;

export default finalConfig;
