# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
pnpm install              # Install dependencies
pnpm dev                  # Start both web and server in development
pnpm dev:web             # Start only the web app (port 3001)
pnpm dev:server          # Start only the server (port 3000)
pnpm build               # Build all applications
pnpm check-types         # TypeScript type checking across all apps
pnpm lint                # Run Biome linting
pnpm lint:fix            # Run Biome linting with auto-fix
pnpm format              # Format code with Biome
```

## Database Commands

```bash
pnpm db:push             # Push schema changes to database
pnpm db:studio           # Open Prisma Studio UI
pnpm db:generate         # Generate Prisma client
pnpm db:migrate          # Run database migrations
```

## Project Architecture

This is a **Turborepo monorepo** with two main applications:

### Apps Structure
- **`apps/web/`** - Next.js frontend application (port 3001)
  - Uses shadcn/ui components with Tailwind CSS
  - Implements tRPC client for type-safe API calls
  - Uses TanStack Query for state management
- **`apps/server/`** - Next.js API server (port 3000)  
  - Serves tRPC API endpoints at `/trpc`
  - Uses Prisma ORM for database operations
  - PostgreSQL database with custom schema location

### Key Technologies
- **Frontend**: Next.js 15, React 19, shadcn/ui, TailwindCSS 4
- **Backend**: Next.js 15, tRPC 11, Prisma 6
- **Database**: PostgreSQL with Prisma ORM
- **Build System**: Turborepo, PNPM workspaces
- **Type Safety**: Full-stack TypeScript with tRPC
- **Error Monitoring**: Sentry for both web and server apps
- **Code Quality**: Biome for linting and formatting

## Important Configuration Details

### Database Setup
- Prisma schema is located at `apps/server/prisma/schema/schema.prisma` (not the default location)
- Generated client outputs to `apps/server/prisma/generated/`
- Requires `DATABASE_URL` and `DIRECT_URL` environment variables in `apps/server/.env`

### tRPC Setup
- Server router defined in `apps/server/src/routers/index.ts`
- Client configuration in `apps/web/src/utils/trpc.ts`
- Uses HTTP batch linking for optimal performance
- Error handling integrated with Sonner toast notifications

### Development Workflow
- Both apps run concurrently during development
- Web app uses Turbopack for fast builds
- Type checking is shared across the monorepo
- Database changes require pushing schema with `pnpm db:push`

### UI Component System
- Uses shadcn/ui with "new-york" style
- Tailwind CSS 4 with CSS variables
- Lucide React for icons
- Theme support with next-themes

### Error Monitoring (Sentry)
- **Web App**: `buddychip-web` project with session replay enabled
- **Server App**: `buddychip-server` project for API error tracking
- **Configuration**: Environment variables set in `.env` files
- **Error Boundary**: React error boundary component available at `apps/web/src/components/error-boundary.tsx`

### Code Quality (Biome)
- **Configuration**: `biome.json` in project root
- **Formatting**: 2-space indentation, semicolons, trailing commas
- **Linting**: TypeScript, React, accessibility, and performance rules
- **Integration**: Configured in all package.json files and Turborepo tasks