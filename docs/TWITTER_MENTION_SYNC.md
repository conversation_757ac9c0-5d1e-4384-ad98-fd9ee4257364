# Twitter Mention Sync Implementation

## Overview

This implementation adds automatic Twitter mention syncing functionality to Buddy<PERSON>hip, allowing users to fetch and manage mentions for their monitored Twitter accounts.

## Architecture

### Backend Components

1. **MentionSyncService** (`/apps/server/src/lib/mention-sync-service.ts`)
   - Core service for syncing mentions from TwitterAPI.io
   - Handles deduplication, error handling, and data transformation
   - Supports both single account and bulk account syncing

2. **tRPC Endpoints** (`/apps/server/src/routers/mentions.ts`)
   - `mentions.syncAccount` - Sync mentions for a specific monitored account
   - `mentions.syncAllAccounts` - Sync mentions for all user's monitored accounts
   - `mentions.getSyncStats` - Get sync statistics and status

3. **API Route** (`/apps/server/src/app/api/sync/mentions/route.ts`)
   - REST API endpoint for external cron services
   - GET `/api/sync/mentions` - Check sync status
   - POST `/api/sync/mentions` - Trigger sync for all users (with optional API key auth)

### Frontend Components

1. **Reply Guy Page** (`/apps/web/src/app/reply-guy/page.tsx`)
   - "Sync Mentions" button for manual synchronization
   - Real-time sync status and progress feedback
   - Integration with existing mention display

## Features

### Manual Sync
- Users can click "Sync Mentions" button to fetch latest mentions
- Shows progress and completion status
- Automatically refreshes mention list after sync

### Automatic Sync (Future)
- Background API route ready for cron job integration
- Supports bulk syncing for all users
- Rate limiting and error handling built-in

### Data Management
- Automatic deduplication prevents duplicate mentions
- Updates account sync timestamps
- Maintains mention counts for statistics

## Usage

### For Users
1. Add monitored accounts via the existing UI
2. Click "Sync Mentions" button on Reply Guy page
3. View newly synced mentions in the feed

### For Developers
1. **Manual Testing**: Use the test script `test/test-mention-sync.js`
2. **API Testing**: Call tRPC endpoints directly
3. **Background Sync**: Set up cron job to call `/api/sync/mentions`

### Environment Variables
- `TWITTER_API_KEY` - Required for TwitterAPI.io access
- `SYNC_API_KEY` - Optional API key for securing the sync endpoint

## API Reference

### tRPC Endpoints

#### `mentions.syncAccount`
```typescript
input: { accountId: string }
output: { success: boolean; result: SyncResult; message: string }
```

#### `mentions.syncAllAccounts`
```typescript
input: void
output: { 
  success: boolean; 
  totalNewMentions: number; 
  accountResults: SyncResult[]; 
  errors: string[]; 
  message: string 
}
```

#### `mentions.getSyncStats`
```typescript
input: void
output: { 
  success: boolean; 
  stats: { 
    totalAccounts: number; 
    activeAccounts: number; 
    totalMentions: number; 
    lastSyncAt: Date | null; 
    accountDetails: AccountDetail[] 
  } 
}
```

### REST API

#### `GET /api/sync/mentions`
Returns sync status and statistics

#### `POST /api/sync/mentions`
Triggers bulk sync operation

Optional body parameters:
- `userId` - Sync specific user only
- `accountId` - Sync specific account only  
- `maxUsers` - Limit number of users to sync (default: 10)

## Error Handling

- Twitter API rate limiting handled with delays between requests
- Individual account failures don't stop bulk operations
- Detailed error logging and user feedback
- Graceful degradation when Twitter API is unavailable

## Performance Considerations

- Caching implemented in TwitterClient to reduce API calls
- Bulk operations include rate limiting delays
- Pagination support for large mention lists
- Database indexes optimized for mention queries

## Testing

### Test Script
```bash
node test/test-mention-sync.js
```

### Manual Testing
1. Start development server: `pnpm dev`
2. Add a monitored account
3. Click "Sync Mentions" button
4. Verify mentions appear in the list

### Production Testing
1. Set up environment variables
2. Configure cron job to call sync endpoint
3. Monitor logs for successful sync operations

## Deployment Checklist

- [ ] `TWITTER_API_KEY` environment variable set
- [ ] Database schema up to date
- [ ] Optional: `SYNC_API_KEY` for secured sync endpoint
- [ ] Optional: Cron job configured for automatic syncing
- [ ] Monitor Twitter API rate limits in production

## Future Enhancements

1. **Real-time Webhooks**: Replace polling with Twitter webhooks when available
2. **Mention Filtering**: Add filters for mention types, sentiment, etc.
3. **Analytics Dashboard**: Show sync statistics and mention trends
4. **Bulk Actions**: Allow bulk operations on synced mentions
5. **Advanced Scheduling**: User-configurable sync intervals