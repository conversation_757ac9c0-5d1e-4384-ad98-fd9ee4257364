name: CI
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: oven-sh/setup-bun@v1
      with: 
        bun-version: "1.2.15"

    - name: Install deps
      run: bun install --frozen-lockfile

    - name: Lint & types
      run: |
        bun run lint
        bun run check-types

    - name: Tests
      run: bun test

  deploy:
    needs: build
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: oven-sh/setup-bun@v1
      with:
        bun-version: "1.2.15"
        
    - name: Install production deps
      run: bun install --production
      
    - name: Deploy to Convex
      run: |
        cd packages/backend
        bunx convex deploy --deployment production --yes
      env:
        CONVEX_DEPLOYMENT: ${{ secrets.CONVEX_DEPLOYMENT }}
        CONVEX_ADMIN_KEY: ${{ secrets.CONVEX_ADMIN_KEY }}