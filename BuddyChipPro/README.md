# 🤖 BuddyChip Pro - AI-Powered Twitter Agent

An intelligent Twitter engagement tool that uses AI to analyze tweets and help you craft meaningful responses for better networking and engagement. Features automated monitoring, AI-powered analysis, and response generation with real-time updates.

## ✨ Key Features

- 🔐 **Google OAuth Authentication** - Secure sign-in with Convex Auth
- 🐦 **Multi-Account Twitter Monitoring** - Track multiple Twitter accounts simultaneously
- 🤖 **Advanced AI Analysis** - Smart tweet analysis using OpenRouter + multiple AI models
- ✍️ **AI Response Generation** - Crafted responses in multiple styles and tones
- 📊 **Real-time Dashboard** - Live monitoring and analytics with priority scoring
- 🎯 **Smart Response Scoring** - AI determines which tweets are worth responding to
- ⏰ **Automated Workflows** - Scheduled scraping, analysis, and response generation
- 💾 **Reactive Backend** - Built on Convex for instant real-time updates
- 🔍 **Mention Monitoring** - Track mentions and replies across all monitored accounts
- 💬 **Context-Aware Responses** - AI understands conversation context for better replies

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, TanStack Router, TailwindCSS, shadcn/ui
- **Backend**: Convex (reactive database + serverless functions)
- **Authentication**: Convex Auth with Google OAuth 2.0
- **AI Services**: OpenRouter, OpenAI, xAI, Perplexity, Anthropic
- **Twitter Integration**: TwitterAPI.io + Official Twitter API
- **Build System**: Turborepo + Bun
- **Real-time**: WebSocket connections via Convex
- **Scheduling**: Convex Cron Jobs for automated workflows

## 🚀 Complete Setup Guide

### Prerequisites
- Node.js 18+ or Bun 1.0+
- Git
- A web browser for OAuth setup

### 1. Clone and Install Dependencies
```bash
git clone https://github.com/OxFrancesco/BuddyChipPro.git
cd BuddyChipPro
bun install
```

### 2. Environment Configuration

#### Step 2.1: Copy Environment Template
```bash
cp .env.example .env.local
```

#### Step 2.2: Configure Required API Keys

**🚀 MINIMUM REQUIRED** (for basic functionality):

1. **Convex Backend** (Free tier available)
   - Go to [dashboard.convex.dev](https://dashboard.convex.dev)
   - Create new project
   - Copy `CONVEX_DEPLOYMENT` and `VITE_CONVEX_URL`

2. **Google OAuth** (Free)
   - Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
   - Create OAuth 2.0 Client ID
   - Add authorized origins: `http://localhost:3001` (dev), your domain (prod)
   - Copy `AUTH_GOOGLE_ID` and `AUTH_GOOGLE_SECRET`

3. **OpenRouter AI** ($5-20/month)
   - Sign up at [openrouter.ai](https://openrouter.ai/keys)
   - Add credits ($5+ recommended for testing)
   - Copy `OPENROUTER_API_KEY`

4. **TwitterAPI.io** ($15-50/month)
   - Sign up at [twitterapi.io](https://twitterapi.io/dashboard)
   - Subscribe to Pro plan (recommended)
   - Copy `TWITTER_API_KEY`

**🔥 RECOMMENDED** (for full features):

5. **OpenAI API** ($10-30/month) - Copy `OPENAI_API_KEY`
6. **xAI API** ($10-30/month) - Copy `XAI_API_KEY`
7. **Twitter Bearer Token** (Free with dev account) - Copy `TWITTER_BEARER_TOKEN`

### 3. Convex Backend Setup

#### Step 3.1: Initialize Convex Project
```bash
bun dev:setup
```

This command will:
- Configure your Convex deployment
- Set up authentication
- Generate API types
- Create necessary database schemas

#### Step 3.2: Configure Environment Variables in Convex
In the Convex dashboard, add your environment variables:
- `OPENROUTER_API_KEY`
- `TWITTER_API_KEY`
- `OPENAI_API_KEY` (optional)
- `XAI_API_KEY` (optional)
- And any other optional variables

### 4. Development Server

#### Start All Services
```bash
bun dev
```

This starts:
- Frontend dev server on `http://localhost:3001`
- Convex backend with real-time sync
- Automated cron jobs (if enabled)

#### Individual Services
```bash
# Frontend only
bun dev:web

# Backend only  
bun dev:server

# Type checking
bun check-types
```

### 5. First Time Setup

1. **Access the App**: Visit `http://localhost:3001`
2. **Sign In**: Use Google OAuth to create your account
3. **Add Twitter Accounts**: Enter Twitter handles you want to monitor
4. **Test AI Features**: Try analyzing tweets and generating responses
5. **Monitor Dashboard**: Check real-time updates and analytics

## 🔧 Configuration Options

### Feature Flags
Control what features are enabled in your `.env.local`:

```bash
ENABLE_AI_ANALYSIS=true
ENABLE_MENTION_MONITORING=true
ENABLE_TWEET_SCRAPING=true
ENABLE_RESPONSE_GENERATION=true
ENABLE_CRON_JOBS=true
```

### Twitter API Configuration
Fine-tune scraping behavior:

```bash
TWITTER_API_DELAY=1000                    # Delay between requests (ms)
TWITTER_DEFAULT_MAX_RESULTS=20            # Tweets per request
TWITTER_MENTION_LOOKBACK_HOURS=24         # How far back to check mentions
DEFAULT_SCRAPE_INTERVAL_MINUTES=60        # How often to scrape
MENTION_CHECK_INTERVAL_MINUTES=15         # How often to check mentions
```

### AI Model Configuration
OpenRouter model preferences and fallbacks are configured in `/packages/backend/convex/lib/openrouter-client.ts`.

## ⏰ Automated Workflows

BuddyChip Pro includes automated cron jobs that run:

### High Frequency (15-60 minutes)
- **Mention Monitoring**: Check for new mentions and replies
- **Quick Health Checks**: Ensure all services are operational

### Daily Workflows
- **Complete Analysis**: Full tweet scraping and AI analysis
- **Response Generation**: Create AI responses for high-value opportunities
- **System Health Check**: Comprehensive service monitoring

### Weekly Workflows
- **Deep Analysis**: Extended scraping with higher limits
- **Data Cleanup**: Remove old data and optimize performance
- **Embedding Generation**: Create vector embeddings for search

## 📊 Usage Examples

### Basic Tweet Analysis
```typescript
// Monitor a Twitter account
await addTwitterAccount({ handle: "elonmusk", isActive: true });

// Scrape recent tweets
await scrapeAccountTweets({ handle: "elonmusk", maxTweets: 20 });

// Analyze with AI
await analyzePendingTweets({ limit: 50 });
```

### Response Generation
```typescript
// Generate AI response for a tweet
await generateResponse({
  content: "Just launched a new product!",
  style: "professional",
  responseType: "reply"
});
```

### Automated Workflows
```typescript
// Run complete workflow
await runCompleteWorkflow({
  handles: ["elonmusk", "sundarpichai"],
  maxTweets: 20,
  generateResponses: true,
  analyzeContent: true
});
```

## 🏗️ Project Structure

```
BuddyChipPro/
├── apps/
│   └── web/                 # React frontend application
│       ├── src/
│       │   ├── components/  # UI components (auth, dashboard, etc.)
│       │   ├── routes/      # TanStack Router pages
│       │   └── lib/         # Utilities and helpers
│       └── package.json
├── packages/
│   └── backend/             # Convex backend
│       ├── convex/
│       │   ├── ai/          # AI analysis and generation
│       │   ├── auth/        # Authentication logic
│       │   ├── mentions/    # Mention monitoring
│       │   ├── tweets/      # Tweet management
│       │   ├── workflows/   # Automated workflows
│       │   ├── crons.ts     # Scheduled jobs
│       │   └── schema.ts    # Database schema
│       └── package.json
├── .env.example             # Environment template
├── convex.json              # Convex configuration
└── package.json             # Root package configuration
```

## 📱 Available Scripts

### Development
```bash
bun dev                      # Start all services
bun dev:web                  # Frontend only
bun dev:server               # Backend only
bun dev:setup                # Initial Convex setup
```

### Production
```bash
bun build                    # Build all applications
bun check-types              # TypeScript validation
```

### Utilities
```bash
bun install                  # Install dependencies
bun clean                    # Clean build artifacts
```

## 🚀 Deployment

### Prerequisites for Production
1. Domain name with SSL certificate
2. Updated OAuth redirect URIs in Google Cloud Console
3. Production Convex deployment
4. Environment variables configured in production

### Deployment Steps
1. **Build the application**: `bun build`
2. **Deploy frontend**: Use Vercel, Netlify, or your preferred platform
3. **Configure Convex**: Set production environment variables
4. **Update OAuth settings**: Add production URLs to Google OAuth
5. **Test thoroughly**: Verify all features work in production

### Environment Variables for Production
Update your production environment with:
- Production API keys (not development keys)
- Production OAuth redirect URIs
- Production Convex deployment URL
- Appropriate rate limits and monitoring settings

## 🔍 Troubleshooting

### Common Issues

**Authentication not working?**
- Verify Google OAuth redirect URIs include your exact domain
- Check that AUTH_GOOGLE_ID and AUTH_GOOGLE_SECRET are correct
- Ensure Convex auth configuration matches your OAuth setup

**Twitter scraping failing?**
- Verify TWITTER_API_KEY is valid and has sufficient credits
- Check API rate limits in TwitterAPI.io dashboard
- Ensure Twitter handles exist and are public

**AI responses not generating?**
- Verify OPENROUTER_API_KEY has sufficient credits
- Check OpenRouter dashboard for API usage and errors
- Ensure AI models are available in your region

**Cron jobs not running?**
- Check Convex dashboard for cron job status
- Verify ENABLE_CRON_JOBS=true in environment
- Monitor Convex logs for error messages

### Debug Mode
Enable detailed logging by setting:
```bash
DEBUG_MODE=true
VERBOSE_LOGGING=true
LOG_LEVEL=debug
```

### Support
- Check the [Convex documentation](https://docs.convex.dev)
- Review [OpenRouter API docs](https://openrouter.ai/docs)
- Submit issues on the GitHub repository

## 💰 Estimated Costs

### Monthly Operating Costs

**Basic Setup (Minimum Required)**:
- Convex: $0-25 (free tier covers most usage)
- OpenRouter: $10-50 (depends on AI usage)
- TwitterAPI.io: $15-50 (Pro plan recommended)
- Google OAuth: Free
- **Total: $25-125/month**

**Full Setup (All Features)**:
- Basic setup: $25-125
- OpenAI API: $10-30
- xAI API: $10-30
- Perplexity API: $5-20
- **Total: $50-205/month**

**Enterprise Usage**:
- Scaled for high volume usage
- Multiple AI providers
- Enhanced rate limits
- **Total: $200-500+/month**

### Cost Optimization Tips
- Start with free tiers where available
- Monitor usage in service dashboards
- Adjust AI model selection based on cost/quality needs
- Use feature flags to disable expensive features during testing
- Set up billing alerts for all paid services

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## ⭐ Support

If you find this project helpful, please consider giving it a star on GitHub!
