# Convex Configuration
VITE_CONVEX_URL=
CONVEX_DEPLOYMENT=

# Google OAuth Configuration
# Get these from: https://console.cloud.google.com/apis/credentials
# 1. Create a new project or select existing
# 2. Enable Google+ API and Google OAuth2 API
# 3. Create OAuth 2.0 Client ID credentials
# 4. Add your domain to authorized origins and redirect URIs
AUTH_GOOGLE_ID=your_google_client_id_here
AUTH_GOOGLE_SECRET=your_google_client_secret_here

# AI Configuration
# OpenRouter API Key for AI tweet analysis and response generation
# Get from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Twitter API Configuration (Optional - for enhanced scraping)
# Get from: https://developer.twitter.com/en/portal/dashboard
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# Additional AI Services (Optional)
# OpenAI API Key for DALL-E image generation
OPENAI_API_KEY=your_openai_api_key_here

# xAI API Key for Live Search integration
XAI_API_KEY=your_xai_api_key_here

# Perplexity API Key for enhanced context
PERPLEXITY_API_KEY=your_perplexity_api_key_here