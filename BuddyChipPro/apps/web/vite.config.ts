import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react";
import path from "node:path";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [
    TanStackRouterVite({
      routesDirectory: "./src/routes",
      generatedRouteTree: "./src/routeTree.gen.ts",
      routeFileIgnorePrefix: "-",
      quoteStyle: "single",
    }),
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Add polyfills for Node.js modules
      "buffer": "buffer",
      "events": "events",
      "stream": "stream-browserify",
      "util": "util",
      "crypto": "crypto-browserify",
    },
  },
  define: {
    global: 'globalThis',
    // Prevent Ethereum property redefinition
    "process.env": {},
    // Fix for EventEmitter
    "process": JSON.stringify({ env: {} }),
  },
  optimizeDeps: {
    include: [
      'buffer', 
      'events',
      'eventemitter3',
      'convex/react', 
      '@clerk/clerk-react',
      'stream-browserify',
      'util',
      'crypto-browserify'
    ],
    exclude: [
      'jayson',
      'rpc-websockets'
    ],
  },
  server: {
    fs: {
      strict: false,
    },
    hmr: {
      overlay: true,
    },
  },
  build: {
    target: 'esnext',
    minify: 'esbuild', // Faster than terser, less likely to cause issues
    sourcemap: false, // Disable sourcemaps for simpler builds
    cssCodeSplit: true,
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // Very simple chunking - let Rollup handle it automatically
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      },
    },
  },
});
