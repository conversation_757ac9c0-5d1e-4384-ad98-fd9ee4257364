import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs";
import { Progress } from "../ui/progress";
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Target,
  Activity,
  Lightbulb,
  AlertCircle,
  CheckCircle,
  Heart,
  Repeat2,
  MessageSquare,
  Eye
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SentimentCorrelationProps {
  accountId?: string;
  timeRange?: "24h" | "7d" | "30d";
  className?: string;
}

export function SentimentCorrelation({ 
  accountId, 
  timeRange = "7d", 
  className 
}: SentimentCorrelationProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);

  // Get sentiment vs engagement correlation data
  const correlationData = useQuery(
    api.mentions.sentimentAnalytics.getSentimentEngagementCorrelation,
    { accountId, timeRange: selectedTimeRange }
  );

  if (!correlationData) {
    return (
      <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
        <CardContent className="py-8">
          <div className="text-center">
            <div className="animate-spin h-8 w-8 border-2 border-[#316FE3] border-t-transparent rounded-full mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">Analyzing correlations...</h3>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (correlationData.analyzedMentions === 0) {
    return (
      <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
        <CardContent className="py-12">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No data available</h3>
            <p className="text-[#6E7A8C]">
              Need at least 5 analyzed mentions to show correlations
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "positive": return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "negative": return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "warning": return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default: return <Lightbulb className="h-4 w-4 text-[#316FE3]" />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case "positive": return "border-green-500/30 bg-green-500/10";
      case "negative": return "border-red-500/30 bg-red-500/10";
      case "warning": return "border-yellow-500/30 bg-yellow-500/10";
      default: return "border-[#316FE3]/30 bg-[#316FE3]/10";
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[#F5F7FA] flex items-center gap-2">
            <Activity className="h-6 w-6 text-[#316FE3]" />
            Sentiment vs Engagement
          </h2>
          <p className="text-[#6E7A8C]">
            How sentiment correlates with engagement levels
          </p>
        </div>
        <Tabs value={selectedTimeRange} onValueChange={(value) => setSelectedTimeRange(value as typeof selectedTimeRange)}>
          <TabsList className="bg-[#000000]/40 border border-[#202631]">
            <TabsTrigger value="24h" className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA]">24h</TabsTrigger>
            <TabsTrigger value="7d" className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA]">7d</TabsTrigger>
            <TabsTrigger value="30d" className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA]">30d</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA]">Bullish High-Engagement</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">
              {correlationData.correlations.bullishHighEngagement}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              mentions with 100+ engagement
            </p>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA]">Bearish High-Engagement</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-400">
              {correlationData.correlations.bearishHighEngagement}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              mentions with 100+ engagement
            </p>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA]">Correlation Score</CardTitle>
            <Target className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className={cn(
              "text-2xl font-bold",
              correlationData.correlations.sentimentEngagementScore > 20 ? "text-green-400" :
              correlationData.correlations.sentimentEngagementScore < -20 ? "text-red-400" :
              "text-gray-400"
            )}>
              {correlationData.correlations.sentimentEngagementScore > 0 ? "+" : ""}
              {correlationData.correlations.sentimentEngagementScore}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              sentiment-engagement index
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Buckets Analysis */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <BarChart3 className="h-5 w-5 text-[#316FE3]" />
            Sentiment by Engagement Level
          </CardTitle>
          <CardDescription className="text-[#6E7A8C]">
            How sentiment changes across different engagement tiers
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {correlationData.buckets.map((bucket: any, idx: number) => (
            <div key={idx} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-[#F5F7FA]">{bucket.label}</span>
                  <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                    {bucket.range} engagement
                  </Badge>
                  <span className="text-xs text-[#6E7A8C]">
                    {bucket.count} mentions ({bucket.percentage}%)
                  </span>
                </div>
                <div className="text-sm font-bold text-[#F5F7FA]">
                  Avg: {bucket.averageScore}/100
                </div>
              </div>

              {bucket.count > 0 && (
                <div className="grid grid-cols-3 gap-2">
                  {/* Bullish */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-green-400">Bullish</span>
                      <span className="text-[#6E7A8C]">{bucket.sentiments.bullish}</span>
                    </div>
                    <Progress 
                      value={bucket.count > 0 ? (bucket.sentiments.bullish / bucket.count) * 100 : 0}
                      className="h-2 bg-gray-800"
                    />
                  </div>

                  {/* Neutral */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-400">Neutral</span>
                      <span className="text-[#6E7A8C]">{bucket.sentiments.neutral}</span>
                    </div>
                    <Progress 
                      value={bucket.count > 0 ? (bucket.sentiments.neutral / bucket.count) * 100 : 0}
                      className="h-2 bg-gray-800"
                    />
                  </div>

                  {/* Bearish */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-red-400">Bearish</span>
                      <span className="text-[#6E7A8C]">{bucket.sentiments.bearish}</span>
                    </div>
                    <Progress 
                      value={bucket.count > 0 ? (bucket.sentiments.bearish / bucket.count) * 100 : 0}
                      className="h-2 bg-gray-800"
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Insights */}
      {correlationData.insights && correlationData.insights.length > 0 && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <Lightbulb className="h-5 w-5 text-yellow-500" />
              Key Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {correlationData.insights.map((insight: any, idx: number) => (
              <div 
                key={idx}
                className={cn(
                  "p-4 rounded-lg border",
                  getInsightColor(insight.type)
                )}
              >
                <div className="flex items-start gap-3">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <h4 className="text-sm font-semibold text-[#F5F7FA] mb-1">
                      {insight.title}
                    </h4>
                    <p className="text-sm text-[#6E7A8C]">
                      {insight.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Engagement Patterns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* High Engagement Distribution */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-[#F5F7FA] flex items-center gap-2">
              <Heart className="h-4 w-4 text-pink-500" />
              High Engagement Patterns
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-[#F5F7FA] mb-2">
                  {correlationData.correlations.bullishHighEngagement + correlationData.correlations.bearishHighEngagement}
                </div>
                <p className="text-sm text-[#6E7A8C]">mentions with 100+ engagement</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-green-400">Bullish mentions</span>
                  <span className="text-sm font-medium text-[#F5F7FA]">
                    {Math.round((correlationData.correlations.bullishHighEngagement / Math.max(1, correlationData.correlations.bullishHighEngagement + correlationData.correlations.bearishHighEngagement)) * 100)}%
                  </span>
                </div>
                <Progress 
                  value={(correlationData.correlations.bullishHighEngagement / Math.max(1, correlationData.correlations.bullishHighEngagement + correlationData.correlations.bearishHighEngagement)) * 100}
                  className="h-2 bg-gray-800"
                />

                <div className="flex items-center justify-between">
                  <span className="text-sm text-red-400">Bearish mentions</span>
                  <span className="text-sm font-medium text-[#F5F7FA]">
                    {Math.round((correlationData.correlations.bearishHighEngagement / Math.max(1, correlationData.correlations.bullishHighEngagement + correlationData.correlations.bearishHighEngagement)) * 100)}%
                  </span>
                </div>
                <Progress 
                  value={(correlationData.correlations.bearishHighEngagement / Math.max(1, correlationData.correlations.bullishHighEngagement + correlationData.correlations.bearishHighEngagement)) * 100}
                  className="h-2 bg-gray-800"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Analysis Summary */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-[#F5F7FA] flex items-center gap-2">
              <Target className="h-4 w-4 text-purple-500" />
              Analysis Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-sm text-[#6E7A8C] mb-1">Total Analyzed</div>
                <div className="text-2xl font-bold text-[#F5F7FA]">
                  {correlationData.analyzedMentions}
                </div>
                <div className="text-xs text-[#6E7A8C]">
                  out of {correlationData.totalMentions} mentions
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-[#6E7A8C]">Coverage</span>
                  <span className="text-[#F5F7FA] font-medium">
                    {Math.round((correlationData.analyzedMentions / Math.max(1, correlationData.totalMentions)) * 100)}%
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-[#6E7A8C]">High Engagement Rate</span>
                  <span className="text-[#F5F7FA] font-medium">
                    {Math.round(((correlationData.correlations.bullishHighEngagement + correlationData.correlations.bearishHighEngagement) / Math.max(1, correlationData.analyzedMentions)) * 100)}%
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-[#6E7A8C]">Sentiment Volatility</span>
                  <span className={cn(
                    "font-medium",
                    Math.abs(correlationData.correlations.sentimentEngagementScore) > 30 ? "text-red-400" :
                    Math.abs(correlationData.correlations.sentimentEngagementScore) > 15 ? "text-yellow-400" :
                    "text-green-400"
                  )}>
                    {Math.abs(correlationData.correlations.sentimentEngagementScore) > 30 ? "High" :
                     Math.abs(correlationData.correlations.sentimentEngagementScore) > 15 ? "Medium" : "Low"}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}