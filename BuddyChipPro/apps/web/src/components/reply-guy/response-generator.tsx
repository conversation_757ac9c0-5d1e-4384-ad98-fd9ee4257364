import { useState, useEffect } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "../ui/tabs";
import { Textarea } from "../ui/textarea";
import { ResponseStyleSelector } from "./response-style-selector";
import { ResponseStyle } from "../../types/responses";

// Define types based on the actual API structure
interface ResponseObject {
  _id: string;
  targetType: "tweet" | "mention";
  targetId: string;
  userId: string;
  content: string;
  style: string;
  characterCount: number;
  confidence: number;
  generationModel?: string;
  contextUsed?: string[];
  responseStrategy?: string;
  estimatedEngagement?: {
    likes: number;
    retweets: number;
    replies: number;
  };
  status: "draft" | "approved" | "declined" | "posted" | "failed";
  generatedImage?: string;
  imagePrompt?: string;
  postedAt?: number;
  postedTweetId?: string;
  actualEngagement?: {
    likes: number;
    retweets: number;
    replies: number;
    views?: number;
  };
  // For compatibility with existing UI that expects these properties
  qualityScore?: number;
  qualityMetrics?: {
    relevance: number;
    clarity: number;
    engagement: number;
    brandSafety: number;
  };
  riskAssessment?: {
    overallRisk: "low" | "medium" | "high";
    riskReasons?: string[];
  };
  engagement?: {
    likes: number;
    retweets: number;
    replies: number;
    quotes: number;
  };
  createdAt: number;
  updatedAt: number;
  approvedAt?: number;
  userFeedback?: {
    rating: number;
    notes?: string;
  };
}

interface GenerateResponsesParams {
  tweetUrl?: string;
  tweetContent?: string;
  mode?: "reply" | "remake";
  authorHandle: string;
  authorDisplayName?: string;
  authorIsVerified?: boolean;
  authorFollowerCount?: number;
  engagement: {
    likes: number;
    retweets: number;
    replies: number;
  };
  userContext?: {
    expertise?: string[];
    interests?: string[];
    writingStyle?: string;
    brand?: string;
  };
  responseStyles?: string[];
  maxLength?: number;
}

interface GeneratedResponsesResult {
  responses: Array<{
    content: string;
    style: string;
    confidence: number;
    characterCount: number;
    model: string;
    estimatedEngagement: {
      likes: number;
      retweets: number;
      replies: number;
    };
    generatedImage?: string;
    imagePrompt?: string;
  }>;
  tweetUrl?: string;
  mode?: "reply" | "remake";
  generatedAt: number;
}
import { 
  Bot, 
  Sparkles, 
  Clock, 
  CheckCircle, 
  XCircle,
  Copy,
  Edit,
  Trash2,
  ThumbsUp,
  ThumbsDown,
  BarChart3,
  Loader2,
  AlertTriangle,
  ExternalLink,
  Reply,
  Search,
  Brain,
  Zap,
  Save,
  X
} from "lucide-react";
import { toast } from "sonner";
import type { Id } from "../../../../../packages/backend/convex/_generated/dataModel";
import { SubscriptionGuard, UsageLimitGuard } from "../billing/subscription-guard";

interface ResponseGeneratorProps {
  targetType: "tweet" | "mention";
  targetId: string;
  originalContent: string;
  originalAuthor: string;
  originalUrl: string;
  userId: string;
  onResponseGenerated?: () => void;
}

export function ResponseGenerator({
  targetType,
  targetId,
  originalContent,
  originalAuthor,
  originalUrl,
  userId,
  onResponseGenerated
}: ResponseGeneratorProps) {
  const [selectedStyles, setSelectedStyles] = useState<ResponseStyle[]>(["professional", "casual", "engaging"]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isImprovingContext, setIsImprovingContext] = useState(false);
  const [improvedContext, setImprovedContext] = useState<any>(null);
  const [showContextInsights, setShowContextInsights] = useState(false);
  const [editingResponseId, setEditingResponseId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState<string>("");
  const [activeTab, setActiveTab] = useState("review");
  const [hasAutoGenerated, setHasAutoGenerated] = useState(false);

  // Get existing responses for this target
  const existingResponses = useQuery(api.responseQueries.getResponsesForTarget, {
    targetType,
    targetId: targetId as Id<"tweets"> | Id<"mentions">,
  });

  // Get available response styles
  const responseStyles = useQuery(api.responseGeneration.getResponseStyles);

  // Actions and Mutations
  const generateResponses = useAction(api.responseGeneration.generateMultiStyleResponses);
  const improveResponseContext = useAction(api.ai.simpleContextImprovement.improveResponseContext);
  const generateWithImprovedContext = useAction(api.ai.simpleContextImprovement.generateResponseWithImprovedContext);
  const updateResponseStatus = useMutation(api.responseMutations.updateResponseStatus);
  const editResponse = useMutation(api.responseMutations.editResponse);
  const deleteResponse = useMutation(api.responseMutations.deleteResponse);

  // Auto-generate responses when component loads (if no existing responses)
  useEffect(() => {
    const shouldAutoGenerate = userId && 
      !hasAutoGenerated && 
      existingResponses !== undefined && 
      existingResponses.length === 0;

    if (shouldAutoGenerate) {
      setHasAutoGenerated(true);
      handleAutoGenerateResponses();
    }
  }, [userId, existingResponses, hasAutoGenerated]);

  const handleAutoGenerateResponses = async () => {
    if (!userId) return;

    setIsGenerating(true);
    try {
      const result = await generateResponses({
        targetType: targetType,
        targetId: targetId as any,
        originalContent: originalContent,
        originalAuthor: originalAuthor,
        originalUrl: originalUrl,
        userId: userId as any,
        selectedStyles: ["professional", "casual", "engaging"],
      });

      toast.success(`Auto-generated ${result.responses.length} responses`);
      onResponseGenerated?.();
    } catch (error) {
      toast.error("Failed to auto-generate responses");
      console.error("Auto-generation failed:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleRegenerateResponses = async () => {
    if (selectedStyles.length === 0) {
      toast.error("Please select at least one response style");
      return;
    }

    if (!userId) {
      toast.error("User not authenticated");
      return;
    }

    setIsGenerating(true);
    try {
      const result = await generateResponses({
        targetType: targetType,
        targetId: targetId as any,
        originalContent: originalContent,
        originalAuthor: originalAuthor,
        originalUrl: originalUrl,
        userId: userId as any,
        selectedStyles: selectedStyles,
        customInstructions: "Regenerate with different approach",
      });

      toast.success(`Regenerated ${result.responses.length} new response${result.responses.length !== 1 ? 's' : ''}`);
      setActiveTab("review");
      onResponseGenerated?.();
    } catch (error) {
      toast.error("Failed to regenerate responses");
      console.error("Response regeneration failed:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleApproveResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId,
        status: "approved",
      });
      toast.success("Response approved");
    } catch (error) {
      toast.error("Failed to approve response");
    }
  };

  const handleRejectResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId,
        status: "declined",
      });
      toast.success("Response declined");
    } catch (error) {
      toast.error("Failed to reject response");
    }
  };

  const handleDeleteResponse = async (responseId: string) => {
    try {
      await deleteResponse({
        responseId,
      });
      toast.success("Response deleted");
    } catch (error) {
      toast.error("Failed to delete response");
    }
  };

  const handleCopyResponse = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Response copied to clipboard");
  };

  const handleReplyOnTwitter = (responseContent: string) => {
    // Check if originalUrl is available
    if (!originalUrl) {
      toast.error("Original tweet URL not available");
      return;
    }

    // Extract tweet ID from the original URL
    const tweetIdMatch = originalUrl.match(/status\/(\d+)/);
    if (!tweetIdMatch) {
      toast.error("Could not extract tweet ID from URL");
      return;
    }
    
    const tweetId = tweetIdMatch[1];
    // Create Twitter reply URL with pre-filled text
    const twitterReplyUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodeURIComponent(responseContent)}`;
    
    // Open in new tab
    window.open(twitterReplyUrl, '_blank');
    toast.success("Opening Twitter reply...");
  };

  const handleImproveContext = async () => {
    if (!userId) {
      toast.error("User not authenticated");
      return;
    }

    setIsImprovingContext(true);
    try {
      toast.info("🤖 Starting AI research with xAI and Perplexity...");
      
      const currentResponseData = existingResponses?.map(r => ({
        content: r.content,
        style: r.style,
        confidence: r.confidence,
      })) || [];

      const result = await improveResponseContext({
        originalContent: originalContent,
        responseType: targetType === "tweet" ? "reply" : "mention",
        authorInfo: {
          handle: originalAuthor,
          displayName: originalAuthor,
        },
        userContext: {
          expertise: ["social media", "content creation"],
          interests: ["engagement", "trending topics"],
          writingStyle: "professional",
          brand: "personal brand",
        },
        currentResponses: currentResponseData,
        researchFocus: ["trending_topics", "audience_insights", "engagement_optimization"],
        maxSteps: 5,
      });

      if (result.success) {
        setImprovedContext(result.improvedContext);
        setShowContextInsights(true);
        toast.success(`✨ Context improved! Found ${result.summary.enhancementsGenerated} insights from ${result.summary.researchSourcesUsed} sources`);
      } else {
        throw new Error(result.error || "Context improvement failed");
      }
    } catch (error) {
      console.error("Context improvement failed:", error);
      toast.error("Failed to improve context. Please try again.");
    } finally {
      setIsImprovingContext(false);
    }
  };

  const handleGenerateWithImprovedContext = async (style: string) => {
    if (!improvedContext) {
      toast.error("No improved context available");
      return;
    }

    setIsGenerating(true);
    try {
      const result = await generateWithImprovedContext({
        originalContent: originalContent,
        improvedContext: improvedContext,
        responseType: targetType === "tweet" ? "reply" : "mention",
        style: style,
        userContext: {
          expertise: ["social media", "content creation"],
          interests: ["engagement", "trending topics"],
          writingStyle: style,
          brand: "personal brand",
        },
      });

      if (result.success) {
        toast.success(`🚀 Enhanced ${style} response generated!`);
        // Trigger regeneration to show new response
        onResponseGenerated?.();
      } else {
        throw new Error(result.error || "Enhanced response generation failed");
      }
    } catch (error) {
      console.error("Enhanced response generation failed:", error);
      toast.error("Failed to generate enhanced response");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleEnhanceIndividualResponse = async (responseId: string, style: string) => {
    if (!improvedContext) {
      // If no context available, first improve context then enhance
      toast.info("Getting fresh research insights...");
      await handleImproveContext();
      return;
    }

    setIsGenerating(true);
    try {
      const result = await generateWithImprovedContext({
        originalContent: originalContent,
        improvedContext: improvedContext,
        responseType: targetType === "tweet" ? "reply" : "mention",
        style: style,
        userContext: {
          expertise: ["social media", "content creation"],
          interests: ["engagement", "trending topics"],
          writingStyle: style,
          brand: "personal brand",
        },
      });

      if (result.success) {
        // Update the existing response with enhanced content
        await editResponse({
          responseId: responseId,
          content: result.enhancedResponse.content,
          notes: "Enhanced with AI research insights",
        });
        
        toast.success(`✨ Response enhanced with AI research!`);
        onResponseGenerated?.();
      } else {
        throw new Error(result.error || "Enhanced response generation failed");
      }
    } catch (error) {
      console.error("Individual response enhancement failed:", error);
      toast.error("Failed to enhance response");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleStartEdit = (responseId: string, currentContent: string) => {
    setEditingResponseId(responseId);
    setEditedContent(currentContent);
  };

  const handleSaveEdit = async (responseId: string) => {
    if (editedContent.trim() === "") {
      toast.error("Response content cannot be empty");
      return;
    }

    try {
      await editResponse({
        responseId: responseId,
        content: editedContent.trim(),
        notes: "User edited response",
      });
      
      setEditingResponseId(null);
      setEditedContent("");
      toast.success("Response updated successfully!");
      onResponseGenerated?.();
    } catch (error) {
      console.error("Failed to update response:", error);
      toast.error("Failed to update response");
    }
  };

  const handleCancelEdit = () => {
    setEditingResponseId(null);
    setEditedContent("");
  };

  const getQualityColor = (score: number) => {
    if (score >= 0.8) return "text-green-600";
    if (score >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "high": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const formatQualityScore = (score: number) => {
    return `${Math.round(score * 100)}%`;
  };

  return (
    <Card className="w-full bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-[var(--buddychip-white)]">
          <Bot className="h-5 w-5 text-[var(--buddychip-accent)]" />
          AI Response Generator
        </CardTitle>
        <CardDescription className="text-[var(--buddychip-grey-text)]">
          Generate AI-powered responses in multiple styles for this {targetType}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] p-1 rounded-lg">
            <TabsTrigger value="review" className="text-[var(--buddychip-grey-text)] data-[state=active]:text-[var(--buddychip-white)] data-[state=active]:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-all duration-200 rounded-md">
              AI Responses
              {existingResponses && existingResponses.length > 0 && (
                <Badge variant="secondary" className="ml-2 bg-[var(--buddychip-accent)] text-white border-[var(--buddychip-accent)]">
                  {existingResponses.length}
                </Badge>
              )}
              {isGenerating && (
                <Badge variant="secondary" className="ml-2 bg-yellow-600 text-white border-yellow-600">
                  Generating...
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="settings" className="text-[var(--buddychip-grey-text)] data-[state=active]:text-[var(--buddychip-white)] data-[state=active]:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-all duration-200 rounded-md">Regenerate</TabsTrigger>
          </TabsList>

          <TabsContent value="settings" className="space-y-6">
            {/* Original Content Preview */}
            <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[var(--buddychip-white)]">Original {targetType}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-[var(--buddychip-white)]">@{originalAuthor}</span>
                    <Badge variant="outline" className="text-xs border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                      {targetType}
                    </Badge>
                  </div>
                  <p className="text-sm leading-relaxed text-[var(--buddychip-white)]">{originalContent}</p>
                </div>
              </CardContent>
            </Card>

            {/* Style Selection */}
            <ResponseStyleSelector
              selectedStyles={selectedStyles as ResponseStyle[]}
              onStylesChange={setSelectedStyles}
              multiple={true}
              userId={userId}
            />

            {/* Improve Context Section */}
            <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-[var(--buddychip-white)] flex items-center gap-2">
                  <Brain className="h-4 w-4 text-[var(--buddychip-accent)]" />
                  AI Context Enhancement
                </CardTitle>
                <CardDescription className="text-xs text-[var(--buddychip-grey-text)]">
                  Use xAI Live Search and Perplexity research to enhance response context with trending topics, audience insights, and competitive analysis.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <SubscriptionGuard feature="basicMonitoring">
                    <Button
                      onClick={handleImproveContext}
                      disabled={isImprovingContext || isGenerating}
                      size="sm"
                      variant="outline"
                      className="flex-1 border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)] hover:text-white"
                    >
                      {isImprovingContext ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Researching...
                        </>
                      ) : (
                        <>
                          <Search className="h-4 w-4 mr-2" />
                          Improve Context
                        </>
                      )}
                    </Button>
                  </SubscriptionGuard>
                  {improvedContext && (
                    <Button
                      onClick={() => setShowContextInsights(!showContextInsights)}
                      size="sm"
                      variant="ghost"
                      className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      {showContextInsights ? 'Hide' : 'Show'} Insights
                    </Button>
                  )}
                </div>
                
                {/* Context Insights Display */}
                {improvedContext && showContextInsights && (
                  <div className="space-y-3 mt-4 p-4 bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg">
                    <div className="text-sm font-medium text-[var(--buddychip-white)] mb-2">
                      📊 Research Insights ({improvedContext.contextEnhancements?.length || 0} found)
                    </div>
                    
                    {improvedContext.contextEnhancements?.slice(0, 3).map((enhancement: any, index: number) => (
                      <div key={index} className="text-xs space-y-1 p-2 bg-[var(--buddychip-light-bg)] rounded border border-[var(--buddychip-grey-stroke)]">
                        <div className="font-medium text-[var(--buddychip-white)]">{enhancement.title}</div>
                        <div className="text-[var(--buddychip-grey-text)]">{enhancement.description}</div>
                        <div className="text-[var(--buddychip-accent)]">💡 {enhancement.actionableAdvice}</div>
                      </div>
                    ))}
                    
                    {improvedContext.overallRecommendation && (
                      <div className="text-xs p-2 bg-green-900/20 border border-green-600/30 rounded">
                        <div className="font-medium text-green-400 mb-1">🎯 Key Recommendation:</div>
                        <div className="text-green-300">{improvedContext.overallRecommendation}</div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <SubscriptionGuard feature="basicMonitoring">
                <UsageLimitGuard feature="aiResponses" amount={1}>
                  <Button
                    onClick={handleRegenerateResponses}
                    disabled={isGenerating || selectedStyles.length === 0}
                    size="lg"
                    className="flex-1 bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/90 text-white"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Regenerating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Regenerate {selectedStyles.length} Response{selectedStyles.length !== 1 ? 's' : ''}
                      </>
                    )}
                  </Button>
                </UsageLimitGuard>
              </SubscriptionGuard>
              
              {improvedContext && (
                <SubscriptionGuard feature="basicMonitoring">
                  <UsageLimitGuard feature="aiResponses" amount={1}>
                    <Button
                      onClick={() => selectedStyles.forEach(style => handleGenerateWithImprovedContext(style))}
                      disabled={isGenerating || selectedStyles.length === 0}
                      size="lg"
                      variant="outline"
                      className="flex-1 border-green-600 text-green-400 hover:bg-green-600 hover:text-white"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Enhancing...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          Generate Enhanced
                        </>
                      )}
                    </Button>
                  </UsageLimitGuard>
                </SubscriptionGuard>
              )}
            </div>
          </TabsContent>

          <TabsContent value="review" className="space-y-4">
            {(!existingResponses || existingResponses.length === 0) && !isGenerating ? (
              <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
                <CardContent className="py-12">
                  <div className="text-center">
                    <Bot className="h-12 w-12 text-[var(--buddychip-grey-text)] mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2 text-[var(--buddychip-white)]">Auto-generating responses...</h3>
                    <p className="text-[var(--buddychip-grey-text)] mb-4">
                      AI is automatically creating 3 response options for you
                    </p>
                    <div className="flex items-center justify-center gap-2 text-yellow-400">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm">This will take a few seconds</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : isGenerating ? (
              <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
                <CardContent className="py-12">
                  <div className="text-center">
                    <Loader2 className="h-12 w-12 text-[var(--buddychip-accent)] mx-auto mb-4 animate-spin" />
                    <h3 className="text-lg font-semibold mb-2 text-[var(--buddychip-white)]">Generating responses...</h3>
                    <p className="text-[var(--buddychip-grey-text)] mb-4">
                      Creating {selectedStyles.length} AI responses with different styles
                    </p>
                    <div className="flex items-center justify-center gap-2 text-[var(--buddychip-accent)]">
                      <Sparkles className="h-4 w-4" />
                      <span className="text-sm">Using Gemini 2.5 Flash</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {existingResponses?.map((response: ResponseObject) => (
                  <Card key={response._id} className="relative bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="capitalize border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                            {response.style}
                          </Badge>
                          <Badge 
                            variant={
                              response.status === 'approved' ? 'default' :
                              response.status === 'declined' ? 'destructive' :
                              response.status === 'posted' ? 'secondary' :
                              'outline'
                            }
                            className="border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]"
                          >
                            {response.status}
                          </Badge>
                          {response.qualityScore && (
                            <Badge variant="secondary" className={`${getQualityColor(response.qualityScore)} bg-[var(--buddychip-grey-stroke)] border-[var(--buddychip-grey-stroke)]`}>
                              Quality: {formatQualityScore(response.qualityScore)}
                            </Badge>
                          )}
                          {response.riskAssessment && (
                            <Badge variant="outline" className={`${getRiskColor(response.riskAssessment.overallRisk)} border-[var(--buddychip-grey-stroke)]`}>
                              {response.riskAssessment.overallRisk} risk
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReplyOnTwitter(response.content)}
                            className="text-[#1DA1F2] hover:text-[#1a8cd8] hover:bg-[#1DA1F2]/10"
                            title="Reply on Twitter"
                          >
                            <Reply className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEnhanceIndividualResponse(response._id, response.style)}
                            className="text-green-400 hover:text-green-300 hover:bg-green-400/10"
                            title="Enhance with AI Research"
                            disabled={isGenerating || isImprovingContext}
                          >
                            ✨
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleStartEdit(response._id, response.content)}
                            className="text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80 hover:bg-[var(--buddychip-accent)]/10"
                            title="Edit Response"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyResponse(response.content)}
                            className="text-[var(--buddychip-white)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
                            title="Copy Response"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteResponse(response._id)}
                            className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
                            title="Delete Response"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Response Content */}
                      <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4 border-l-4 border-l-[var(--buddychip-accent)]">
                        {editingResponseId === response._id ? (
                          <div className="space-y-3">
                            <Textarea
                              value={editedContent}
                              onChange={(e) => setEditedContent(e.target.value)}
                              className="min-h-[100px] resize-none bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] focus:border-[var(--buddychip-accent)]"
                              placeholder="Edit your response..."
                            />
                            <div className="flex items-center justify-between">
                              <div className="text-xs text-[var(--buddychip-grey-text)]">
                                Length: {editedContent.length} characters
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveEdit(response._id)}
                                  className="bg-green-600 hover:bg-green-600/90 text-white"
                                >
                                  <Save className="h-3 w-3 mr-1" />
                                  Save
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={handleCancelEdit}
                                  className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                                >
                                  <X className="h-3 w-3 mr-1" />
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <>
                            <p className="text-sm leading-relaxed text-[var(--buddychip-white)]">{response.content}</p>
                            <div className="mt-2 text-xs text-[var(--buddychip-grey-text)]">
                              Length: {response.content.length} characters
                            </div>
                          </>
                        )}
                      </div>

                      {/* Quality Metrics */}
                      {response.qualityMetrics && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Relevance</div>
                            <div className="font-medium text-[var(--buddychip-white)]">{formatQualityScore(response.qualityMetrics.relevance)}</div>
                          </div>
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Clarity</div>
                            <div className="font-medium text-[var(--buddychip-white)]">{formatQualityScore(response.qualityMetrics.clarity)}</div>
                          </div>
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Engagement</div>
                            <div className="font-medium text-[var(--buddychip-white)]">{formatQualityScore(response.qualityMetrics.engagement)}</div>
                          </div>
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Brand Safety</div>
                            <div className="font-medium text-[var(--buddychip-white)]">{formatQualityScore(response.qualityMetrics.brandSafety)}</div>
                          </div>
                        </div>
                      )}

                      {/* Risk Assessment */}
                      {response.riskAssessment && response.riskAssessment.riskReasons && response.riskAssessment.riskReasons.length > 0 && (
                        <div className="bg-[var(--buddychip-black)] border border-yellow-600 rounded-lg p-3">
                          <div className="flex items-center gap-2 mb-2">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            <span className="text-sm font-medium text-yellow-400">Risk Factors</span>
                          </div>
                          <ul className="text-xs text-yellow-300 space-y-1">
                            {response.riskAssessment.riskReasons.map((reason: string, index: number) => (
                              <li key={index}>• {reason}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Action Buttons */}
                      {response.status === 'draft' && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleApproveResponse(response._id)}
                            className="flex-1 bg-green-600 hover:bg-green-600/90 text-[var(--buddychip-white)] border-0"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleRejectResponse(response._id)}
                            className="flex-1 border border-red-600 text-red-400 hover:bg-red-600/90 hover:text-[var(--buddychip-white)]"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Reject
                          </Button>
                        </div>
                      )}


                      {/* Engagement Metrics (if posted) */}
                      {response.engagement && (
                        <>
                          <Separator />
                          <div className="grid grid-cols-4 gap-4 text-sm">
                            <div className="text-center">
                              <div className="font-medium text-[var(--buddychip-white)]">{response.engagement.likes}</div>
                              <div className="text-[var(--buddychip-grey-text)]">Likes</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium text-[var(--buddychip-white)]">{response.engagement.retweets}</div>
                              <div className="text-[var(--buddychip-grey-text)]">Retweets</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium text-[var(--buddychip-white)]">{response.engagement.replies}</div>
                              <div className="text-[var(--buddychip-grey-text)]">Replies</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium text-[var(--buddychip-white)]">{response.engagement.quotes}</div>
                              <div className="text-[var(--buddychip-grey-text)]">Quotes</div>
                            </div>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

        </Tabs>
      </CardContent>
    </Card>
  );
}