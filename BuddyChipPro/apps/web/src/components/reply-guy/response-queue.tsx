import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { <PERSON><PERSON> } from "../ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { ResponseStyleSelector } from "./response-style-selector";
import { ResponseStyle } from "../../types/responses";
import { 
  MessageSquare, 
  CheckCircle, 
  XCircle,
  Clock,
  Copy,
  Trash2,
  Filter,
  BarChart3,
  Sparkles,
  Bot
} from "lucide-react";
import { toast } from "sonner";
import { ResponseStatus } from "../../types/responses";

interface ResponseQueueProps {
  userId: string;
}

export function ResponseQueue({ userId }: ResponseQueueProps) {
  const [statusFilter, setStatusFilter] = useState<ResponseStatus | "all">("all");
  const [styleFilter, setStyleFilter] = useState<ResponseStyle | "all">("all");

  // Get responses pending review
  const pendingResponses = useQuery(api.responseQueries.getResponsesPendingReview, {
    userId: userId as any,
    style: styleFilter === "all" ? undefined : styleFilter,
  });

  // Get all user responses with filtering
  const allResponses = useQuery(api.responseQueries.getUserResponses, {
    userId: userId as any,
    style: styleFilter === "all" ? undefined : styleFilter,
    status: statusFilter === "all" ? undefined : statusFilter,
    limit: 50,
  });

  // Get response statistics
  const responseStats = useQuery(api.responseQueries.getResponseStats);

  // Mutations
  const updateResponseStatus = useMutation(api.responseMutations.updateResponseStatus);
  const bulkUpdateStatus = useMutation(api.responseMutations.bulkUpdateResponseStatus);
  const deleteResponse = useMutation(api.responseMutations.deleteResponse);

  const [selectedResponses, setSelectedResponses] = useState<string[]>([]);

  const handleApproveResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId: responseId as any,
        status: "approved",
        reviewedBy: userId as any,
      });
      toast.success("Response approved");
    } catch (error) {
      toast.error("Failed to approve response");
    }
  };

  const handleRejectResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId: responseId as any,
        status: "declined",
        reviewedBy: userId as any,
      });
      toast.success("Response declined");
    } catch (error) {
      toast.error("Failed to reject response");
    }
  };

  const handleBulkApprove = async () => {
    if (selectedResponses.length === 0) return;
    
    try {
      await bulkUpdateStatus({
        responseIds: selectedResponses as any,
        status: "approved",
        reviewedBy: userId as any,
      });
      toast.success(`Approved ${selectedResponses.length} responses`);
      setSelectedResponses([]);
    } catch (error) {
      toast.error("Failed to approve responses");
    }
  };

  const handleBulkReject = async () => {
    if (selectedResponses.length === 0) return;
    
    try {
      await bulkUpdateStatus({
        responseIds: selectedResponses as any,
        status: "declined",
        reviewedBy: userId as any,
      });
      toast.success(`Rejected ${selectedResponses.length} responses`);
      setSelectedResponses([]);
    } catch (error) {
      toast.error("Failed to reject responses");
    }
  };

  const handleCopyResponse = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Response copied to clipboard");
  };

  const handleDeleteResponse = async (responseId: string) => {
    try {
      await deleteResponse({
        responseId: responseId as any,
      });
      toast.success("Response deleted");
    } catch (error) {
      toast.error("Failed to delete response");
    }
  };

  const toggleResponseSelection = (responseId: string) => {
    setSelectedResponses(prev => 
      prev.includes(responseId) 
        ? prev.filter(id => id !== responseId)
        : [...prev, responseId]
    );
  };

  const formatQualityScore = (score: number) => {
    return `${Math.round(score * 100)}%`;
  };

  const getQualityColor = (score: number) => {
    if (score >= 0.8) return "text-green-600";
    if (score >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "high": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-orange-500/50 group">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-orange-400 transition-colors">Pending Review</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-orange-400 transition-colors">
              {responseStats?.byStatus.draft || 0}
            </div>
            <p className="text-xs text-[#6E7A8C]">Need approval</p>
          </CardContent>
        </Card>
        
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-[#316FE3]/50 group">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">Approved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">
              {responseStats?.byStatus.approved || 0}
            </div>
            <p className="text-xs text-[#6E7A8C]">Ready to post</p>
          </CardContent>
        </Card>
        
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-green-500/50 group">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-green-400 transition-colors">Posted</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-green-400 transition-colors">
              {responseStats?.byStatus.posted || 0}
            </div>
            <p className="text-xs text-[#6E7A8C]">Live responses</p>
          </CardContent>
        </Card>
        
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-purple-500/50 group">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-purple-400 transition-colors">Avg Quality</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-purple-400 transition-colors">
              {responseStats ? responseStats.averageConfidence : 0}%
            </div>
            <p className="text-xs text-[#6E7A8C]">Quality score</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <Filter className="h-5 w-5 text-[#316FE3]" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-[#F5F7FA]">Status:</label>
              <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as ResponseStatus | "all")}>
                <SelectTrigger className="w-40 bg-[#000000]/40 border-[#202631] text-[#F5F7FA] hover:bg-[#000000]/60 focus:ring-[#316FE3]/30">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  <SelectItem value="all" className="text-[#F5F7FA] hover:bg-[#202631]">All Statuses</SelectItem>
                  <SelectItem value="draft" className="text-[#F5F7FA] hover:bg-[#202631]">Draft</SelectItem>
                  <SelectItem value="pending_review" className="text-[#F5F7FA] hover:bg-[#202631]">Pending Review</SelectItem>
                  <SelectItem value="approved" className="text-[#F5F7FA] hover:bg-[#202631]">Approved</SelectItem>
                  <SelectItem value="declined" className="text-[#F5F7FA] hover:bg-[#202631]">Rejected</SelectItem>
                  <SelectItem value="posted" className="text-[#F5F7FA] hover:bg-[#202631]">Posted</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-[#F5F7FA]">Style:</label>
              <Select value={styleFilter} onValueChange={(value) => setStyleFilter(value as ResponseStyle | "all")}>
                <SelectTrigger className="w-40 bg-[#000000]/40 border-[#202631] text-[#F5F7FA] hover:bg-[#000000]/60 focus:ring-[#316FE3]/30">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  <SelectItem value="all" className="text-[#F5F7FA] hover:bg-[#202631]">All Styles</SelectItem>
                  <SelectItem value="professional" className="text-[#F5F7FA] hover:bg-[#202631]">Professional</SelectItem>
                  <SelectItem value="casual" className="text-[#F5F7FA] hover:bg-[#202631]">Casual</SelectItem>
                  <SelectItem value="humorous" className="text-[#F5F7FA] hover:bg-[#202631]">Humorous</SelectItem>
                  <SelectItem value="technical" className="text-[#F5F7FA] hover:bg-[#202631]">Technical</SelectItem>
                  <SelectItem value="supportive" className="text-[#F5F7FA] hover:bg-[#202631]">Supportive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedResponses.length > 0 && (
        <Card className="border-[#316FE3]/50 bg-[#316FE3]/10 backdrop-blur-sm">
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-[#F5F7FA]">
                  {selectedResponses.length} response{selectedResponses.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <div className="flex gap-2">
                <Button size="sm" onClick={handleBulkApprove} className="bg-green-600 hover:bg-green-700 text-white">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve All
                </Button>
                <Button size="sm" variant="ghost" onClick={handleBulkReject} className="border border-[#202631] hover:bg-[#202631] text-[#F5F7FA] hover:text-[#F5F7FA]">
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject All
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => setSelectedResponses([])}
                  className="text-[#6E7A8C] hover:text-[#F5F7FA] hover:bg-[#202631]"
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Response Tabs */}
      <Tabs defaultValue="pending" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-[#000000]/70 border-[#202631]">
          <TabsTrigger 
            value="pending"
            className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA] data-[state=active]:bg-[#316FE3] hover:text-[#F5F7FA] transition-colors"
          >
            Pending Review
            {pendingResponses && pendingResponses.length > 0 && (
              <Badge variant="destructive" className="ml-2 bg-red-600 text-white">
                {pendingResponses.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger 
            value="all"
            className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA] data-[state=active]:bg-[#316FE3] hover:text-[#F5F7FA] transition-colors"
          >
            All Responses
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          {!pendingResponses || pendingResponses.length === 0 ? (
            <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">All caught up!</h3>
                  <p className="text-[#6E7A8C]">
                    No responses pending review. Great job staying on top of things!
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {pendingResponses.map((response: any) => (
                <Card key={response._id} className="relative bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          checked={selectedResponses.includes(response._id)}
                          onChange={() => toggleResponseSelection(response._id)}
                          className="rounded bg-[#000000]/40 border-[#202631] text-[#316FE3] focus:ring-[#316FE3]/20"
                        />
                        <Badge variant="outline" className="capitalize border-[#202631] text-[#6E7A8C]">
                          {response.style}
                        </Badge>
                        {response.qualityScore && (
                          <Badge variant="secondary" className={`border-[#202631] ${getQualityColor(response.qualityScore)}`}>
                            {formatQualityScore(response.qualityScore)}
                          </Badge>
                        )}
                        {response.riskAssessment && (
                          <Badge variant="outline" className={`border-[#202631] ${getRiskColor(response.riskAssessment.overallRisk)}`}>
                            {response.riskAssessment.overallRisk} risk
                          </Badge>
                        )}
                        <Badge variant="outline" className="border-[#202631] text-[#6E7A8C]">
                          <Clock className="h-3 w-3 mr-1" />
                          Pending
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyResponse(response.content)}
                          className="text-[#6E7A8C] hover:text-[#F5F7FA] hover:bg-[#202631]"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteResponse(response._id)}
                          className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Original Content */}
                    <div className="bg-[#000000]/40 rounded-lg p-3 border border-[#202631]">
                      <div className="text-xs text-[#6E7A8C] mb-1">Original:</div>
                      <p className="text-sm text-[#F5F7FA]">{response.originalContent}</p>
                      <div className="text-xs text-[#6E7A8C] mt-1">
                        by @{response.originalAuthor}
                      </div>
                    </div>

                    {/* Generated Response */}
                    <div className="bg-[#316FE3]/10 rounded-lg p-4 border-l-4 border-[#316FE3]">
                      <p className="text-sm leading-relaxed text-[#F5F7FA]">{response.content}</p>
                      <div className="mt-2 text-xs text-[#6E7A8C]">
                        Length: {response.content.length} characters
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleApproveResponse(response._id)}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={() => handleRejectResponse(response._id)}
                        className="flex-1 border border-[#202631] hover:bg-[#202631] text-[#F5F7FA] hover:text-[#F5F7FA]"
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          {!allResponses || allResponses.length === 0 ? (
            <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <Bot className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No responses yet</h3>
                  <p className="text-[#6E7A8C]">
                    Generate some AI responses to see them here
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {allResponses.map((response: any) => (
                <Card key={response._id} className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="capitalize border-[#202631] text-[#6E7A8C]">
                          {response.style}
                        </Badge>
                        <Badge 
                          className={
                            response.status === 'approved' ? 'bg-[#316FE3] text-white' :
                            response.status === 'declined' ? 'bg-red-600 text-white' :
                            response.status === 'posted' ? 'bg-green-600 text-white' :
                            'border-[#202631] text-[#6E7A8C]'
                          }
                        >
                          {response.status}
                        </Badge>
                        {response.qualityScore && (
                          <Badge variant="secondary" className={`border-[#202631] ${getQualityColor(response.qualityScore)}`}>
                            {formatQualityScore(response.qualityScore)}
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-[#6E7A8C]">
                        {new Date(response.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm leading-relaxed text-[#F5F7FA]">{response.content}</p>
                    
                    {/* Engagement Metrics (if posted) */}
                    {response.engagement && (
                      <div className="mt-4 pt-4 border-t border-[#202631] grid grid-cols-4 gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium text-[#F5F7FA]">{response.engagement.likes}</div>
                          <div className="text-[#6E7A8C]">Likes</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-[#F5F7FA]">{response.engagement.retweets}</div>
                          <div className="text-[#6E7A8C]">Retweets</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-[#F5F7FA]">{response.engagement.replies}</div>
                          <div className="text-[#6E7A8C]">Replies</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-[#F5F7FA]">{response.engagement.quotes}</div>
                          <div className="text-[#6E7A8C]">Quotes</div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}