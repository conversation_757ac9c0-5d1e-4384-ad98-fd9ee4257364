import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { TwitterAccount } from "@/types/responses";
import { But<PERSON> } from "../ui/button";
import { Switch } from "../ui/switch";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Separator } from "../ui/separator";
import { Badge } from "../ui/badge";
import { 
  Settings2, 
  Bell, 
  Users, 
  Target,
  Plus,
  X,
  Save,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

export function MonitoringSettings() {
  // Get user's Twitter accounts
  const twitterAccounts = useQuery(api.users.getUserTwitterAccounts) as TwitterAccount[] | undefined;
  
  // State for form inputs
  const [selectedAccountId, setSelectedAccountId] = useState<string>("");
  const [newKeyword, setNewKeyword] = useState("");
  const [newExcludeKeyword, setNewExcludeKeyword] = useState("");

  // Mutation for updating settings
  const updateMonitoringSettings = useMutation(api.mentions.mentionMutations.updateMonitoringSettings);

  const selectedAccount = twitterAccounts?.find((acc) => acc._id === selectedAccountId);

  const handleToggleMonitoring = async (accountId: string, enabled: boolean) => {
    try {
      await updateMonitoringSettings({
        twitterAccountId: accountId as any,
        isMonitoringEnabled: enabled,
      });
      toast.success(`Monitoring ${enabled ? 'enabled' : 'disabled'} for account`);
    } catch (error) {
      toast.error("Failed to update monitoring settings");
    }
  };

  const handleUpdateSettings = async (accountId: string, settings: any) => {
    try {
      await updateMonitoringSettings({
        twitterAccountId: accountId as any,
        isMonitoringEnabled: true,
      });
      toast.success("Settings updated successfully");
    } catch (error) {
      toast.error("Failed to update settings");
    }
  };

  const handleAddKeyword = async (accountId: string, keyword: string, type: 'mention' | 'exclude') => {
    if (!keyword.trim()) return;

    // Note: Currently only basic monitoring toggle is supported
    // Enhanced keyword filtering will be added in future updates
    try {
      await updateMonitoringSettings({
        twitterAccountId: accountId as any,
        isMonitoringEnabled: true,
      });
      
      if (type === 'mention') {
        setNewKeyword("");
      } else {
        setNewExcludeKeyword("");
      }
      
      toast.success("Monitoring enabled. Advanced keyword filtering coming soon!");
    } catch (error) {
      toast.error("Failed to update monitoring settings");
    }
  };

  const handleRemoveKeyword = async (accountId: string, keyword: string, type: 'mention' | 'exclude') => {
    // Note: Advanced keyword management will be implemented in future updates
    try {
      await updateMonitoringSettings({
        twitterAccountId: accountId as any,
        isMonitoringEnabled: true,
      });
      toast.success("Advanced keyword management coming soon!");
    } catch (error) {
      toast.error("Failed to update settings");
    }
  };

  if (!twitterAccounts || twitterAccounts.length === 0) {
    return (
      <Card className="bg-[#000000]/80 border-[#202631]">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <Settings2 className="h-5 w-5 text-[#316FE3]" />
            Monitoring Settings
          </CardTitle>
          <CardDescription className="text-[#6E7A8C]">
            Configure mention monitoring for your Twitter accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No Twitter accounts found</h3>
            <p className="text-[#6E7A8C]">
              Add Twitter accounts to your dashboard first to enable mention monitoring.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Account Overview */}
      <Card className="bg-[#000000]/80 border-[#202631]">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <Users className="h-5 w-5 text-[#316FE3]" />
            Account Monitoring Overview
          </CardTitle>
          <CardDescription className="text-[#6E7A8C]">
            Enable or disable mention monitoring for each of your Twitter accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {twitterAccounts.map((account) => (
              <div key={account._id} className="flex items-center justify-between p-4 border border-[#202631] rounded-lg bg-[#0E1117]/50">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-[#316FE3]/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-[#316FE3]">
                      {account.handle.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-[#F5F7FA]">@{account.handle}</div>
                    <div className="text-sm text-[#6E7A8C]">{account.displayName}</div>
                  </div>
                  {account.isMonitoringEnabled && (
                    <Badge variant="default" className="text-xs bg-[#316FE3]/20 text-[#316FE3] border-[#316FE3]/30">
                      <Bell className="h-3 w-3 mr-1" />
                      Monitoring
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <Label htmlFor={`monitoring-${account._id}`} className="text-sm text-[#F5F7FA]">
                    Enable Monitoring
                  </Label>
                  <Switch
                    id={`monitoring-${account._id}`}
                    checked={account.isMonitoringEnabled || false}
                    onCheckedChange={(checked) => handleToggleMonitoring(account._id, checked)}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Settings */}
      <Card className="bg-[#000000]/80 border-[#202631]">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <Target className="h-5 w-5 text-[#316FE3]" />
            Detailed Settings
          </CardTitle>
          <CardDescription className="text-[#6E7A8C]">
            Configure specific monitoring and response settings for each account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="account-select" className="text-[#F5F7FA]">Select Account</Label>
              <Select value={selectedAccountId} onValueChange={setSelectedAccountId}>
                <SelectTrigger className="w-full bg-[#0E1117] border-[#202631] text-[#F5F7FA]">
                  <SelectValue placeholder="Choose an account to configure" />
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  {twitterAccounts.map((account) => (
                    <SelectItem key={account._id} value={account._id} className="text-[#F5F7FA] focus:bg-[#316FE3]/20 focus:text-[#F5F7FA]">
                      @{account.handle} - {account.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedAccount && (
              <div className="space-y-6 pt-4">
                {/* Keywords Monitoring */}
                <div className="space-y-3">
                  <Label className="text-base font-medium text-[#F5F7FA]">Additional Keywords</Label>
                  <p className="text-sm text-[#6E7A8C]">
                    Monitor mentions of these keywords in addition to @{selectedAccount.handle}
                  </p>
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter keyword to monitor"
                      value={newKeyword}
                      onChange={(e) => setNewKeyword(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleAddKeyword(selectedAccount._id, newKeyword, 'mention');
                        }
                      }}
                      className="bg-[#0E1117] border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                    />
                    <Button 
                      size="sm" 
                      onClick={() => handleAddKeyword(selectedAccount._id, newKeyword, 'mention')}
                      disabled={!newKeyword.trim()}
                      className="bg-[#316FE3] hover:bg-[#316FE3]/80 text-[#F5F7FA]"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {selectedAccount.mentionKeywords?.map((keyword, index: number) => (
                      <Badge key={index} variant="secondary" className="gap-2">
                        {keyword}
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                          onClick={() => handleRemoveKeyword(selectedAccount._id, keyword, 'mention')}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Response Settings */}
                <div className="space-y-4">
                  <Label className="text-base font-medium text-[#F5F7FA]">Response Automation</Label>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-generate" className="text-[#F5F7FA]">Auto-generate responses</Label>
                        <p className="text-sm text-[#6E7A8C]">
                          Automatically generate AI responses for mentions
                        </p>
                      </div>
                      <Switch
                        id="auto-generate"
                        checked={selectedAccount.responseSettings?.autoGenerateResponses || false}
                        onCheckedChange={(checked) => 
                          handleUpdateSettings(selectedAccount._id, {
                            ...selectedAccount.responseSettings,
                            autoGenerateResponses: checked,
                          })
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="min-followers" className="text-[#F5F7FA]">Minimum follower count</Label>
                      <Input
                        id="min-followers"
                        type="number"
                        placeholder="100"
                        value={selectedAccount.responseSettings?.minimumFollowerCount || ''}
                        onChange={(e) => 
                          handleUpdateSettings(selectedAccount._id, {
                            ...selectedAccount.responseSettings,
                            minimumFollowerCount: parseInt(e.target.value) || 0,
                          })
                        }
                        className="bg-[#0E1117] border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                      />
                      <p className="text-sm text-[#6E7A8C] mt-1">
                        Only respond to users with at least this many followers
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Exclude Keywords */}
                <div className="space-y-3">
                  <Label className="text-base font-medium text-[#F5F7FA]">Exclude Keywords</Label>
                  <p className="text-sm text-[#6E7A8C]">
                    Skip mentions containing these keywords
                  </p>
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter keyword to exclude"
                      value={newExcludeKeyword}
                      onChange={(e) => setNewExcludeKeyword(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleAddKeyword(selectedAccount._id, newExcludeKeyword, 'exclude');
                        }
                      }}
                      className="bg-[#0E1117] border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                    />
                    <Button 
                      size="sm" 
                      onClick={() => handleAddKeyword(selectedAccount._id, newExcludeKeyword, 'exclude')}
                      disabled={!newExcludeKeyword.trim()}
                      className="bg-[#316FE3] hover:bg-[#316FE3]/80 text-[#F5F7FA]"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {selectedAccount.responseSettings?.excludeKeywords?.map((keyword, index: number) => (
                      <Badge key={index} variant="destructive" className="gap-2">
                        {keyword}
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-4 w-4 p-0 hover:bg-destructive-foreground hover:text-destructive"
                          onClick={() => handleRemoveKeyword(selectedAccount._id, keyword, 'exclude')}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}