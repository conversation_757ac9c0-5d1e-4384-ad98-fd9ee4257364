import { useState, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Volume2,
  Zap,
  Bell,
  BellOff,
  Eye,
  RefreshCw,
  Clock,
  X
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SentimentAlertsProps {
  accountId?: string;
  alertThreshold?: number;
  autoRefresh?: boolean;
  className?: string;
}

export function SentimentAlerts({ 
  accountId, 
  alertThreshold = 0.3, 
  autoRefresh = true,
  className 
}: SentimentAlertsProps) {
  const [isEnabled, setIsEnabled] = useState(true);
  const [dismissedAlerts, setDismissedAlerts] = useState<string[]>([]);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  // Get sentiment alerts
  const alertsData = useQuery(
    isEnabled ? api.mentions.sentimentAnalytics.detectSentimentAlerts : "skip",
    isEnabled ? { accountId, alertThreshold } : "skip"
  );

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (!autoRefresh || !isEnabled) return;

    const interval = setInterval(() => {
      setLastRefresh(Date.now());
    }, 30000);

    return () => clearInterval(interval);
  }, [autoRefresh, isEnabled]);

  const handleDismissAlert = (alertId: string) => {
    setDismissedAlerts(prev => [...prev, alertId]);
  };

  const handleToggleAlerts = () => {
    setIsEnabled(!isEnabled);
    if (!isEnabled) {
      setDismissedAlerts([]); // Clear dismissed alerts when re-enabling
    }
  };

  if (!alertsData && isEnabled) {
    return (
      <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
        <CardContent className="py-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin h-6 w-6 border-2 border-[#316FE3] border-t-transparent rounded-full mr-3"></div>
            <span className="text-[#6E7A8C]">Loading alerts...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Filter out dismissed alerts
  const activeAlerts = alertsData?.alerts?.filter((alert: any) => {
    const alertId = `${alert.type}-${alert.timestamp}`;
    return !dismissedAlerts.includes(alertId);
  }) || [];

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "bullish_spike": return <TrendingUp className="h-4 w-4" />;
      case "bearish_spike": return <TrendingDown className="h-4 w-4" />;
      case "volume_spike": return <Volume2 className="h-4 w-4" />;
      case "extreme_sentiment": return <Zap className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getAlertColor = (type: string, severity: string) => {
    if (severity === "high") {
      return type.includes("bullish") ? "text-green-400 border-green-500/30 bg-green-500/10" :
             type.includes("bearish") ? "text-red-400 border-red-500/30 bg-red-500/10" :
             "text-orange-400 border-orange-500/30 bg-orange-500/10";
    } else {
      return type.includes("bullish") ? "text-green-500 border-green-600/30 bg-green-600/10" :
             type.includes("bearish") ? "text-red-500 border-red-600/30 bg-red-600/10" :
             "text-yellow-500 border-yellow-600/30 bg-yellow-600/10";
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "high":
        return <Badge variant="destructive" className="text-xs">High</Badge>;
      case "medium":
        return <Badge variant="secondary" className="text-xs bg-yellow-600 text-yellow-100">Medium</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">Low</Badge>;
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  return (
    <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative">
              {isEnabled ? (
                <Bell className="h-5 w-5 text-[#316FE3]" />
              ) : (
                <BellOff className="h-5 w-5 text-[#6E7A8C]" />
              )}
              {activeAlerts.length > 0 && isEnabled && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
              )}
            </div>
            <CardTitle className="text-[#F5F7FA]">Sentiment Alerts</CardTitle>
            {activeAlerts.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {activeAlerts.length} active
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleAlerts}
              className="text-[#6E7A8C] hover:text-[#F5F7FA]"
            >
              {isEnabled ? <Bell className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
            </Button>
            {isEnabled && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLastRefresh(Date.now())}
                className="text-[#6E7A8C] hover:text-[#F5F7FA]"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        <CardDescription className="text-[#6E7A8C]">
          {isEnabled ? (
            <>
              Real-time sentiment monitoring • 
              <span className="ml-1 text-xs">
                Last update: {formatTimeAgo(lastRefresh)}
              </span>
            </>
          ) : (
            "Alerts disabled"
          )}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {!isEnabled ? (
          <div className="text-center py-8">
            <BellOff className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
            <p className="text-[#6E7A8C] mb-4">Sentiment alerts are disabled</p>
            <Button
              variant="outline"
              onClick={handleToggleAlerts}
              className="border-[#202631] text-[#F5F7FA] hover:bg-[#202631]"
            >
              Enable Alerts
            </Button>
          </div>
        ) : activeAlerts.length === 0 ? (
          <div className="text-center py-8">
            <div className="relative">
              <Activity className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-ping" />
            </div>
            <h3 className="text-lg font-semibold mb-2 text-green-400">All Clear</h3>
            <p className="text-[#6E7A8C]">No significant sentiment changes detected</p>
            
            {/* Current Status */}
            {alertsData?.currentSentiment && (
              <div className="mt-4 p-4 bg-[#000000]/60 rounded-lg border border-[#202631]">
                <div className="flex items-center justify-center gap-4">
                  <div className="text-center">
                    <div className="text-sm text-[#6E7A8C]">Current Score</div>
                    <div className="text-xl font-bold text-[#F5F7FA]">
                      {alertsData.currentSentiment.score}/100
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-[#6E7A8C]">Trend</div>
                    <div className={cn(
                      "text-xl font-bold capitalize",
                      alertsData.currentSentiment.trend === "bullish" ? "text-green-400" :
                      alertsData.currentSentiment.trend === "bearish" ? "text-red-400" :
                      "text-gray-400"
                    )}>
                      {alertsData.currentSentiment.trend}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-[#6E7A8C]">Recent Activity</div>
                    <div className="text-xl font-bold text-[#F5F7FA]">
                      {alertsData.recentActivity.mentions}
                    </div>
                    <div className="text-xs text-[#6E7A8C]">mentions/hour</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {activeAlerts.map((alert: any) => {
              const alertId = `${alert.type}-${alert.timestamp}`;
              const colorClasses = getAlertColor(alert.type, alert.severity);
              
              return (
                <Alert key={alertId} className={cn("border", colorClasses)}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      {getAlertIcon(alert.type)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <AlertTitle className="text-sm font-semibold">
                            {alert.title}
                          </AlertTitle>
                          {getSeverityBadge(alert.severity)}
                          <div className="flex items-center gap-1 text-xs text-[#6E7A8C]">
                            <Clock className="h-3 w-3" />
                            {formatTimeAgo(alert.timestamp)}
                          </div>
                        </div>
                        <AlertDescription className="text-sm">
                          {alert.description}
                        </AlertDescription>
                        {alert.change !== 0 && (
                          <div className="mt-2 flex items-center gap-2">
                            <span className="text-xs text-[#6E7A8C]">Score:</span>
                            <span className="text-sm font-medium">{alert.score}/100</span>
                            {alert.change > 0 ? (
                              <span className="text-green-400 text-sm">
                                (+{alert.change})
                              </span>
                            ) : (
                              <span className="text-red-400 text-sm">
                                ({alert.change})
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDismissAlert(alertId)}
                      className="text-[#6E7A8C] hover:text-[#F5F7FA] p-1"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </Alert>
              );
            })}
          </div>
        )}

        {/* Settings */}
        <div className="pt-4 border-t border-[#202631]">
          <div className="flex items-center justify-between text-sm">
            <span className="text-[#6E7A8C]">Alert sensitivity: {Math.round(alertThreshold * 100)}%</span>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="text-[#6E7A8C] hover:text-[#F5F7FA] text-xs"
              >
                <Eye className="h-3 w-3 mr-1" />
                View All
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}