import { useState } from "react";
import { useMutation, useQuery, useAction } from "convex/react";
import { api } from "../../../../packages/backend/convex/_generated/api";
import type { Doc, Id } from "../../../../packages/backend/convex/_generated/dataModel";
import { Card, CardContent, CardHeader } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import { 
  MessageSquare, 
  Heart, 
  Repeat2, 
  Calendar,
  ExternalLink,
  Check,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Brain,
  Search,
  Zap
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "../../lib/utils";

// Type definitions for proper TypeScript support
type Mention = Doc<"mentions">;
type Response = Doc<"responses">;

interface EnhancedMentionCardProps {
  mention: Mention;
  isHighlighted?: boolean;
}

export function EnhancedMentionCard({ mention, isHighlighted = false }: EnhancedMentionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [improvedContext] = useState<Record<string, unknown> | null>(null);
  const [showContextInsights, setShowContextInsights] = useState(false);

  // Get current user
  const currentUser = useQuery(api.users.getCurrentUser);
  
  // Get responses for this mention
  const mentionResponses = useQuery(api.responseQueries.getResponsesForTarget, {
    targetType: "mention",
    targetId: mention._id,
  });

  // Enhanced responses (responses that have been enhanced with AI research)
  const enhancedResponses = mentionResponses?.filter((r: Response) => r.isEnhanced || r.generationModel?.includes("Enhanced")) || [];
  const hasEnhancedResponses = enhancedResponses.length > 0;

  // Mutations and Actions
  const updateResponseStatus = useMutation(api.responseMutations.updateResponseStatus);
  const deleteResponse = useMutation(api.responseMutations.deleteResponse);
  const markNotificationSent = useMutation(api.mentions.mentionMutations.markNotificationSent);
  const generateResponses = useAction(api.ai.responseGeneration.generateResponsesForMention);

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return 'Just now';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleMarkAsRead = async () => {
    try {
      await markNotificationSent({ mentionId: mention._id });
      toast.success("Marked as read");
    } catch (error) {
      toast.error("Failed to mark as read");
    }
  };

  const handleCopyResponse = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Response copied to clipboard");
  };

  const handleApproveResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId: responseId as Id<"responses">,
        status: "approved",
      });
      toast.success("Response approved!");
    } catch (error) {
      toast.error("Failed to approve response");
    }
  };

  const handleDeleteResponse = async (responseId: string) => {
    try {
      await deleteResponse({
        responseId: responseId as Id<"responses">,
      });
      toast.success("Response deleted");
    } catch (error) {
      toast.error("Failed to delete response");
    }
  };

  const handleGenerateResponses = async () => {
    if (!currentUser) {
      toast.error("User not authenticated");
      return;
    }

    try {
      toast.info("🤖 Generating AI responses...");
      
      const result = await generateResponses({
        mentionId: mention._id,
        mentionContent: mention.mentionContent,
        authorHandle: mention.mentionAuthorHandle,
        authorName: mention.mentionAuthor,
        context: "crypto/DeFi discussion",
      });

      if (result.success && result.totalGenerated > 0) {
        toast.success(`✅ Generated ${result.totalGenerated} contextual AI responses!`);
        // Responses will auto-refresh via the query
      } else {
        toast.error(`Failed to generate responses: ${result.error || "Unknown error"}`);
      }
    } catch (error) {
      console.error("Response generation failed:", error);
      toast.error("Failed to generate responses. Please try again.");
    }
  };

  const handleImproveContext = async () => {
    if (!currentUser) {
      toast.error("User not authenticated");
      return;
    }

    // This functionality needs to be implemented with existing AI actions
    toast.info("Context improvement feature needs to be implemented");
  };


  return (
    <Card 
      className={cn(
        "transition-all duration-300 bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] cursor-pointer hover:border-[var(--buddychip-accent)]/50 hover:shadow-lg hover:shadow-[var(--buddychip-accent)]/10",
        !mention.isNotificationSent && 'ring-2 ring-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10',
        hasEnhancedResponses && 'border-l-4 border-l-[var(--buddychip-accent)] shadow-lg shadow-[var(--buddychip-accent)]/20',
        isHighlighted && 'ring-2 ring-blue-500/50 bg-blue-500/5',
        isExpanded && 'border-[var(--buddychip-accent)]/50 shadow-xl shadow-[var(--buddychip-accent)]/20 bg-[var(--buddychip-accent)]/5'
      )}
      onClick={handleToggleExpanded}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 bg-[var(--buddychip-grey-stroke)] rounded-full flex items-center justify-center">
              <span className="text-sm font-semibold text-[var(--buddychip-white)]">
                {mention.mentionAuthor?.charAt(0).toUpperCase() || '?'}
              </span>
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span className="font-semibold text-[var(--buddychip-white)]">
                  {mention.mentionAuthor || 'Unknown'}
                </span>
                <span className="text-[var(--buddychip-grey-text)] text-sm">
                  @{mention.mentionAuthorHandle || 'unknown'}
                </span>
                <div className={`w-2 h-2 rounded-full ${getPriorityColor(mention.priority)}`} />
                <Badge variant="outline" className="text-xs border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                  {mention.mentionType || 'mention'}
                </Badge>
                {mention.priority === 'high' && (
                  <Badge variant="destructive" className="text-xs">
                    High Priority
                  </Badge>
                )}
                {hasEnhancedResponses && (
                  <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white text-xs">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Enhanced
                  </Badge>
                )}
                {/* Sentiment Indicator */}
                {mention.sentimentAnalysis && (
                  <Badge variant="outline" className="text-xs">
                    {mention.sentimentAnalysis.sentiment} ({mention.sentimentAnalysis.sentimentScore}/100)
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 text-xs text-[var(--buddychip-grey-text)] mt-1">
                <Calendar className="h-3 w-3 text-white" />
                <span>{formatTimeAgo(mention.createdAt)}</span>
                {mentionResponses && mentionResponses.length > 0 && (
                  <>
                    <span>•</span>
                    <span className="text-[var(--buddychip-accent)]">
                      {mentionResponses.length} response{mentionResponses.length !== 1 ? 's' : ''}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
            {!mention.isNotificationSent && (
              <Button 
                size="sm" 
                variant="ghost" 
                className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]" 
                onClick={handleMarkAsRead}
              >
                <Check className="h-4 w-4 mr-1 text-white" />
                Mark Read
              </Button>
            )}
            {improvedContext && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowContextInsights(!showContextInsights)}
                className="border border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)] hover:text-white"
              >
                <Brain className="h-4 w-4 mr-1 text-white" />
                {showContextInsights ? 'Hide' : 'Show'} Insights
              </Button>
            )}
            <Button
              size="sm"
              variant="ghost"
              onClick={handleImproveContext}
              className="border border-green-600 text-green-400 hover:bg-green-600 hover:text-white"
            >
              <Search className="h-4 w-4 mr-1 text-white" />
              Research Topic
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
              onClick={(e) => {
                e.stopPropagation();
                const url = mention.url || `https://twitter.com/i/web/status/${mention.mentionTweetId}`;
                window.open(url, '_blank');
              }}
            >
              <ExternalLink className="h-4 w-4 text-white" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
              onClick={(e) => {
                e.stopPropagation();
                handleToggleExpanded();
              }}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4 text-white" /> : <ChevronDown className="h-4 w-4 text-white" />}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Mention Content */}
        <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4">
          <p className="text-sm leading-relaxed text-[var(--buddychip-white)]">
            {mention.mentionContent}
          </p>
          
          {/* Sentiment Analysis */}
          {mention.sentimentAnalysis && (
            <div className="mt-3 pt-3 border-t border-[var(--buddychip-grey-stroke)]">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-[var(--buddychip-grey-text)]">Sentiment Analysis</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-[var(--buddychip-grey-text)]">
                      {Math.round(mention.sentimentAnalysis.confidence * 100)}% confidence
                    </span>
                    {mention.sentimentAnalysis.keyWords && mention.sentimentAnalysis.keyWords.length > 0 && (
                      <div className="flex gap-1">
                        {mention.sentimentAnalysis.keyWords.slice(0, 3).map((word: string, idx: number) => (
                          <span key={idx} className="text-xs bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)] px-2 py-1 rounded">
                            {word}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={mention.sentimentAnalysis.sentiment === 'bullish' ? 'default' : 
                            mention.sentimentAnalysis.sentiment === 'bearish' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {mention.sentimentAnalysis.sentiment}
                  </Badge>
                  <span className="text-sm text-[var(--buddychip-white)]">
                    {mention.sentimentAnalysis.sentimentScore}/100
                  </span>
                </div>
                {mention.sentimentAnalysis.reasoning && (
                  <p className="text-xs text-[var(--buddychip-grey-text)] italic">
                    {mention.sentimentAnalysis.reasoning}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Quick Response Summary (when collapsed) */}
        {!isExpanded && mentionResponses && mentionResponses.length > 0 && (
          <div className="bg-[var(--buddychip-black)]/50 border border-[var(--buddychip-grey-stroke)] rounded-lg p-3 hover:bg-[var(--buddychip-accent)]/5 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-white" />
                <span className="text-sm font-medium text-[var(--buddychip-white)]">
                  {mentionResponses.length} AI Response{mentionResponses.length !== 1 ? 's' : ''} Ready
                </span>
                {hasEnhancedResponses && (
                  <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white text-xs animate-pulse">
                    ✨ {enhancedResponses.length} Enhanced
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 text-xs text-[var(--buddychip-grey-text)]">
                <ChevronDown className="h-4 w-4 text-white" />
                <span>Click to expand</span>
              </div>
            </div>
          </div>
        )}

        {/* Expandable hint when no responses */}
        {!isExpanded && (!mentionResponses || mentionResponses.length === 0) && (
          <div className="flex items-center justify-center py-2 text-xs text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-accent)] transition-colors">
            <ChevronDown className="h-4 w-4 mr-1 text-white" />
            <span>Click to expand and generate AI responses</span>
          </div>
        )}

        {/* Expanded Content */}
        {isExpanded && (
          <div className="space-y-6" onClick={(e) => e.stopPropagation()}>
            <Separator />
            
            {/* Context Insights Panel - Placeholder */}
            {improvedContext && (
              <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-[var(--buddychip-white)]">Context Insights</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowContextInsights(!showContextInsights)}
                  >
                    {showContextInsights ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </div>
                {showContextInsights && (
                  <div className="text-xs text-[var(--buddychip-grey-text)]">
                    Context insights would be displayed here
                  </div>
                )}
              </div>
            )}

            {/* Enhanced Response Display - Simplified Version */}
            {mentionResponses && mentionResponses.length > 0 ? (
              <div>
                <div className="flex items-center justify-between mb-4">
                </div>
                
                <div className="space-y-4">
                  {mentionResponses.map((response: Response) => (
                    <div key={response._id} className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {response.style}
                          </Badge>
                          <span className="text-xs text-[var(--buddychip-grey-text)]">
                            {Math.round(response.confidence * 100)}% confidence
                          </span>
                          {response.isEnhanced && (
                            <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white text-xs">
                              <Sparkles className="h-3 w-3 mr-1" />
                              Enhanced
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleCopyResponse(response.content)}
                            className="h-8 w-8 p-0"
                          >
                            <MessageSquare className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleApproveResponse(response._id)}
                            className="h-8 w-8 p-0 text-green-500 hover:text-green-400"
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDeleteResponse(response._id)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-400"
                          >
                            <Zap className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-[var(--buddychip-white)] leading-relaxed mb-3">
                        {response.content}
                      </p>
                      {response.estimatedEngagement && (
                        <div className="flex items-center gap-4 text-xs text-[var(--buddychip-grey-text)]">
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {response.estimatedEngagement.likes}
                          </span>
                          <span className="flex items-center gap-1">
                            <Repeat2 className="h-3 w-3" />
                            {response.estimatedEngagement.retweets}
                          </span>
                          <span className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3" />
                            {response.estimatedEngagement.replies}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="space-y-4">
                  <div className="text-[var(--buddychip-grey-text)]">
                    No AI responses generated yet for this mention.
                  </div>
                  <Button
                    variant="outline"
                    onClick={handleGenerateResponses}
                    className="border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)] hover:text-white"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate AI Responses
                  </Button>
                </div>
              </div>
            )}

          </div>
        )}
      </CardContent>
    </Card>
  );
}