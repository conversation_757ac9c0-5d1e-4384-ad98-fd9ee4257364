import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardHeader } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { Separator } from "../ui/separator";
import { ConfirmDialog } from "../ui/confirm-dialog";
import { ResponseGenerator } from "./response-generator";
import { SentimentBar, SentimentIndicator, SentimentTooltip } from "../ui/sentiment-bar";
import { SentimentResponseSuggestions } from "./sentiment-response-suggestions";
import { 
  MessageSquare, 
  Heart, 
  Repeat2, 
  Eye, 
  Calendar,
  ExternalLink,
  Bot,
  Check,
  X,
  Copy,
  Sparkles,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react";
import { toast } from "sonner";

interface MentionCardProps {
  mention: any; // Type from Convex schema
}

export function MentionCard({ mention }: MentionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedResponseId, setSelectedResponseId] = useState<string | null>(null);
  const [showResponseGenerator, setShowResponseGenerator] = useState(false);
  const [showSentimentSuggestions, setShowSentimentSuggestions] = useState(false);
  const [showMarkReadDialog, setShowMarkReadDialog] = useState(false);

  // Get current user
  const currentUser = useQuery(api.userQueries.getCurrentUser);
  
  // Get responses for this mention
  const mentionResponses = useQuery(api.responseQueries.getResponsesForTarget, {
    targetType: "mention",
    targetId: mention._id,
  });

  // Mutations for response management
  const updateResponseStatus = useMutation(api.responseMutations.updateResponseStatus);
  const markNotificationSent = useMutation(api.mentions.mentionMutations.markNotificationSent);

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return 'Just now';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const handleApproveResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId: responseId as any,
        status: "approved",
      });
      toast.success("Response approved!");
    } catch (error) {
      toast.error("Failed to approve response");
    }
  };

  const handleDeclineResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId: responseId as any,
        status: "declined",
      });
      toast.success("Response declined");
    } catch (error) {
      toast.error("Failed to decline response");
    }
  };

  const handleCopyResponse = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Response copied to clipboard");
  };

  const handleMarkAsRead = () => {
    if (!mention.isNotificationSent) {
      // Check if user chose "don't show today" 
      const storageKey = "mark-read-confirmation-dismissed";
      const dismissed = localStorage.getItem(storageKey);
      
      if (dismissed) {
        const dismissedDate = new Date(dismissed);
        const today = new Date();
        
        // If dismissed today, skip dialog and mark as read directly
        if (dismissedDate.toDateString() === today.toDateString()) {
          confirmMarkAsRead();
          return;
        }
      }
      
      // Show dialog if not dismissed today
      setShowMarkReadDialog(true);
    }
  };

  const confirmMarkAsRead = async () => {
    try {
      await markNotificationSent({ mentionId: mention._id });
      toast.success("Marked as read");
    } catch (error) {
      toast.error("Failed to mark as read");
    }
  };

  return (
    <Card className={`transition-all duration-200 bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] ${!mention.isNotificationSent ? 'ring-2 ring-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 bg-[var(--buddychip-grey-stroke)] rounded-full flex items-center justify-center">
              <span className="text-sm font-semibold text-[var(--buddychip-white)]">
                {mention.mentionAuthor?.charAt(0).toUpperCase() || '?'}
              </span>
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span className="font-semibold text-[var(--buddychip-white)]">{mention.mentionAuthor || 'Unknown'}</span>
                <span className="text-[var(--buddychip-grey-text)] text-sm">@{mention.mentionAuthorHandle || 'unknown'}</span>
                <div className={`w-2 h-2 rounded-full ${getPriorityColor(mention.priority)}`} />
                <Badge variant="outline" className="text-xs border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                  {mention.mentionType}
                </Badge>
                {mention.priority === 'high' && (
                  <Badge variant="destructive" className="text-xs">
                    High Priority
                  </Badge>
                )}
                {/* Sentiment Indicator */}
                {mention.sentimentAnalysis && (
                  <SentimentIndicator
                    sentiment={mention.sentimentAnalysis.sentiment}
                    score={mention.sentimentAnalysis.sentimentScore}
                    size="sm"
                    showScore={true}
                  />
                )}
              </div>
              <div className="flex items-center gap-2 text-xs text-[var(--buddychip-grey-text)] mt-1">
                <Calendar className="h-3 w-3" />
                <span>{formatTimeAgo(mention.createdAt)}</span>
                <span>•</span>
                <span>to @{mention.monitoredAccount?.handle}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {!mention.isNotificationSent && (
              <Button size="sm" variant="ghost" className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]" onClick={handleMarkAsRead}>
                <Check className="h-4 w-4 mr-1" />
                Mark Read
              </Button>
            )}
            {mention.sentimentAnalysis && (
              <Button
                size="sm"
                variant="ghost"
                className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
                onClick={() => setShowSentimentSuggestions(!showSentimentSuggestions)}
              >
                <TrendingUp className="h-4 w-4 mr-1" />
                {showSentimentSuggestions ? 'Hide' : 'Show'} Smart Suggestions
              </Button>
            )}
            <Button
              size="sm"
              variant="ghost"
              className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
              onClick={() => setShowResponseGenerator(!showResponseGenerator)}
            >
              <Sparkles className="h-4 w-4 mr-1" />
              {showResponseGenerator ? 'Hide' : 'Show'} AI Response
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
              onClick={() => window.open(`https://twitter.com/i/web/status/${mention.mentionTweetId}`, '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Mention Content */}
        <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4">
          <p className="text-sm leading-relaxed text-[var(--buddychip-white)]">{mention.mentionContent || mention.content}</p>
          
          {/* Sentiment Analysis Bar */}
          {mention.sentimentAnalysis && (
            <div className="mt-3 pt-3 border-t border-[var(--buddychip-grey-stroke)]">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-[var(--buddychip-grey-text)]">Sentiment Analysis</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-[var(--buddychip-grey-text)]">
                      {Math.round(mention.sentimentAnalysis.confidence * 100)}% confidence
                    </span>
                    {mention.sentimentAnalysis.keyWords && mention.sentimentAnalysis.keyWords.length > 0 && (
                      <div className="flex gap-1">
                        {mention.sentimentAnalysis.keyWords.slice(0, 3).map((word: string, idx: number) => (
                          <span key={idx} className="text-xs bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)] px-2 py-1 rounded">
                            {word}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <SentimentBar
                  sentiment={mention.sentimentAnalysis.sentiment}
                  score={mention.sentimentAnalysis.sentimentScore}
                  confidence={mention.sentimentAnalysis.confidence}
                  compact={true}
                  showLabel={false}
                  showScore={true}
                />
                {mention.sentimentAnalysis.reasoning && (
                  <p className="text-xs text-[var(--buddychip-grey-text)] italic">
                    {mention.sentimentAnalysis.reasoning}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Engagement Metrics */}
        <div className="flex items-center gap-4 text-sm text-[var(--buddychip-grey-text)]">
          <div className="flex items-center gap-1">
            <Heart className="h-4 w-4" />
            <span>{mention.engagement.likes}</span>
          </div>
          <div className="flex items-center gap-1">
            <Repeat2 className="h-4 w-4" />
            <span>{mention.engagement.retweets}</span>
          </div>
          <div className="flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            <span>{mention.engagement.replies}</span>
          </div>
          {mention.engagement.views && (
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{mention.engagement.views.toLocaleString()}</span>
            </div>
          )}
        </div>

        {/* Sentiment-Based Response Suggestions */}
        {showSentimentSuggestions && mention.sentimentAnalysis && (
          <>
            <Separator />
            <SentimentResponseSuggestions mentionId={mention._id} />
          </>
        )}

        {/* Response Generator */}
        {showResponseGenerator && (
          <>
            <Separator />
            {currentUser && (
              <ResponseGenerator
                targetType="mention"
                targetId={mention._id}
                originalContent={mention.mentionContent || mention.content}
                originalAuthor={mention.mentionAuthor || 'Unknown'}
                originalUrl={mention.url}
                userId={currentUser._id}
                onResponseGenerated={() => {
                  // Optionally close the generator after successful generation
                  // setShowResponseGenerator(false);
                }}
              />
            )}
          </>
        )}

        {/* Quick View of Existing Responses */}
        {!showResponseGenerator && mentionResponses && mentionResponses.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bot className="h-4 w-4 text-[var(--buddychip-accent)]" />
                  <span className="text-sm font-medium text-[var(--buddychip-white)]">AI Generated Responses</span>
                  <Badge variant="secondary" className="text-xs bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] border-[var(--buddychip-grey-stroke)]">
                    {mentionResponses.length} options
                  </Badge>
                </div>
                <Button 
                  size="sm" 
                  variant="ghost"
                  className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
                  onClick={() => setShowResponseGenerator(true)}
                >
                  <Sparkles className="h-4 w-4 mr-1" />
                  Manage
                </Button>
              </div>
              
              <div className="space-y-2">
                {mentionResponses.slice(0, 2).map((response: any) => (
                  <div 
                    key={response._id}
                    className="border border-[var(--buddychip-grey-stroke)] rounded-lg p-3 bg-[var(--buddychip-black)]"
                  >
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs capitalize border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                            {response.style}
                          </Badge>
                          {response.qualityScore && (
                            <Badge variant="secondary" className="text-xs bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] border-[var(--buddychip-grey-stroke)]">
                              {Math.round(response.qualityScore * 100)}% quality
                            </Badge>
                          )}
                          <Badge 
                            variant={
                              response.status === 'approved' ? 'default' :
                              response.status === 'declined' ? 'destructive' :
                              response.status === 'posted' ? 'secondary' :
                              'outline'
                            }
                            className="text-xs border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]"
                          >
                            {response.status}
                          </Badge>
                        </div>
                        <p className="text-sm leading-relaxed line-clamp-2 text-[var(--buddychip-white)]">{response.content}</p>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleCopyResponse(response.content)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        
                        {response.status === 'draft' && (
                          <>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-green-500 hover:text-green-400 hover:bg-green-500/10"
                              onClick={() => handleApproveResponse(response._id)}
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
                              onClick={() => handleDeclineResponse(response._id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {mentionResponses.length > 2 && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="w-full text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
                    onClick={() => setShowResponseGenerator(true)}
                  >
                    View all {mentionResponses.length} responses
                  </Button>
                )}
              </div>
            </div>
          </>
        )}

        {/* Processing Status */}
        {!mention.isProcessed && (
          <div className="flex items-center gap-2 text-sm text-[var(--buddychip-grey-text)]">
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
            <span>AI analysis in progress...</span>
          </div>
        )}
      </CardContent>

      {/* Mark Read Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showMarkReadDialog}
        onClose={() => setShowMarkReadDialog(false)}
        onConfirm={confirmMarkAsRead}
        title="Remove Tweet"
        message="Do you want to remove this tweet?"
        confirmText="Remove"
        cancelText="Cancel"
        showDontShowToday={true}
        storageKey="mark-read-confirmation-dismissed"
      />
    </Card>
  );
}