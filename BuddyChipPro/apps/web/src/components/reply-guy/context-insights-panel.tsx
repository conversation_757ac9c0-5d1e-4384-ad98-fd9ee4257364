import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { Separator } from "../ui/separator";
import { 
  Brain, 
  Search, 
  TrendingUp, 
  Users, 
  Target, 
  Lightbulb, 
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Globe,
  BarChart3,
  Eye,
  EyeOff
} from "lucide-react";

interface ContextEnhancement {
  title: string;
  description: string;
  keyInformation: string;
  source?: string;
  confidence?: number;
  category?: "current_events" | "topic_background" | "recent_developments" | "factual_context";
}

interface ResearchSource {
  title: string;
  url?: string;
  snippet: string;
  relevanceScore: number;
  source: "xai" | "perplexity" | "web";
}

interface ImprovedContext {
  contextEnhancements: ContextEnhancement[];
  researchSources?: ResearchSource[];
  topicSummary?: string;
  relatedTopics?: string[];
  currentEvents?: {
    recentNews: string[];
    trendingDiscussions: string[];
    keyDevelopments: string[];
  };
  factualContext?: {
    keyFacts: string[];
    importantDates: string[];
    relevantNumbers: string[];
  };
}

interface ContextInsightsPanelProps {
  improvedContext: ImprovedContext;
  isVisible: boolean;
  onToggleVisibility: () => void;
  className?: string;
}

export function ContextInsightsPanel({
  improvedContext,
  isVisible,
  onToggleVisibility,
  className = ""
}: ContextInsightsPanelProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>(["enhancements"]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "current_events": return <Globe className="h-4 w-4" />;
      case "topic_background": return <Search className="h-4 w-4" />;
      case "recent_developments": return <TrendingUp className="h-4 w-4" />;
      case "factual_context": return <BarChart3 className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "current_events": return "text-orange-400 bg-orange-400/10 border-orange-400/30";
      case "topic_background": return "text-blue-400 bg-blue-400/10 border-blue-400/30";
      case "recent_developments": return "text-green-400 bg-green-400/10 border-green-400/30";
      case "factual_context": return "text-purple-400 bg-purple-400/10 border-purple-400/30";
      default: return "text-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10 border-[var(--buddychip-accent)]/30";
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case "xai": return "🤖";
      case "perplexity": return "🔍";
      case "web": return "🌐";
      default: return "📄";
    }
  };

  if (!isVisible) {
    return (
      <div className={`${className}`}>
        <Button
          onClick={onToggleVisibility}
          variant="outline"
          size="sm"
          className="w-full border-[var(--buddychip-accent)]/30 text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/10"
        >
          <Eye className="h-4 w-4 mr-2" />
          Show AI Research Insights
        </Button>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Card className="bg-gradient-to-r from-[var(--buddychip-black)]/90 to-[var(--buddychip-accent)]/5 border-[var(--buddychip-accent)]/30 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold text-[var(--buddychip-white)] flex items-center gap-2">
              <Brain className="h-5 w-5 text-[var(--buddychip-accent)]" />
              Topic Research & Context
              <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white">
                {improvedContext.contextEnhancements?.length || 0} Found
              </Badge>
            </CardTitle>
            <Button
              onClick={onToggleVisibility}
              variant="ghost"
              size="sm"
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
            >
              <EyeOff className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Context Enhancements */}
          <div className="space-y-3">
            <Button
              onClick={() => toggleSection("enhancements")}
              variant="ghost"
              className="w-full justify-between p-0 h-auto text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)]"
            >
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-[var(--buddychip-accent)]" />
                <span className="font-medium">Research Findings & Context</span>
                <Badge variant="secondary" className="bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                  {improvedContext.contextEnhancements?.length || 0}
                </Badge>
              </div>
              {expandedSections.includes("enhancements") ? 
                <ChevronUp className="h-4 w-4" /> : 
                <ChevronDown className="h-4 w-4" />
              }
            </Button>

            {expandedSections.includes("enhancements") && (
              <div className="space-y-3 ml-6">
                {improvedContext.contextEnhancements?.slice(0, 5).map((enhancement, index) => (
                  <div 
                    key={index} 
                    className={`p-4 rounded-lg border ${getCategoryColor(enhancement.category || 'default')}`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        {getCategoryIcon(enhancement.category || 'default')}
                      </div>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-[var(--buddychip-white)] text-sm">
                            {enhancement.title}
                          </h4>
                          {enhancement.confidence && (
                            <Badge variant="outline" className="text-xs border-current">
                              {Math.round(enhancement.confidence * 100)}% confident
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-[var(--buddychip-grey-text)]">
                          {enhancement.description}
                        </p>
                        <div className="bg-[var(--buddychip-black)]/50 rounded p-2 border-l-2 border-l-current">
                          <div className="text-xs text-[var(--buddychip-white)] font-medium mb-1">
                            🔍 Key Information:
                          </div>
                          <div className="text-xs text-[var(--buddychip-white)]">
                            {enhancement.keyInformation}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Topic Summary */}
          {improvedContext.topicSummary && (
            <div className="p-4 bg-blue-900/20 border border-blue-600/30 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Search className="h-4 w-4 text-blue-400" />
                <span className="font-medium text-blue-400 text-sm">📋 Topic Summary</span>
              </div>
              <p className="text-sm text-blue-300 leading-relaxed">
                {improvedContext.topicSummary}
              </p>
            </div>
          )}

          {/* Related Topics */}
          {improvedContext.relatedTopics && improvedContext.relatedTopics.length > 0 && (
            <div className="space-y-2">
              <Button
                onClick={() => toggleSection("related")}
                variant="ghost"
                className="w-full justify-between p-0 h-auto text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)]"
              >
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-orange-400" />
                  <span className="font-medium">Related Topics</span>
                  <Badge variant="secondary" className="bg-orange-400/20 text-orange-300">
                    {improvedContext.relatedTopics.length}
                  </Badge>
                </div>
                {expandedSections.includes("related") ? 
                  <ChevronUp className="h-4 w-4" /> : 
                  <ChevronDown className="h-4 w-4" />
                }
              </Button>

              {expandedSections.includes("related") && (
                <div className="ml-6 flex flex-wrap gap-2">
                  {improvedContext.relatedTopics.slice(0, 8).map((topic, index) => (
                    <Badge 
                      key={index} 
                      variant="outline" 
                      className="text-xs border-orange-400/30 text-orange-300 bg-orange-400/10"
                    >
                      🔗 {topic}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Current Events */}
          {improvedContext.currentEvents && (
            <div className="space-y-2">
              <Button
                onClick={() => toggleSection("events")}
                variant="ghost"
                className="w-full justify-between p-0 h-auto text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)]"
              >
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-400" />
                  <span className="font-medium">Current Events</span>
                </div>
                {expandedSections.includes("events") ? 
                  <ChevronUp className="h-4 w-4" /> : 
                  <ChevronDown className="h-4 w-4" />
                }
              </Button>

              {expandedSections.includes("events") && (
                <div className="ml-6 space-y-3">
                  {improvedContext.currentEvents.recentNews && improvedContext.currentEvents.recentNews.length > 0 && (
                    <div>
                      <div className="text-xs font-medium text-blue-300 mb-1">📰 Recent News:</div>
                      <div className="flex flex-wrap gap-1">
                        {improvedContext.currentEvents.recentNews.slice(0, 5).map((news, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-blue-400/30 text-blue-300 bg-blue-400/10">
                            {news}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {improvedContext.currentEvents.trendingDiscussions && improvedContext.currentEvents.trendingDiscussions.length > 0 && (
                    <div>
                      <div className="text-xs font-medium text-blue-300 mb-1">🔥 Trending Discussions:</div>
                      <div className="flex flex-wrap gap-1">
                        {improvedContext.currentEvents.trendingDiscussions.slice(0, 5).map((discussion, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-blue-400/30 text-blue-300 bg-blue-400/10">
                            {discussion}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {improvedContext.currentEvents.keyDevelopments && improvedContext.currentEvents.keyDevelopments.length > 0 && (
                    <div>
                      <div className="text-xs font-medium text-blue-300 mb-1">⚡ Key Developments:</div>
                      <div className="flex flex-wrap gap-1">
                        {improvedContext.currentEvents.keyDevelopments.slice(0, 3).map((development, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-blue-400/30 text-blue-300 bg-blue-400/10">
                            {development}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Factual Context */}
          {improvedContext.factualContext && (
            <div className="space-y-2">
              <Button
                onClick={() => toggleSection("facts")}
                variant="ghost"
                className="w-full justify-between p-0 h-auto text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)]"
              >
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-purple-400" />
                  <span className="font-medium">Factual Context</span>
                </div>
                {expandedSections.includes("facts") ? 
                  <ChevronUp className="h-4 w-4" /> : 
                  <ChevronDown className="h-4 w-4" />
                }
              </Button>

              {expandedSections.includes("facts") && (
                <div className="ml-6 space-y-3">
                  {improvedContext.factualContext.keyFacts && improvedContext.factualContext.keyFacts.length > 0 && (
                    <div>
                      <div className="text-xs font-medium text-purple-300 mb-1">📊 Key Facts:</div>
                      <div className="flex flex-wrap gap-1">
                        {improvedContext.factualContext.keyFacts.slice(0, 5).map((fact, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-purple-400/30 text-purple-300 bg-purple-400/10">
                            {fact}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {improvedContext.factualContext.importantDates && improvedContext.factualContext.importantDates.length > 0 && (
                    <div>
                      <div className="text-xs font-medium text-purple-300 mb-1">📅 Important Dates:</div>
                      <div className="flex flex-wrap gap-1">
                        {improvedContext.factualContext.importantDates.slice(0, 5).map((date, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-purple-400/30 text-purple-300 bg-purple-400/10">
                            {date}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {improvedContext.factualContext.relevantNumbers && improvedContext.factualContext.relevantNumbers.length > 0 && (
                    <div>
                      <div className="text-xs font-medium text-purple-300 mb-1">🔢 Relevant Numbers:</div>
                      <div className="flex flex-wrap gap-1">
                        {improvedContext.factualContext.relevantNumbers.slice(0, 3).map((number, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-purple-400/30 text-purple-300 bg-purple-400/10">
                            {number}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Research Sources */}
          {improvedContext.researchSources && improvedContext.researchSources.length > 0 && (
            <div className="space-y-2">
              <Button
                onClick={() => toggleSection("sources")}
                variant="ghost"
                className="w-full justify-between p-0 h-auto text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)]"
              >
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-[var(--buddychip-accent)]" />
                  <span className="font-medium">Research Sources</span>
                  <Badge variant="secondary" className="bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                    {improvedContext.researchSources.length}
                  </Badge>
                </div>
                {expandedSections.includes("sources") ? 
                  <ChevronUp className="h-4 w-4" /> : 
                  <ChevronDown className="h-4 w-4" />
                }
              </Button>

              {expandedSections.includes("sources") && (
                <div className="ml-6 space-y-2">
                  {improvedContext.researchSources.slice(0, 5).map((source, index) => (
                    <div key={index} className="p-3 bg-[var(--buddychip-black)]/50 rounded border border-[var(--buddychip-grey-stroke)]">
                      <div className="flex items-start justify-between gap-2 mb-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{getSourceIcon(source.source)}</span>
                          <span className="text-xs font-medium text-[var(--buddychip-white)]">
                            {source.title}
                          </span>
                          <Badge variant="outline" className="text-xs border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                            {Math.round(source.relevanceScore * 100)}% relevant
                          </Badge>
                        </div>
                        {source.url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(source.url, '_blank')}
                            className="h-6 w-6 p-0 text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                      <p className="text-xs text-[var(--buddychip-grey-text)] line-clamp-2">
                        {source.snippet}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}