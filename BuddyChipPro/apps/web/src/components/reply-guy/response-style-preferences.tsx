import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Label } from "../ui/label";
import { Separator } from "../ui/separator";
import { Switch } from "../ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { ResponseStyleSelector } from "./response-style-selector";
import { ResponseStyle } from "../../types/responses";
import { 
  Settings, 
  Brain, 
  Shield, 
  TrendingUp,
  BarChart3,
  Save,
  RotateCcw
} from "lucide-react";
import { toast } from "sonner";

interface ResponseStylePreferencesProps {
  userId: string;
}

export function ResponseStylePreferences({ userId }: ResponseStylePreferencesProps) {
  const [defaultStyles, setDefaultStyles] = useState<ResponseStyle[]>(["professional"]);
  const [autoGenerateStyles, setAutoGenerateStyles] = useState(true);
  const [qualityThreshold, setQualityThreshold] = useState(0.7);
  const [riskTolerance, setRiskTolerance] = useState<"conservative" | "balanced" | "aggressive">("balanced");
  const [autoApproveThreshold, setAutoApproveThreshold] = useState(0.85);
  const [hasChanges, setHasChanges] = useState(false);

  // Get current user settings
  const userSettings = useQuery(api.settings.getUserSettings, { userId: userId as any });

  // Get style performance analytics
  const styleAnalytics = useQuery(api.responseQueries.getStylePerformanceAnalytics, {
    userId: userId as any,
    timeRange: "30d",
  });

  // Get overall response statistics
  const responseStats = useQuery(api.responseQueries.getResponseStatsByStyle, {
    userId: userId as any,
    timeRange: "30d",
  });

  // Mutations
  const updatePreferences = useMutation(api.responseMutations.updateResponsePreferences);
  const setPreferredStyle = useMutation(api.responseMutations.setUserPreferredStyle);

  const handleSavePreferences = async () => {
    try {
      await updatePreferences({
        userId: userId as any,
        preferences: {
          defaultStyles,
          autoGenerateStyles,
          qualityThreshold,
          riskTolerance,
          autoApproveThreshold,
        },
      });
      
      // Also update the primary preferred style
      if (defaultStyles.length > 0) {
        await setPreferredStyle({
          userId: userId as any,
          preferredStyle: defaultStyles[0],
        });
      }

      setHasChanges(false);
      toast.success("Preferences saved successfully");
    } catch (error) {
      toast.error("Failed to save preferences");
      console.error("Failed to save preferences:", error);
    }
  };

  const handleResetToDefaults = () => {
    setDefaultStyles(["professional"]);
    setAutoGenerateStyles(true);
    setQualityThreshold(0.7);
    setRiskTolerance("balanced");
    setAutoApproveThreshold(0.85);
    setHasChanges(true);
  };

  const markChanged = () => {
    setHasChanges(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Response Style Preferences
          </h2>
          <p className="text-muted-foreground">
            Configure how AI generates responses for mentions and tweets
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleResetToDefaults}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button 
            onClick={handleSavePreferences}
            disabled={!hasChanges}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Performance Overview */}
      {responseStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Total Responses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{responseStats.total}</div>
              <p className="text-xs text-muted-foreground">Last 30 days</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Average Quality</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(responseStats.qualityMetrics.averageQuality * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">Quality score</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Low Risk</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{responseStats.riskDistribution.low}</div>
              <p className="text-xs text-muted-foreground">Safe responses</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Approved Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {responseStats.total > 0 
                  ? Math.round((responseStats.byStatus.approved / responseStats.total) * 100) 
                  : 0}%
              </div>
              <p className="text-xs text-muted-foreground">Auto-approved</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Default Styles Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Default Response Styles
          </CardTitle>
          <CardDescription>
            Choose which styles to generate automatically for new mentions and tweets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponseStyleSelector
            selectedStyles={defaultStyles}
            onStylesChange={(styles) => {
              setDefaultStyles(styles);
              markChanged();
            }}
            multiple={true}
            showPreferences={false}
          />
        </CardContent>
      </Card>

      {/* Generation Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Generation Settings</CardTitle>
          <CardDescription>
            Configure how and when responses are automatically generated
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Auto-generate responses</Label>
              <div className="text-sm text-muted-foreground">
                Automatically generate responses for high-priority mentions
              </div>
            </div>
            <Switch
              checked={autoGenerateStyles}
              onCheckedChange={(checked) => {
                setAutoGenerateStyles(checked);
                markChanged();
              }}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <Label>Quality Threshold</Label>
            <div className="text-sm text-muted-foreground mb-2">
              Minimum quality score for responses (0-100%). Lower threshold generates more responses.
            </div>
            <Select
              value={qualityThreshold.toString()}
              onValueChange={(value) => {
                setQualityThreshold(parseFloat(value));
                markChanged();
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0.5">50% - Lower quality, more responses</SelectItem>
                <SelectItem value="0.6">60% - Balanced</SelectItem>
                <SelectItem value="0.7">70% - Good quality (recommended)</SelectItem>
                <SelectItem value="0.8">80% - High quality</SelectItem>
                <SelectItem value="0.9">90% - Premium quality only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Risk Tolerance
            </Label>
            <div className="text-sm text-muted-foreground mb-2">
              How cautious should AI be when generating responses?
            </div>
            <Select
              value={riskTolerance}
              onValueChange={(value: any) => {
                setRiskTolerance(value);
                markChanged();
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="conservative">
                  <div className="space-y-1">
                    <div>Conservative</div>
                    <div className="text-xs text-muted-foreground">Very safe, formal responses only</div>
                  </div>
                </SelectItem>
                <SelectItem value="balanced">
                  <div className="space-y-1">
                    <div>Balanced</div>
                    <div className="text-xs text-muted-foreground">Safe with some personality (recommended)</div>
                  </div>
                </SelectItem>
                <SelectItem value="aggressive">
                  <div className="space-y-1">
                    <div>Aggressive</div>
                    <div className="text-xs text-muted-foreground">More creative, higher engagement potential</div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div className="space-y-3">
            <Label>Auto-Approval Threshold</Label>
            <div className="text-sm text-muted-foreground mb-2">
              Responses above this quality score will be automatically approved for posting
            </div>
            <Select
              value={autoApproveThreshold.toString()}
              onValueChange={(value) => {
                setAutoApproveThreshold(parseFloat(value));
                markChanged();
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">Never auto-approve</SelectItem>
                <SelectItem value="0.75">75% - Moderate threshold</SelectItem>
                <SelectItem value="0.8">80% - Balanced (recommended)</SelectItem>
                <SelectItem value="0.85">85% - High confidence</SelectItem>
                <SelectItem value="0.9">90% - Very high confidence only</SelectItem>
                <SelectItem value="0.95">95% - Near perfect only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Style Performance Analytics */}
      {styleAnalytics && styleAnalytics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Style Performance Analytics
            </CardTitle>
            <CardDescription>
              How each response style has performed over the last 30 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {styleAnalytics.map((style: any) => (
                <div key={style.style} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium capitalize">{style.style}</h4>
                      <Badge variant="secondary">
                        {style.totalResponses} responses
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">
                        {Math.round(style.averageEngagement)} avg engagement
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Quality Score</div>
                      <div className="font-medium">
                        {Math.round(style.averageQuality * 100)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Approval Rate</div>
                      <div className="font-medium">
                        {Math.round(style.approvalRate * 100)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Total Engagement</div>
                      <div className="font-medium">
                        {style.totalEngagement}
                      </div>
                    </div>
                  </div>
                  
                  {style.topResponse && (
                    <div className="mt-3 p-2 bg-muted rounded text-xs">
                      <div className="font-medium mb-1">Top performing response:</div>
                      <div className="line-clamp-2">{style.topResponse.content}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Changes Footer */}
      {hasChanges && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium">You have unsaved changes</span>
              </div>
              <Button onClick={handleSavePreferences}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}