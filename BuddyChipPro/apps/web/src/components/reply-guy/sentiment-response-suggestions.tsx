import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { 
  Lightbulb, 
  MessageSquare, 
  Copy, 
  Check,
  Sparkles,
  Target,
  Heart,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Zap,
  BookOpen,
  Users
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SentimentResponseSuggestionsProps {
  mentionId: string;
  className?: string;
}

export function SentimentResponseSuggestions({ mentionId, className }: SentimentResponseSuggestionsProps) {
  const [copiedSuggestion, setCopiedSuggestion] = useState<string | null>(null);

  // Get sentiment-based response suggestions
  const suggestionsData = useQuery(
    api.mentions.sentimentAnalytics.getSentimentBasedResponseSuggestions,
    { mentionId: mentionId as any }
  );

  const handleCopySuggestion = async (template: string, suggestionId: string) => {
    try {
      await navigator.clipboard.writeText(template);
      setCopiedSuggestion(suggestionId);
      setTimeout(() => setCopiedSuggestion(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const getStrategyIcon = (strategy: string) => {
    switch (strategy) {
      case "amplify": return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "engage": return <Heart className="h-4 w-4 text-pink-500" />;
      case "support": return <Users className="h-4 w-4 text-blue-500" />;
      case "address_concerns": return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case "provide_perspective": return <BarChart3 className="h-4 w-4 text-purple-500" />;
      case "acknowledge": return <Check className="h-4 w-4 text-cyan-500" />;
      case "educate": return <BookOpen className="h-4 w-4 text-indigo-500" />;
      case "engage_discussion": return <MessageSquare className="h-4 w-4 text-gray-400" />;
      case "cautious": return <TrendingDown className="h-4 w-4 text-yellow-500" />;
      default: return <Lightbulb className="h-4 w-4 text-[#316FE3]" />;
    }
  };

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case "amplify": return "border-green-500/30 bg-green-500/10";
      case "engage": return "border-pink-500/30 bg-pink-500/10";
      case "support": return "border-blue-500/30 bg-blue-500/10";
      case "address_concerns": return "border-orange-500/30 bg-orange-500/10";
      case "provide_perspective": return "border-purple-500/30 bg-purple-500/10";
      case "acknowledge": return "border-cyan-500/30 bg-cyan-500/10";
      case "educate": return "border-indigo-500/30 bg-indigo-500/10";
      case "engage_discussion": return "border-gray-500/30 bg-gray-500/10";
      case "cautious": return "border-yellow-500/30 bg-yellow-500/10";
      default: return "border-[#316FE3]/30 bg-[#316FE3]/10";
    }
  };

  const getToneBadgeColor = (tone: string) => {
    switch (tone) {
      case "celebratory": return "bg-green-600 text-green-100";
      case "enthusiastic": return "bg-pink-600 text-pink-100";
      case "encouraging": return "bg-blue-600 text-blue-100";
      case "empathetic": return "bg-orange-600 text-orange-100";
      case "balanced": return "bg-purple-600 text-purple-100";
      case "respectful": return "bg-cyan-600 text-cyan-100";
      case "informative": return "bg-indigo-600 text-indigo-100";
      case "conversational": return "bg-gray-600 text-gray-100";
      case "careful": return "bg-yellow-600 text-yellow-100";
      default: return "bg-[#316FE3] text-blue-100";
    }
  };

  if (!suggestionsData) {
    return (
      <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
        <CardContent className="py-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin h-6 w-6 border-2 border-[#316FE3] border-t-transparent rounded-full mr-3"></div>
            <span className="text-[#6E7A8C]">Generating response suggestions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!suggestionsData.suggestions || suggestionsData.suggestions.length === 0) {
    return (
      <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
        <CardContent className="py-8">
          <div className="text-center">
            <MessageSquare className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No suggestions available</h3>
            <p className="text-[#6E7A8C]">
              {suggestionsData.reasoning || "Unable to generate response suggestions for this mention"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-[#316FE3]" />
            <CardTitle className="text-[#F5F7FA]">AI Response Suggestions</CardTitle>
            <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
              {suggestionsData.suggestions.length} strategies
            </Badge>
          </div>
          <Badge className={cn("text-xs", getToneBadgeColor("default"))}>
            {suggestionsData.recommendedStrategy}
          </Badge>
        </div>
        <CardDescription className="text-[#6E7A8C]">
          {suggestionsData.reasoning}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Recommended Strategy */}
        <div className="space-y-4">
          {suggestionsData.suggestions.map((suggestion: any, index: number) => {
            const suggestionId = `${suggestion.strategy}-${index}`;
            const isRecommended = index === 0;
            
            return (
              <div 
                key={suggestionId}
                className={cn(
                  "p-4 rounded-lg border transition-all duration-200 hover:border-opacity-60",
                  getStrategyColor(suggestion.strategy),
                  isRecommended && "ring-1 ring-[#316FE3]/30"
                )}
              >
                <div className="space-y-3">
                  {/* Strategy Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStrategyIcon(suggestion.strategy)}
                      <div>
                        <h4 className="text-sm font-semibold text-[#F5F7FA] capitalize">
                          {suggestion.strategy.replace(/_/g, ' ')} Strategy
                        </h4>
                        {isRecommended && (
                          <div className="flex items-center gap-1 mt-1">
                            <Target className="h-3 w-3 text-[#316FE3]" />
                            <span className="text-xs text-[#316FE3] font-medium">Recommended</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={cn("text-xs", getToneBadgeColor(suggestion.tone))}>
                        {suggestion.tone}
                      </Badge>
                      {suggestion.urgency === "immediate" && (
                        <Badge variant="destructive" className="text-xs">
                          <Zap className="h-3 w-3 mr-1" />
                          Urgent
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Response Template */}
                  <div className="bg-[#000000]/60 rounded-lg p-3 border border-[#202631]">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1">
                        <div className="text-xs text-[#6E7A8C] mb-2 flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          Suggested Response:
                        </div>
                        <p className="text-sm text-[#F5F7FA] leading-relaxed">
                          {suggestion.template}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopySuggestion(suggestion.template, suggestionId)}
                        className="text-[#6E7A8C] hover:text-[#F5F7FA] p-2"
                      >
                        {copiedSuggestion === suggestionId ? (
                          <Check className="h-4 w-4 text-green-400" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Reasoning */}
                  <div className="text-xs text-[#6E7A8C] bg-[#000000]/40 rounded px-3 py-2 border border-[#202631]/50">
                    <span className="font-medium">Reasoning:</span> {suggestion.reasoning}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Usage Tips */}
        <div className="pt-4 border-t border-[#202631]">
          <div className="bg-[#316FE3]/10 border border-[#316FE3]/30 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <Lightbulb className="h-4 w-4 text-[#316FE3] mt-0.5" />
              <div className="text-xs text-[#6E7A8C]">
                <span className="font-medium text-[#F5F7FA]">Pro tip:</span> These suggestions are based on sentiment analysis. 
                Feel free to customize them to match your brand voice and add specific context relevant to your project.
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}