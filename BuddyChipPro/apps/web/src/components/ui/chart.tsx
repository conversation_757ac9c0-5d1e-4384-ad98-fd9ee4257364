import { cn } from "../../lib/utils";

interface ChartProps {
  data: Array<{ label: string; value: number; color?: string }>;
  className?: string;
  type?: "bar" | "donut" | "line";
}

export function Chart({ data, className, type = "bar" }: ChartProps) {
  const maxValue = Math.max(...data.map(item => item.value));

  if (type === "donut") {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;

    return (
      <div className={cn("flex items-center justify-center", className)}>
        <div className="relative w-32 h-32">
          <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
            {data.map((item, index) => {
              const percentage = (item.value / total) * 100;
              const strokeDasharray = `${percentage} ${100 - percentage}`;
              const strokeDashoffset = -currentAngle;
              currentAngle += percentage;

              return (
                <circle
                  key={index}
                  cx="50"
                  cy="50"
                  r="40"
                  fill="transparent"
                  stroke={item.color || `hsl(${index * 137.5}, 70%, 50%)`}
                  strokeWidth="8"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  className="transition-all duration-300"
                />
              );
            })}
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-lg font-bold text-[#F5F7FA]">{total}</div>
              <div className="text-xs text-[#6E7A8C]">Total</div>
            </div>
          </div>
        </div>
        <div className="ml-4 space-y-2">
          {data.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: item.color || `hsl(${index * 137.5}, 70%, 50%)` }}
              />
              <span className="text-sm text-[#F5F7FA]">{item.label}</span>
              <span className="text-sm text-[#6E7A8C]">({item.value})</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {data.map((item, index) => (
        <div key={index} className="space-y-1">
          <div className="flex items-center justify-between text-sm">
            <span className="text-[#F5F7FA]">{item.label}</span>
            <span className="text-[#6E7A8C]">{item.value}</span>
          </div>
          <div className="w-full bg-[#202631] rounded-full h-2">
            <div
              className="h-2 rounded-full transition-all duration-500 ease-out"
              style={{
                width: `${(item.value / maxValue) * 100}%`,
                backgroundColor: item.color || `hsl(${index * 137.5}, 70%, 50%)`
              }}
            />
          </div>
        </div>
      ))}
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    trend: "up" | "down" | "neutral";
  };
  icon?: React.ReactNode;
  className?: string;
}

export function MetricCard({ title, value, change, icon, className }: MetricCardProps) {
  return (
    <div className={cn(
      "bg-[#000000]/60 border border-[#202631] rounded-lg p-4 backdrop-blur-sm hover:bg-[#000000]/80 transition-all duration-200",
      className
    )}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-[#6E7A8C]">{title}</p>
          <p className="text-2xl font-bold text-[#F5F7FA]">{value}</p>
          {change && (
            <div className={cn(
              "flex items-center gap-1 text-xs mt-1",
              change.trend === "up" && "text-green-400",
              change.trend === "down" && "text-red-400",
              change.trend === "neutral" && "text-[#6E7A8C]"
            )}>
              <span>{change.trend === "up" ? "↗" : change.trend === "down" ? "↘" : "→"}</span>
              <span>{Math.abs(change.value)}%</span>
            </div>
          )}
        </div>
        {icon && (
          <div className="text-[#316FE3] opacity-80">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
}