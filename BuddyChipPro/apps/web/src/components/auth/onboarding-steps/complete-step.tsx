import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { CelebrationAnimation } from "@/components/auth/celebration-animation";
import { CheckCircle, Sparkles, ArrowRight, Twitter, Zap, Users } from "lucide-react";

export function CompleteStep() {
  const [showCelebration, setShowCelebration] = useState(true);
  const [redirectCountdown, setRedirectCountdown] = useState(5);
  const navigate = useNavigate();

  console.log("🎉 Complete step rendered - Starting celebration");

  useEffect(() => {
    // Countdown timer for redirect
    const countdownInterval = setInterval(() => {
      setRedirectCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          navigate({ to: "/dashboard" });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(countdownInterval);
  }, [navigate]);

  const handleGoToDashboard = () => {
    console.log("🚀 User clicked Go to Dashboard");
    navigate({ to: "/dashboard" });
  };

  const completedFeatures = [
    {
      icon: CheckCircle,
      title: "Profile Created",
      description: "Your account is ready to go"
    },
    {
      icon: Twitter,
      title: "Twitter Connected",
      description: "Ready to monitor mentions"
    },
    {
      icon: Zap,
      title: "AI Assistant Active",
      description: "Smart replies are enabled"
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="space-y-8 text-center p-6"
    >
      {/* Celebration Animation */}
      <CelebrationAnimation
        show={showCelebration}
        type="confetti"
        duration={4000}
        onComplete={() => setShowCelebration(false)}
        message="Welcome to BuddyChip!"
        subMessage="Your account is ready to go"
      />

      {/* Success Icon */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{
          delay: 0.3,
          type: "spring",
          stiffness: 200,
          damping: 20
        }}
        className="flex justify-center"
      >
        <div className="relative">
          <div className="w-24 h-24 bg-gradient-to-br from-[#3fb950] to-[#2ea043] rounded-full flex items-center justify-center shadow-2xl">
            <CheckCircle className="w-12 h-12 text-white" />
          </div>

          {/* Success pulse effect */}
          <motion.div
            animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="absolute inset-0 rounded-full border-4 border-[#3fb950]"
          />
        </div>
      </motion.div>

      {/* Success Message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="space-y-4"
      >
        <h2 className="text-4xl font-bold text-[#F5F7FA] tracking-tight">
          You're all set!{" "}
          <motion.span
            animate={{ rotate: [0, 15, -15, 0] }}
            transition={{ duration: 0.5, delay: 1, repeat: 3 }}
            className="inline-block"
          >
            🎉
          </motion.span>
        </h2>

        <p className="text-lg text-[#6E7A8C] leading-relaxed max-w-md mx-auto">
          Welcome aboard! Your BuddyChip account is ready and your{" "}
          <span className="text-[#316FE3] font-semibold">Yap Journey</span> begins now.
        </p>
      </motion.div>

      {/* Feature Completion List */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-[#0E1117] border border-[#202631] rounded-xl p-6 max-w-sm mx-auto"
      >
        <h3 className="text-[#F5F7FA] font-semibold mb-4 flex items-center justify-center">
          <Sparkles className="w-5 h-5 mr-2 text-[#316FE3]" />
          Setup Complete
        </h3>

        <div className="space-y-3">
          {completedFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8 + index * 0.1 }}
              className="flex items-center space-x-3"
            >
              <div className="w-8 h-8 bg-[#3fb950]/10 rounded-lg flex items-center justify-center">
                <feature.icon className="w-4 h-4 text-[#3fb950]" />
              </div>
              <div className="text-left">
                <p className="font-medium text-[#F5F7FA] text-sm">
                  {feature.title}
                </p>
                <p className="text-[#6E7A8C] text-xs">
                  {feature.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
        className="space-y-4"
      >
        <Button
          onClick={handleGoToDashboard}
          className="w-full bg-gradient-to-r from-[#316FE3] to-[#2563eb] hover:from-[#2563eb] hover:to-[#1d4ed8] text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <Users className="w-5 h-5 mr-2" />
          Go to Dashboard
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>

        <motion.p
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-xs text-[#6E7A8C]"
        >
          Redirecting automatically in {redirectCountdown} seconds...
        </motion.p>
      </motion.div>

      {/* Quick Tips */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
        className="bg-[#316FE3]/5 border border-[#316FE3]/20 rounded-lg p-4 max-w-md mx-auto"
      >
        <h4 className="text-[#316FE3] font-semibold text-sm mb-2">
          💡 Quick Tip
        </h4>
        <p className="text-[#6E7A8C] text-xs leading-relaxed">
          Start by connecting your Twitter account in the dashboard to begin monitoring mentions and getting AI-powered assistance with your tweets.
        </p>
      </motion.div>
    </motion.div>
  );
}