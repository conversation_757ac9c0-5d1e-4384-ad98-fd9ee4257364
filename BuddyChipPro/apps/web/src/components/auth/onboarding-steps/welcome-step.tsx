import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Sparkles, Twitter, Zap, Users, TrendingUp } from "lucide-react";
import { useState, useEffect, createElement } from "react";

interface WelcomeStepProps {
  onNext: () => void;
}

export function WelcomeStep({ onNext }: WelcomeStepProps) {
  const [currentFeature, setCurrentFeature] = useState(0);

  console.log("🎉 Welcome step rendered");

  const features = [
    {
      icon: Twitter,
      title: "AI-Powered Twitter Assistant",
      description: "Get intelligent help with your tweets and replies"
    },
    {
      icon: Zap,
      title: "Real-time Mention Monitoring",
      description: "Never miss important conversations about you"
    },
    {
      icon: Users,
      title: "Smart Engagement",
      description: "Build meaningful connections with your audience"
    },
    {
      icon: TrendingUp,
      title: "Growth Analytics",
      description: "Track your progress and optimize your strategy"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [features.length]);

  const handleGetStarted = () => {
    console.log("🚀 User clicked Get Started");
    onNext();
  };

  return (
    <div className="space-y-8 text-center p-6">
      {/* Animated logo/icon */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{
          type: "spring",
          stiffness: 200,
          damping: 20,
          delay: 0.2
        }}
        className="flex justify-center"
      >
        <div className="relative">
          <div className="w-20 h-20 bg-gradient-to-br from-[#316FE3] to-[#2563eb] rounded-2xl flex items-center justify-center shadow-2xl">
            <Sparkles className="w-10 h-10 text-white" />
          </div>

          {/* Floating particles */}
          <motion.div
            animate={{
              y: [-10, -20, -10],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: 0.5
            }}
            className="absolute -top-2 -right-2 w-4 h-4 bg-[#3fb950] rounded-full"
          />
          <motion.div
            animate={{
              y: [-5, -15, -5],
              opacity: [0.3, 0.8, 0.3]
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              delay: 1
            }}
            className="absolute -bottom-1 -left-3 w-3 h-3 bg-[#d29922] rounded-full"
          />
        </div>
      </motion.div>

      {/* Welcome message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-4"
      >
        <h1 className="text-4xl font-bold text-[#F5F7FA] tracking-tight">
          Welcome to{" "}
          <span className="bg-gradient-to-r from-[#316FE3] to-[#2563eb] bg-clip-text text-transparent">
            BuddyChip
          </span>
          !
        </h1>

        <p className="text-lg text-[#6E7A8C] leading-relaxed max-w-md mx-auto">
          Your AI-powered companion for Twitter success. Let's set up your account and start your{" "}
          <span className="text-[#316FE3] font-semibold">Yap Journey</span>.
        </p>
      </motion.div>

      {/* Feature showcase */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-[#0E1117] border border-[#202631] rounded-xl p-6 max-w-sm mx-auto"
      >
        <motion.div
          key={currentFeature}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.5 }}
          className="flex items-center space-x-4"
        >
          <div className="w-12 h-12 bg-[#316FE3]/10 rounded-lg flex items-center justify-center">
            {createElement(features[currentFeature].icon, {
              className: "w-6 h-6 text-[#316FE3]"
            })}
          </div>
          <div className="text-left">
            <h3 className="font-semibold text-[#F5F7FA] text-sm">
              {features[currentFeature].title}
            </h3>
            <p className="text-[#6E7A8C] text-xs">
              {features[currentFeature].description}
            </p>
          </div>
        </motion.div>

        {/* Feature indicators */}
        <div className="flex justify-center space-x-2 mt-4">
          {features.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentFeature ? "bg-[#316FE3] scale-125" : "bg-[#202631]"
              }`}
            />
          ))}
        </div>
      </motion.div>

      {/* Call to action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="space-y-4"
      >
        <Button
          onClick={handleGetStarted}
          className="w-full bg-gradient-to-r from-[#316FE3] to-[#2563eb] hover:from-[#2563eb] hover:to-[#1d4ed8] text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <Sparkles className="w-5 h-5 mr-2" />
          Get Started
        </Button>

        <p className="text-xs text-[#6E7A8C]">
          Takes less than 2 minutes to set up
        </p>
      </motion.div>
    </div>
  );
}