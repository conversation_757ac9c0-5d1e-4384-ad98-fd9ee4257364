import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { User, Camera, Sparkles, ArrowLeft, ArrowRight } from "lucide-react";
import { useState } from "react";
import { profileSetupSchema, type ProfileSetupFormData } from "@/lib/auth-schemas";
import { EnhancedFormField } from "@/components/auth/enhanced-form-field";
import { formatDisplayName } from "@/lib/auth-utils";

interface ProfileSetupStepProps {
  onNext: () => void;
  onBack: () => void;
}

export function ProfileSetupStep({ onNext, onBack }: ProfileSetupStepProps) {
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  console.log("👤 Profile setup step rendered");

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm({
    resolver: zodResolver(profileSetupSchema),
    defaultValues: {
      displayName: "",
      bio: "",
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: "en",
    },
  });

  const displayName = watch("displayName");

  const onSubmit = async (data: ProfileSetupFormData) => {
    console.log("💾 Saving profile data:", data);

    try {
      // TODO: Call mutation to update user profile
      // await updateUserProfile(data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log("✅ Profile saved successfully");
      onNext();
    } catch (error) {
      console.error("❌ Failed to save profile:", error);
    }
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log("📸 Uploading avatar:", file.name);
    setIsUploading(true);

    try {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // TODO: Upload to your storage service
      // const avatarUrl = await uploadAvatar(file);
      // setValue("avatar", avatarUrl);

      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log("✅ Avatar uploaded successfully");
    } catch (error) {
      console.error("❌ Failed to upload avatar:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDisplayNameChange = (value: string) => {
    setValue("displayName", value);
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-8 p-6"
    >
      {/* Header */}
      <div className="text-center space-y-3">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="w-16 h-16 bg-gradient-to-br from-[#316FE3] to-[#2563eb] rounded-full flex items-center justify-center mx-auto shadow-lg"
        >
          <User className="w-8 h-8 text-white" />
        </motion.div>

        <h2 className="text-2xl font-bold text-[#F5F7FA]">Set up your profile</h2>
        <p className="text-[#6E7A8C] text-sm leading-relaxed">
          Tell us a bit about yourself to personalize your experience
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Avatar Upload */}
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="w-24 h-24 bg-[#202631] border-2 border-dashed border-[#484f58] rounded-full flex items-center justify-center overflow-hidden">
              {avatarPreview ? (
                <img
                  src={avatarPreview}
                  alt="Avatar preview"
                  className="w-full h-full object-cover"
                />
              ) : (
                <Camera className="w-8 h-8 text-[#6E7A8C]" />
              )}
            </div>

            <input
              type="file"
              accept="image/*"
              onChange={handleAvatarUpload}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={isUploading}
            />

            {isUploading && (
              <div className="absolute inset-0 bg-[#000000]/50 rounded-full flex items-center justify-center">
                <div className="w-6 h-6 border-2 border-[#316FE3] border-t-transparent rounded-full animate-spin" />
              </div>
            )}
          </div>

          <p className="text-xs text-[#6E7A8C] text-center">
            Click to upload a profile picture<br />
            <span className="text-[#484f58]">JPG, PNG up to 5MB</span>
          </p>
        </div>

        {/* Display Name */}
        <EnhancedFormField
          id="displayName"
          label="Display Name"
          type="text"
          placeholder="How should we call you?"
          value={displayName}
          onChange={handleDisplayNameChange}
          error={errors.displayName?.message}
          hint="This is how others will see your name"
          required
        />

        {/* Bio */}
        <div className="space-y-2">
          <label htmlFor="bio" className="text-[#F5F7FA] font-medium text-sm">
            Bio <span className="text-[#6E7A8C] font-normal">(optional)</span>
          </label>
          <textarea
            id="bio"
            placeholder="Tell us about yourself..."
            {...register("bio")}
            className="w-full h-20 bg-[#0E1117] border border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C] rounded-lg px-3 py-2 transition-all duration-200 focus:border-[#316FE3] focus:ring-2 focus:ring-[#316FE3]/20 resize-none"
            maxLength={160}
          />
          <div className="flex justify-between text-xs text-[#6E7A8C]">
            <span>Share what makes you unique</span>
            <span>{watch("bio")?.length || 0}/160</span>
          </div>
          {errors.bio && (
            <p className="text-[#f85149] text-sm">{errors.bio.message}</p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            className="flex-1 border border-[#202631] hover:border-[#484f58] hover:bg-[#0E1117]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>

          <Button
            type="submit"
            disabled={isSubmitting || isUploading}
            className="flex-1 bg-gradient-to-r from-[#316FE3] to-[#2563eb] hover:from-[#2563eb] hover:to-[#1d4ed8] text-white font-semibold transition-all duration-300 transform hover:scale-105"
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </form>
    </motion.div>
  );
}