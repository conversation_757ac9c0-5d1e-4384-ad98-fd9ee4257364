import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { useUser } from "@clerk/clerk-react";

export function AuthTestPanel() {
  const { user, isSignedIn } = useUser();
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  
  // Test queries
  const authHealthCheck = useQuery(api.debug.authDebugging.authHealthCheck, {});
  const testAuthContext = useQuery(api.debug.authDebugging.testAuthContext, {});
  const authenticatedQuery = useQuery(api.mentions.mentionQueries.getUserMentionStats, {});
  const bypassQuery = useQuery(api.mentions.mentionQueries.getUserStatsNoAuth, {}); // Temporary for comparison
  
  const addTestResult = (name: string, success: boolean, details: any) => {
    const result = {
      name,
      success,
      details,
      timestamp: new Date().toISOString(),
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10
  };

  const runComprehensiveTest = async () => {
    setIsRunning(true);
    addTestResult("Test Started", true, "Running comprehensive authentication test");

    try {
      // Test 1: Basic auth context
      addTestResult(
        "Auth Context",
        !!testAuthContext?.success,
        testAuthContext?.authState || "No auth context"
      );

      // Test 2: Health check
      addTestResult(
        "Health Check",
        authHealthCheck?.overallStatus?.includes("HEALTHY") || false,
        authHealthCheck?.summary || "No health data"
      );

      // Test 3: Authenticated vs Bypass queries
      addTestResult(
        "Authenticated Query",
        !!authenticatedQuery && Object.keys(authenticatedQuery).length > 0,
        authenticatedQuery || "No data returned"
      );

      addTestResult(
        "Bypass Query", 
        !!bypassQuery && Object.keys(bypassQuery).length > 0,
        bypassQuery || "No data returned"
      );

      // Test 4: JWT Token validation
      if (isSignedIn) {
        try {
          const tokenTest = await fetch('/api/test-jwt'); // Would need to implement
          addTestResult(
            "JWT Token Test",
            tokenTest.ok,
            `Status: ${tokenTest.status}`
          );
        } catch (error) {
          addTestResult(
            "JWT Token Test",
            false,
            `Error: ${error instanceof Error ? error.message : String(error)}`
          );
        }
      }

      addTestResult("Test Completed", true, "All tests finished");
    } catch (error) {
      addTestResult(
        "Test Error",
        false,
        error instanceof Error ? error.message : String(error)
      );
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusBadge = (success: boolean) => (
    <Badge variant={success ? "default" : "destructive"} className="text-xs">
      {success ? "✅ PASS" : "❌ FAIL"}
    </Badge>
  );

  return (
    <Card className="p-6 bg-[#000000]/40 border-[#202631]">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-[#F5F7FA]">🧪 Authentication Test Panel</h3>
          <Button 
            onClick={runComprehensiveTest}
            disabled={isRunning}
            className="bg-[#059669] hover:bg-[#047857]"
          >
            {isRunning ? "Running Tests..." : "Run Full Test"}
          </Button>
        </div>

        {/* Real-time Status */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-1">Auth Context</div>
            {getStatusBadge(!!testAuthContext?.success)}
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-1">Health Check</div>
            {getStatusBadge(authHealthCheck?.overallStatus?.includes("HEALTHY") || false)}
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-1">Authenticated</div>
            {getStatusBadge(!!authenticatedQuery && Object.keys(authenticatedQuery).length > 0)}
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-1">Bypass Query</div>
            {getStatusBadge(!!bypassQuery && Object.keys(bypassQuery).length > 0)}
          </div>
        </div>

        {/* Data Comparison */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-[#F5F7FA]">Authenticated Query Results</h4>
            <div className="bg-[#202631] p-3 rounded border text-xs">
              <pre className="text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(authenticatedQuery, null, 2) || "No data"}
              </pre>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-[#F5F7FA]">Bypass Query Results</h4>
            <div className="bg-[#202631] p-3 rounded border text-xs">
              <pre className="text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(bypassQuery, null, 2) || "No data"}
              </pre>
            </div>
          </div>
        </div>

        {/* Test Results Log */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-[#F5F7FA]">Test Results</h4>
            <div className="bg-[#202631] p-3 rounded border max-h-48 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between text-xs mb-1">
                  <span className="text-gray-300">{result.name}</span>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(result.success)}
                    <span className="text-gray-500">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Migration Status */}
        <div className="border-t border-gray-700 pt-4">
          <h4 className="font-medium text-[#F5F7FA] mb-2">Migration Status</h4>
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-400">Currently Using:</span>
              <span className="text-yellow-400">Bypass Queries (Temporary)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Target:</span>
              <span className="text-green-400">Authenticated Queries</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Ready for Migration:</span>
              <span className={
                (!!authenticatedQuery && Object.keys(authenticatedQuery).length > 0) 
                  ? "text-green-400" : "text-red-400"
              }>
                {(!!authenticatedQuery && Object.keys(authenticatedQuery).length > 0) 
                  ? "✅ Ready" : "❌ Not Ready"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}