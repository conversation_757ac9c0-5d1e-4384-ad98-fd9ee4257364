import { useUser, useAuth as useClerkAuth } from "@clerk/clerk-react";
import { useConvexAuth } from "convex/react";
import { useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { ReactNode, useEffect, useState } from "react";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import Loader from "../loader";
import { AuthErrorBoundary } from "./auth-error-boundary";

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  requireFullAuth?: boolean;
}

interface AuthState {
  clerkLoaded: boolean;
  clerkSignedIn: boolean;
  convexLoaded: boolean;
  convexAuthenticated: boolean;
  userSynced: boolean;
  error?: string;
}

export function EnhancedAuthGuard({ 
  children, 
  fallback, 
  requireFullAuth = true 
}: AuthGuardProps) {
  const { user, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const clerkAuth = useClerkAuth();
  const { isAuthenticated: convexAuth, isLoading: convexLoading } = useConvexAuth();
  const [authState, setAuthState] = useState<AuthState>({
    clerkLoaded: false,
    clerkSignedIn: false,
    convexLoaded: false,
    convexAuthenticated: false,
    userSynced: false,
  });
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  
  const createOrUpdateUser = useMutation(api.users.createOrUpdateUser);

  // Update auth state
  useEffect(() => {
    setAuthState({
      clerkLoaded,
      clerkSignedIn: isSignedIn || false,
      convexLoaded: !convexLoading,
      convexAuthenticated: convexAuth || false,
      userSynced: false, // Will be updated by user sync effect
    });
  }, [clerkLoaded, isSignedIn, convexLoading, convexAuth]);

  // User synchronization
  useEffect(() => {
    if (isSignedIn && user && convexAuth && clerkLoaded && !convexLoading) {
      console.log("🔄 Syncing user to Convex...");
      
      createOrUpdateUser({
        email: user.primaryEmailAddress?.emailAddress || "",
        name: user.fullName || user.firstName || user.username || "User",
        clerkId: user.id,
        image: user.imageUrl,
      })
        .then((result: unknown) => {
          console.log("✅ User synced to Convex:", result);
          setAuthState(prev => ({ ...prev, userSynced: true }));
        })
        .catch((error) => {
          console.error("❌ User sync failed:", error);
          setAuthState(prev => ({ 
            ...prev, 
            error: `User sync failed: ${error.message}`,
            userSynced: false 
          }));
        });
    }
  }, [isSignedIn, user, convexAuth, clerkLoaded, convexLoading, createOrUpdateUser]);

  // Retry mechanism
  const handleRetry = async () => {
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    try {
      // Force token refresh
      if (clerkAuth.getToken) {
        await clerkAuth.getToken({ template: "convex", skipCache: true });
      }
      
      // Clear error state
      setAuthState(prev => ({ ...prev, error: undefined }));
      
      // Wait a moment for state to update
      setTimeout(() => {
        setIsRetrying(false);
      }, 1000);
    } catch (error) {
      console.error("Retry failed:", error);
      setAuthState(prev => ({ 
        ...prev, 
        error: `Retry failed: ${error instanceof Error ? error.message : String(error)}` 
      }));
      setIsRetrying(false);
    }
  };

  // Loading state
  if (!authState.clerkLoaded || (!authState.convexLoaded && requireFullAuth) || isRetrying) {
    return fallback || (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="p-8 bg-[#000000]/40 border-[#202631]">
          <div className="text-center space-y-4">
            <Loader />
            <h3 className="text-lg font-medium text-[#F5F7FA]">
              {isRetrying ? "Retrying Authentication..." : "Loading Authentication..."}
            </h3>
            <div className="text-sm text-gray-400 space-y-1">
              <div className={authState.clerkLoaded ? "text-green-400" : "text-yellow-400"}>
                ✓ Clerk: {authState.clerkLoaded ? "Loaded" : "Loading..."}
              </div>
              {requireFullAuth && (
                <div className={authState.convexLoaded ? "text-green-400" : "text-yellow-400"}>
                  ✓ Convex: {authState.convexLoaded ? "Connected" : "Connecting..."}
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // Not signed in
  if (!authState.clerkSignedIn) {
    return fallback || (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="max-w-md w-full p-8 bg-[#000000]/40 border-[#202631]">
          <div className="text-center space-y-4">
            <div className="text-6xl mb-4">🔐</div>
            <h2 className="text-xl font-bold text-[#F5F7FA]">Authentication Required</h2>
            <p className="text-gray-300">
              You need to sign in to access this content.
            </p>
            <Button
              onClick={() => window.location.href = "/sign-in"}
              className="w-full bg-[#316FE3] hover:bg-[#2563eb]"
            >
              Sign In
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Convex authentication issues (when required)
  if (requireFullAuth && !authState.convexAuthenticated) {
    return fallback || (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="max-w-md w-full p-8 bg-[#000000]/40 border-[#dc2626]">
          <div className="text-center space-y-4">
            <div className="text-6xl mb-4">⚠️</div>
            <h2 className="text-xl font-bold text-yellow-400">Authentication Issue</h2>
            <p className="text-gray-300 text-sm">
              There's a problem connecting to the secure backend. This might be temporary.
            </p>
            
            {authState.error && (
              <div className="bg-[#202631] p-3 rounded border border-red-600 text-left">
                <div className="text-xs text-red-300">
                  <strong>Error:</strong> {authState.error}
                </div>
              </div>
            )}
            
            <div className="space-y-2">
              <Button
                onClick={handleRetry}
                disabled={isRetrying}
                className="w-full bg-[#316FE3] hover:bg-[#2563eb]"
              >
                {isRetrying ? "Retrying..." : `Retry Connection ${retryCount > 0 ? `(${retryCount})` : ""}`}
              </Button>
              
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                Reload Page
              </Button>
            </div>
            
            <div className="text-xs text-gray-500 pt-4 border-t border-gray-700">
              Status: Clerk ✅ | Convex ❌ | Retry: {retryCount}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // User sync issues (when required)
  if (requireFullAuth && authState.convexAuthenticated && !authState.userSynced && !authState.error) {
    return fallback || (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="p-8 bg-[#000000]/40 border-[#202631]">
          <div className="text-center space-y-4">
            <Loader />
            <h3 className="text-lg font-medium text-[#F5F7FA]">
              Setting up your account...
            </h3>
            <div className="text-sm text-gray-400">
              Syncing your profile with the secure backend
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // All good - render children
  return (
    <AuthErrorBoundary>
      {children}
    </AuthErrorBoundary>
  );
}

// Convenience components for different auth levels
export const AuthGuards = {
  // Requires only Clerk authentication
  Basic: ({ children, fallback }: AuthGuardProps) => (
    <EnhancedAuthGuard requireFullAuth={false} fallback={fallback}>
      {children}
    </EnhancedAuthGuard>
  ),
  
  // Requires full Clerk + Convex authentication
  Full: ({ children, fallback }: AuthGuardProps) => (
    <EnhancedAuthGuard requireFullAuth={true} fallback={fallback}>
      {children}
    </EnhancedAuthGuard>
  ),
  
  // For dashboard pages (full auth + user sync)
  Dashboard: ({ children, fallback }: AuthGuardProps) => (
    <EnhancedAuthGuard requireFullAuth={true} fallback={fallback}>
      {children}
    </EnhancedAuthGuard>
  ),
};