import { useUser, useAuth as useClerkAuth } from "@clerk/clerk-react";
import { useConvexAuth } from "convex/react";
import { useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { useEffect, useState } from "react";
import { Card } from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";

interface AuthStatusProps {
  showDetails?: boolean;
}

export function AuthStatusDashboard({ showDetails = false }: AuthStatusProps) {
  const { user, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const clerkAuth = useClerkAuth();
  const { isAuthenticated: convexAuth, isLoading: convexLoading } = useConvexAuth();
  const [lastTokenCheck, setLastTokenCheck] = useState<Date | null>(null);
  const [tokenStatus, setTokenStatus] = useState<'unknown' | 'valid' | 'invalid' | 'missing'>('unknown');
  
  // Test Convex connectivity
  const healthCheck = useQuery(api.debug.authDebugging.authHealthCheck, {});
  
  // Auto-refresh token status every 30 seconds
  useEffect(() => {
    const checkTokenStatus = async () => {
      if (!isSignedIn || !clerkAuth.getToken) return;
      
      try {
        const token = await clerkAuth.getToken({ template: "convex" });
        setTokenStatus(token ? 'valid' : 'missing');
        setLastTokenCheck(new Date());
      } catch (error) {
        console.error("Token check failed:", error);
        setTokenStatus('invalid');
        setLastTokenCheck(new Date());
      }
    };

    checkTokenStatus();
    const interval = setInterval(checkTokenStatus, 30000); // Check every 30s
    
    return () => clearInterval(interval);
  }, [isSignedIn, clerkAuth]);

  const getStatusColor = (status: boolean | undefined, loading = false) => {
    if (loading) return "bg-yellow-500";
    return status ? "bg-green-500" : "bg-red-500";
  };

  const getStatusText = (status: boolean | undefined, loading = false) => {
    if (loading) return "Loading...";
    return status ? "Connected" : "Disconnected";
  };

  const formatTime = (date: Date | null) => {
    if (!date) return "Never";
    return date.toLocaleTimeString();
  };

  return (
    <Card className="p-4 bg-[#000000]/40 border-[#202631]">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium text-[#F5F7FA]">🔐 Auth Status Monitor</h3>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor(convexAuth && isSignedIn, convexLoading || !clerkLoaded)}`}></div>
          <span className="text-sm text-gray-300">
            {getStatusText(convexAuth && isSignedIn, convexLoading || !clerkLoaded)}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Clerk Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-400">Clerk Authentication</h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Loaded:</span>
              <Badge variant={clerkLoaded ? "default" : "secondary"} className="text-xs">
                {clerkLoaded ? "✅" : "⏳"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Signed In:</span>
              <Badge variant={isSignedIn ? "default" : "destructive"} className="text-xs">
                {isSignedIn ? "✅" : "❌"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">User ID:</span>
              <span className="text-xs text-gray-300 font-mono">
                {user?.id ? `${user.id.substring(0, 8)}...` : "None"}
              </span>
            </div>
          </div>
        </div>

        {/* JWT Token Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-400">JWT Token</h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Status:</span>
              <Badge 
                variant={tokenStatus === 'valid' ? "default" : tokenStatus === 'missing' ? "destructive" : "secondary"} 
                className="text-xs"
              >
                {tokenStatus === 'valid' ? "✅ Valid" : 
                 tokenStatus === 'missing' ? "❌ Missing" : 
                 tokenStatus === 'invalid' ? "⚠️ Invalid" : "❓ Unknown"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Last Check:</span>
              <span className="text-xs text-gray-300">
                {formatTime(lastTokenCheck)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Template:</span>
              <span className="text-xs text-gray-300 font-mono">convex</span>
            </div>
          </div>
        </div>

        {/* Convex Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-400">Convex Backend</h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Loading:</span>
              <Badge variant={convexLoading ? "secondary" : "default"} className="text-xs">
                {convexLoading ? "⏳ Yes" : "✅ No"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Authenticated:</span>
              <Badge variant={convexAuth ? "default" : "destructive"} className="text-xs">
                {convexAuth ? "✅ Yes" : "❌ No"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Health Check:</span>
              <Badge 
                variant={healthCheck?.success ? "default" : "destructive"} 
                className="text-xs"
              >
                {healthCheck?.success ? "✅ Pass" : "❌ Fail"}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {showDetails && healthCheck && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <h4 className="text-sm font-medium text-gray-400 mb-2">Health Check Details</h4>
          <div className="bg-[#202631] p-3 rounded border">
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-gray-500">Overall Status:</span>
                <span className="font-mono text-gray-300">{healthCheck.overallStatus}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Checks Passed:</span>
                <span className="font-mono text-gray-300">
                  {healthCheck.summary?.passed || 0}/{healthCheck.summary?.total || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Execution Time:</span>
                <span className="font-mono text-gray-300">{healthCheck.executionTime}ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Last Updated:</span>
                <span className="font-mono text-gray-300">
                  {healthCheck.timestamp ? new Date(healthCheck.timestamp).toLocaleTimeString() : "Unknown"}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-4 pt-4 border-t border-gray-700">
        <div className="flex flex-wrap gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => window.location.reload()}
            className="text-xs border-gray-600 text-gray-300 hover:bg-gray-800"
          >
            Refresh Page
          </Button>
          
          {isSignedIn && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => clerkAuth.signOut()}
              className="text-xs border-red-600 text-red-300 hover:bg-red-900/20"
            >
              Sign Out
            </Button>
          )}
          
          {!isSignedIn && (
            <Button
              size="sm"
              onClick={() => window.location.href = "/sign-in"}
              className="text-xs bg-[#316FE3] hover:bg-[#2563eb]"
            >
              Sign In
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
}