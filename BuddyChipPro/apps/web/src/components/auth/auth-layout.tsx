import { Link } from "@tanstack/react-router";
import type { ReactNode } from "react";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";

interface AuthLayoutProps {
  children: ReactNode;
  showBackButton?: boolean;
  backUrl?: string;
  title?: string;
  subtitle?: string;
}

/**
 * Enhanced AuthLayout – shared wrapper for custom auth pages.
 * Provides branded background, responsive design, and improved UX.
 */
export function AuthLayout({
  children,
  showBackButton = false,
  backUrl = "/",
  title,
  subtitle
}: AuthLayoutProps) {
  console.log("🎨 Auth layout rendered");

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0E1117] via-slate-900 to-[#000000] flex items-center justify-center p-4 relative overflow-hidden">
      {/* Enhanced animated background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Primary blob */}
        <motion.div
          animate={{
            x: [-20, 20, -20],
            y: [-10, 10, -10],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-4 -left-4 w-72 h-72 bg-[#316FE3]/10 rounded-full mix-blend-multiply filter blur-xl"
        />

        {/* Secondary blob */}
        <motion.div
          animate={{
            x: [20, -20, 20],
            y: [10, -10, 10],
            scale: [1.1, 1, 1.1]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute top-1/2 -right-4 w-72 h-72 bg-[#316FE3]/5 rounded-full mix-blend-multiply filter blur-xl"
        />

        {/* Additional accent blobs */}
        <motion.div
          animate={{
            rotate: [0, 360],
            scale: [0.8, 1.2, 0.8]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-10 left-1/4 w-32 h-32 bg-[#3fb950]/5 rounded-full mix-blend-multiply filter blur-2xl"
        />

        <motion.div
          animate={{
            rotate: [360, 0],
            scale: [1.2, 0.8, 1.2]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 right-1/4 w-24 h-24 bg-[#d29922]/5 rounded-full mix-blend-multiply filter blur-2xl"
        />
      </div>

      {/* Back button */}
      {showBackButton && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="absolute top-6 left-6 z-20"
        >
          <Link
            to={backUrl}
            className="flex items-center gap-2 text-[#6E7A8C] hover:text-[#F5F7FA] transition-colors duration-200 group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-200" />
            <span className="text-sm font-medium">Back</span>
          </Link>
        </motion.div>
      )}

      {/* Main content container */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="relative z-10 w-full max-w-lg mx-auto"
      >
        {/* Branding header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <Link to="/" className="inline-flex items-center gap-3 group">
              <div className="relative">
                <img
                  src="/Logo.svg"
                  alt="BuddyChip"
                  className="h-12 w-12 transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-[#316FE3]/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
              <span className="text-3xl font-bold text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors duration-300">
                BuddyChip
              </span>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="mt-3 space-y-1"
          >
            {title ? (
              <h1 className="text-xl font-semibold text-[#F5F7FA]">{title}</h1>
            ) : null}

            <p className="text-[#6E7A8C] text-sm leading-relaxed">
              {subtitle || "Start your Yap Journey with AI-powered Twitter assistance"}
            </p>
          </motion.div>
        </div>

        {/* Content container with enhanced styling */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="bg-[#000000]/70 backdrop-blur-md rounded-2xl border border-[#202631]/50 shadow-2xl overflow-hidden"
        >
          {/* Inner glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#316FE3]/5 via-transparent to-[#3fb950]/5 pointer-events-none" />

          {/* Content */}
          <div className="relative p-6 sm:p-8">
            {children}
          </div>
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="text-center mt-6 space-y-2"
        >
          <p className="text-[#6E7A8C] text-xs leading-relaxed">
            By continuing, you agree to our{" "}
            <a
              href="/terms"
              className="text-[#316FE3] hover:text-[#2563eb] underline underline-offset-2 transition-colors duration-200"
            >
              Terms of Service
            </a>
            {" "}and{" "}
            <a
              href="/privacy"
              className="text-[#316FE3] hover:text-[#2563eb] underline underline-offset-2 transition-colors duration-200"
            >
              Privacy Policy
            </a>
          </p>

          <p className="text-[#484f58] text-xs">
            Secure authentication powered by Clerk
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
}