import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Confetti from "react-confetti";
import { Check, Sparkles, Heart, Star, Zap } from "lucide-react";

interface CelebrationAnimationProps {
  show: boolean;
  type?: "confetti" | "sparkles" | "success" | "hearts" | "stars";
  duration?: number;
  onComplete?: () => void;
  message?: string;
  subMessage?: string;
}

export function CelebrationAnimation({
  show,
  type = "confetti",
  duration = 3000,
  onComplete,
  message = "Success!",
  subMessage,
}: CelebrationAnimationProps) {
  const [isVisible, setIsVisible] = useState(show);
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });

  console.log(`🎉 Celebration animation - Type: ${type}, Show: ${show}, Duration: ${duration}ms`);

  useEffect(() => {
    setIsVisible(show);
    
    if (show) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onComplete?.();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [show, duration, onComplete]);

  useEffect(() => {
    const updateWindowSize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateWindowSize();
    window.addEventListener('resize', updateWindowSize);
    return () => window.removeEventListener('resize', updateWindowSize);
  }, []);

  const renderCelebrationContent = () => {
    switch (type) {
      case "confetti":
        return (
          <>
            {isVisible && (
              <Confetti
                width={windowSize.width}
                height={windowSize.height}
                recycle={false}
                numberOfPieces={400}
                gravity={0.3}
                colors={['#316FE3', '#2563eb', '#3fb950', '#d29922', '#f85149', '#58a6ff']}
              />
            )}
            <CelebrationMessage message={message} subMessage={subMessage} icon={Check} />
          </>
        );
        
      case "sparkles":
        return (
          <>
            <SparklesEffect />
            <CelebrationMessage message={message} subMessage={subMessage} icon={Sparkles} />
          </>
        );
        
      case "hearts":
        return (
          <>
            <FloatingElements icon={Heart} color="#f85149" />
            <CelebrationMessage message={message} subMessage={subMessage} icon={Heart} />
          </>
        );
        
      case "stars":
        return (
          <>
            <FloatingElements icon={Star} color="#d29922" />
            <CelebrationMessage message={message} subMessage={subMessage} icon={Star} />
          </>
        );
        
      case "success":
      default:
        return (
          <>
            <SuccessRipple />
            <CelebrationMessage message={message} subMessage={subMessage} icon={Check} />
          </>
        );
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 pointer-events-none"
        >
          {renderCelebrationContent()}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

function CelebrationMessage({ 
  message, 
  subMessage, 
  icon: Icon 
}: { 
  message: string; 
  subMessage?: string; 
  icon: React.ElementType;
}) {
  return (
    <div className="absolute inset-0 flex items-center justify-center">
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        exit={{ scale: 0, rotate: 180 }}
        transition={{ 
          type: "spring", 
          stiffness: 200, 
          damping: 20,
          delay: 0.2 
        }}
        className="bg-[#000000]/90 backdrop-blur-sm border border-[#202631] rounded-2xl p-8 text-center shadow-2xl"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
          className="w-16 h-16 mx-auto mb-4 bg-[#316FE3] rounded-full flex items-center justify-center"
        >
          <Icon className="w-8 h-8 text-white" />
        </motion.div>
        
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="text-2xl font-bold text-[#F5F7FA] mb-2"
        >
          {message}
        </motion.h2>
        
        {subMessage && (
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
            className="text-[#6E7A8C] text-sm"
          >
            {subMessage}
          </motion.p>
        )}
      </motion.div>
    </div>
  );
}

function SparklesEffect() {
  const sparkles = Array.from({ length: 20 }, (_, i) => i);
  
  return (
    <div className="absolute inset-0">
      {sparkles.map((i) => (
        <motion.div
          key={i}
          initial={{ 
            opacity: 0, 
            scale: 0,
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
          }}
          animate={{ 
            opacity: [0, 1, 0], 
            scale: [0, 1, 0],
            rotate: [0, 180, 360],
          }}
          transition={{ 
            duration: 2,
            delay: Math.random() * 2,
            repeat: 1,
          }}
          className="absolute"
        >
          <Sparkles className="w-6 h-6 text-[#316FE3]" />
        </motion.div>
      ))}
    </div>
  );
}

function FloatingElements({ 
  icon: Icon, 
  color 
}: { 
  icon: React.ElementType; 
  color: string;
}) {
  const elements = Array.from({ length: 15 }, (_, i) => i);
  
  return (
    <div className="absolute inset-0">
      {elements.map((i) => (
        <motion.div
          key={i}
          initial={{ 
            opacity: 0,
            scale: 0,
            x: Math.random() * window.innerWidth,
            y: window.innerHeight + 50,
          }}
          animate={{ 
            opacity: [0, 1, 0],
            scale: [0, 1, 0.5],
            y: -50,
            x: Math.random() * window.innerWidth,
          }}
          transition={{ 
            duration: 3,
            delay: Math.random() * 1.5,
            ease: "easeOut",
          }}
          className="absolute"
        >
          <Icon className="w-8 h-8" style={{ color }} />
        </motion.div>
      ))}
    </div>
  );
}

function SuccessRipple() {
  return (
    <div className="absolute inset-0 flex items-center justify-center">
      <motion.div
        initial={{ scale: 0, opacity: 1 }}
        animate={{ scale: 4, opacity: 0 }}
        transition={{ duration: 1.5, ease: "easeOut" }}
        className="w-32 h-32 border-4 border-[#3fb950] rounded-full"
      />
      <motion.div
        initial={{ scale: 0, opacity: 1 }}
        animate={{ scale: 3, opacity: 0 }}
        transition={{ duration: 1.5, delay: 0.3, ease: "easeOut" }}
        className="absolute w-32 h-32 border-4 border-[#316FE3] rounded-full"
      />
    </div>
  );
}
