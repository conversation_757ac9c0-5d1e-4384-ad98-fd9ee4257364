declare global {
  interface Window {
    ConvexLogger?: {
      logError: (type: string, payload: any) => void;
    };
  }
}

import { Component, ReactNode } from "react";
import { Button } from "../ui/button";
import { Card } from "../ui/card";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error("🔴 Auth Error Boundary Caught:", error, errorInfo);
    this.setState({ errorInfo });
    
    // Log to Convex for monitoring (if available)
    if (window.ConvexLogger) {
      window.ConvexLogger.logError('AUTH_ERROR_BOUNDARY', {
        error: error.message,
        stack: error.stack,
        errorInfo,
        timestamp: new Date().toISOString(),
      });
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Card className="max-w-md w-full p-6 bg-[#000000]/40 border-[#dc2626]">
            <div className="text-center space-y-4">
              <div className="text-6xl mb-4">🔴</div>
              <h2 className="text-xl font-bold text-red-400">Authentication Error</h2>
              <p className="text-gray-300 text-sm">
                There was a problem with the authentication system.
              </p>
              
              {this.state.error && (
                <details className="text-left bg-[#202631] p-3 rounded border">
                  <summary className="cursor-pointer text-sm font-medium text-gray-400 mb-2">
                    Error Details
                  </summary>
                  <div className="text-xs font-mono text-red-300">
                    <div className="font-semibold mb-1">Message:</div>
                    <div className="mb-3">{this.state.error.message}</div>
                    
                    {this.state.error.stack && (
                      <>
                        <div className="font-semibold mb-1">Stack Trace:</div>
                        <pre className="whitespace-pre-wrap text-xs">
                          {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                        </pre>
                      </>
                    )}
                  </div>
                </details>
              )}
              
              <div className="space-y-2">
                <Button
                  onClick={() => {
                    this.setState({ hasError: false, error: undefined });
                  }}
                  className="w-full bg-[#316FE3] hover:bg-[#2563eb]"
                >
                  Try Again
                </Button>
                
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  Reload Page
                </Button>
              </div>
              
              <div className="text-xs text-gray-500 pt-4 border-t border-gray-700">
                <p>If this problem persists, try:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Clearing browser cache</li>
                  <li>Logging out and back in</li>
                  <li>Disabling browser extensions</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}