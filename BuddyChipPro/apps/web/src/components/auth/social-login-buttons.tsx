import { SignInButton } from "@clerk/clerk-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>G<PERSON><PERSON> } from "react-icons/si";
import { useState } from "react";
import { Loader2 } from "lucide-react";

interface SocialLoginButtonsProps {
  mode?: "modal" | "redirect";
  showAllProviders?: boolean;
}

export function SocialLoginButtons({
  mode = "redirect",
  showAllProviders = false
}: SocialLoginButtonsProps) {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);

  const handleProviderClick = (provider: string) => {
    console.log(`🔐 Initiating ${provider} authentication...`);
    setLoadingProvider(provider);
    // Reset loading state after a delay (Clerk will handle the actual redirect)
    setTimeout(() => setLoadingProvider(null), 3000);
  };

  const socialProviders = [
    {
      id: "google",
      name: "<PERSON>",
      icon: <PERSON><PERSON>oogle,
      className: "hover:bg-[#4285f4]/10 hover:border-[#4285f4] hover:text-[#4285f4]",
      primary: true,
    },
    {
      id: "github",
      name: "GitHub",
      icon: SiGithub,
      className: "hover:bg-[#333]/10 hover:border-[#333] hover:text-[#F5F7FA]",
      primary: true,
    },
    {
      id: "apple",
      name: "Apple",
      icon: SiApple,
      className: "hover:bg-[#000]/10 hover:border-[#000] hover:text-[#F5F7FA]",
      primary: false,
    },
    {
      id: "twitter",
      name: "Twitter",
      icon: SiGitter,
      className: "hover:bg-[#0078d4]/10 hover:border-[#0078d4] hover:text-[#0078d4]",
      primary: false,
    },
  ];

  const displayProviders = showAllProviders
    ? socialProviders
    : socialProviders.filter(p => p.primary);

  return (
    <div className="space-y-3">
      {displayProviders.map((provider) => {
        const Icon = provider.icon;
        const isLoading = loadingProvider === provider.id;

        return (
          <SignInButton key={provider.id} mode={mode}>
            <Button
              variant="outline"
              className={`
                w-full bg-[#0E1117] border-[#202631] text-[#F5F7FA]
                transition-all duration-300 ease-out
                transform hover:scale-[1.02] active:scale-[0.98]
                shadow-sm hover:shadow-md
                focus:ring-2 focus:ring-[#316FE3]/20 focus:ring-offset-2 focus:ring-offset-[#000000]
                disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
                ${provider.className}
              `}
              onClick={() => handleProviderClick(provider.id)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 mr-3 animate-spin" />
              ) : (
                <Icon className="w-5 h-5 mr-3" />
              )}
              {isLoading ? `Connecting to ${provider.name}...` : `Continue with ${provider.name}`}
            </Button>
          </SignInButton>
        );
      })}

      {!showAllProviders && (
        <div className="text-center">
          <button
            onClick={() => {/* Toggle to show more providers */}}
            className="text-[#6E7A8C] hover:text-[#316FE3] text-sm font-medium transition-colors duration-200"
          >
            More sign-in options
          </button>
        </div>
      )}
    </div>
  );
}