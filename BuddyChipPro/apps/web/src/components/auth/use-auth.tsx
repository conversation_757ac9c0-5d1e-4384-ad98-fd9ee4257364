import { useUser } from "@clerk/clerk-react";
import { useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { useEffect } from "react";
import { useConvexAuth } from "convex/react";

export function useAuth() {
  const { isLoaded, isSignedIn, user } = useUser();
  const { isAuthenticated: isConvexAuthenticated, isLoading: isConvexLoading } = useConvexAuth();
  const createOrUpdateUser = useMutation(api.users.createOrUpdateUser);

  // Extract wallet information from user's web3 wallets
  const walletAddress = user?.web3Wallets?.[0]?.web3Wallet || null;
  const walletType = user?.web3Wallets?.[0]?.verification?.strategy || null;

  // Create or update user in Convex when they sign in
  useEffect(() => {
    if (isSignedIn && user) {
      console.log("🔍 Creating/updating user in Convex:", {
        email: user.primaryEmailAddress?.emailAddress || "",
        name: user.fullName || user.firstName || user.username || "User",
        clerkId: user.id,
      });
      
      createOrUpdateUser({
        email: user.primaryEmailAddress?.emailAddress || "",
        name: user.fullName || user.firstName || user.username || "User",
        clerkId: user.id,
        image: user.imageUrl,
      }).then((result: unknown) => {
        console.log("✅ User created/updated in Convex:", result);
      }).catch((error) => {
        console.error("❌ Failed to create/update user:", error);
      });
    }
  }, [isSignedIn, user, createOrUpdateUser]);

  return {
    isLoading: !isLoaded || isConvexLoading,
    isAuthenticated: isSignedIn && isLoaded && isConvexAuthenticated,
    user,
    wallet: {
      address: walletAddress,
      type: walletType,
      isConnected: !!walletAddress,
    },
  };
}