import { SignInButton, SignOutButton, SignedIn, SignedOut } from "@clerk/clerk-react";
import { But<PERSON> } from "@/components/ui/button";

export function AuthButton() {
  return (
    <div className="flex gap-2">
      <SignedOut>
        <SignInButton>
          <Button className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/80">
            Sign In
          </Button>
        </SignInButton>
      </SignedOut>
      <SignedIn>
        <SignOutButton>
          <Button 
            variant="ghost"
            className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
          >
            Sign Out
          </Button>
        </SignOutButton>
      </SignedIn>
    </div>
  );
}