import React, { FC, ReactNode } from 'react';

/**
 * Simplified Solana Wallet Provider
 * 
 * DISABLED: Solana wallet adapters cause module conflicts in browser environment.
 * This is a fallback provider that renders children without wallet functionality.
 * 
 * To enable wallet functionality:
 * 1. Fix jayson/rpc-websockets browser compatibility
 * 2. Restore original wallet adapter imports
 * 3. Test thoroughly in both dev and production
 */

interface SolanaWalletProviderProps {
  children: ReactNode;
  network?: any;
  endpoint?: string;
}

/**
 * Fallback Solana Wallet Provider
 * 
 * This provider simply renders children without wallet functionality
 * to prevent module import conflicts in the browser environment.
 */
export const SolanaWalletProvider: FC<SolanaWalletProviderProps> = ({
  children,
  network,
  endpoint,
}) => {
  // Log that wallet functionality is disabled
  console.info('Solana wallet functionality is currently disabled to prevent module conflicts');
  
  // Simply render children without any wallet context
  return <>{children}</>;
};