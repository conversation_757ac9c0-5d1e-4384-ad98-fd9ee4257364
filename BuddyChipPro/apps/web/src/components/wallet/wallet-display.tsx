import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@BuddyChipAI/backend';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { ChevronDown, Copy, Settings, Plus, Wallet } from 'lucide-react';
import { WalletConnectionModal } from './wallet-connection-modal';
import { toast } from 'sonner';

export function WalletDisplay() {
  const [showConnectionModal, setShowConnectionModal] = useState(false);
  const [selectedBlockchain, setSelectedBlockchain] = useState<'ethereum' | 'solana' | 'polygon' | 'base'>('solana');
  
  // Get user's wallets - handle potential TypeScript errors
  let wallets: any[] = [];
  let primaryWallet: any = null;
  
  try {
    wallets = useQuery(api.auth.walletDetection.getUserWallets) || [];
    primaryWallet = useQuery(api.auth.walletDetection.getPrimaryWallet);
  } catch (error) {
    console.warn('Wallet queries failed:', error);
  }
  
  // Mutations
  const setPrimaryWallet = useMutation(api.auth.walletDetection.setPrimaryWallet);
  const markWalletUsed = useMutation(api.walletMutations.markWalletUsed);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Address copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy address');
    }
  };

  const formatAddress = (address: string, length = 8) => {
    return `${address.slice(0, length)}...${address.slice(-length)}`;
  };

  const getBlockchainIcon = (blockchain: string) => {
    switch (blockchain) {
      case 'ethereum':
        return '⟠';
      case 'solana':
        return '◎';
      case 'polygon':
        return '🔷';
      case 'base':
        return '🔵';
      default:
        return '💰';
    }
  };

  const getWalletTypeIcon = (walletType: string) => {
    switch (walletType.toLowerCase()) {
      case 'phantom':
        return '👻';
      case 'metamask':
        return '🦊';
      case 'coinbase':
        return '🟦';
      case 'solflare':
        return '☀️';
      default:
        return '💼';
    }
  };

  const handleWalletSelect = async (walletId: string) => {
    try {
      await setPrimaryWallet({ walletId: walletId as any });
      await markWalletUsed({ walletId: walletId as any });
      toast.success('Primary wallet updated');
    } catch (error) {
      toast.error('Failed to update primary wallet');
    }
  };

  const handleConnectWallet = (blockchain: 'ethereum' | 'solana' | 'polygon' | 'base') => {
    setSelectedBlockchain(blockchain);
    setShowConnectionModal(true);
  };

  // Always show the wallet UI
  const isLoading = wallets === undefined;

  // No wallets connected
  if (!wallets || wallets.length === 0) {
    return (
      <>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => handleConnectWallet('solana')}
          className="border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-accent)]/10 hover:text-[var(--buddychip-accent)] hover:border-[var(--buddychip-accent)]"
        >
          <Plus className="h-4 w-4 mr-2" />
          Connect Wallet
        </Button>

        <WalletConnectionModal
          isOpen={showConnectionModal}
          onClose={() => setShowConnectionModal(false)}
          blockchain="solana"
        />
      </>
    );
  }

  // Has wallets - show primary wallet with dropdown
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            {primaryWallet && (
              <>
                <span>{getBlockchainIcon(primaryWallet.blockchain)}</span>
                <span>{getWalletTypeIcon(primaryWallet.walletType)}</span>
                <span className="font-mono text-xs">
                  {formatAddress(primaryWallet.address)}
                </span>
                {primaryWallet.metadata?.balance && (
                  <Badge variant="secondary" className="text-xs">
                    {parseFloat(primaryWallet.metadata.balance).toFixed(2)}
                  </Badge>
                )}
              </>
            )}
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent 
          align="end" 
          className="w-64 bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] shadow-lg"
        >
          {/* Current wallets */}
          {wallets.map((wallet) => (
            <DropdownMenuItem
              key={wallet._id}
              onClick={() => handleWalletSelect(wallet._id)}
              className="flex items-center justify-between py-2.5 px-3 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/10 hover:text-[var(--buddychip-accent)] focus:bg-[var(--buddychip-accent)]/10 focus:text-[var(--buddychip-accent)]"
            >
              <div className="flex items-center gap-2">
                <span>{getBlockchainIcon(wallet.blockchain)}</span>
                <span>{getWalletTypeIcon(wallet.walletType)}</span>
                <div className="flex flex-col">
                  <span className="font-mono text-xs">
                    {formatAddress(wallet.address)}
                  </span>
                  <span className="text-xs text-muted-foreground capitalize">
                    {wallet.blockchain} • {wallet.walletType}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-1">
                {wallet.isPrimary && (
                  <Badge variant="default" className="text-xs">Primary</Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    copyToClipboard(wallet.address);
                  }}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </DropdownMenuItem>
          ))}
          
          <DropdownMenuSeparator />
          
          {/* Add new wallet option */}
          <DropdownMenuItem 
            onClick={() => handleConnectWallet('solana')}
            className="py-2.5 px-3 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/10 hover:text-[var(--buddychip-accent)] focus:bg-[var(--buddychip-accent)]/10 focus:text-[var(--buddychip-accent)]"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Solana Wallet
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem className="py-2.5 px-3 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/10 hover:text-[var(--buddychip-accent)] focus:bg-[var(--buddychip-accent)]/10 focus:text-[var(--buddychip-accent)]">
            <Settings className="h-4 w-4 mr-2" />
            Wallet Settings
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <WalletConnectionModal
        isOpen={showConnectionModal}
        onClose={() => setShowConnectionModal(false)}
        blockchain={selectedBlockchain}
      />
    </>
  );
}