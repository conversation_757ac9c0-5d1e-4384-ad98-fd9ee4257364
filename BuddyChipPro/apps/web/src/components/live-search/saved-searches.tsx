import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { 
  BookmarkIcon, 
  Search,
  Trash2,
  Edit3,
  Play,
  Clock,
  Zap,
  Star,
  Copy,
  Download,
  Plus,
  Tag,
  Calendar,
  RefreshCw,
  Bell,
  Settings,
  Globe,
  Brain,
  Target,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SavedSearch {
  id: string;
  name: string;
  query: string;
  searchType: "general" | "mentions" | "trends" | "analysis";
  sources: string[];
  useXAI: boolean;
  filters?: any;
  tags: string[];
  createdAt: number;
  lastUsed?: number;
  useCount: number;
  isScheduled: boolean;
  scheduleInterval?: string;
  isFavorite: boolean;
  notifications: boolean;
}

interface SavedSearchesProps {
  onSearchSelect: (search: any) => void;
  onExecuteSearch: (params: any) => void;
}

export function SavedSearches({ onSearchSelect, onExecuteSearch }: SavedSearchesProps) {
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [filterTag, setFilterTag] = useState<string>("all");
  const [searchFilter, setSearchFilter] = useState("");
  const [sortBy, setSortBy] = useState<string>("recent");

  // Initialize with some example saved searches
  useEffect(() => {
    const exampleSearches: SavedSearch[] = [
      {
        id: "1",
        name: "AI Trends Weekly",
        query: "artificial intelligence trends 2024",
        searchType: "trends",
        sources: ["x", "web", "news"],
        useXAI: true,
        tags: ["AI", "technology", "trends"],
        createdAt: Date.now() - 7 * 24 * 60 * 60 * 1000,
        lastUsed: Date.now() - 2 * 60 * 60 * 1000,
        useCount: 15,
        isScheduled: true,
        scheduleInterval: "weekly",
        isFavorite: true,
        notifications: true
      },
      {
        id: "2",
        name: "Crypto Market Sentiment",
        query: "cryptocurrency bitcoin ethereum sentiment",
        searchType: "analysis",
        sources: ["x", "web"],
        useXAI: true,
        tags: ["crypto", "bitcoin", "sentiment"],
        createdAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
        lastUsed: Date.now() - 6 * 60 * 60 * 1000,
        useCount: 8,
        isScheduled: false,
        isFavorite: false,
        notifications: false
      },
      {
        id: "3",
        name: "BuddyChip Mentions",
        query: "@buddychip OR \"BuddyChip Pro\"",
        searchType: "mentions",
        sources: ["x"],
        useXAI: false,
        tags: ["brand", "mentions", "monitoring"],
        createdAt: Date.now() - 3 * 24 * 60 * 60 * 1000,
        lastUsed: Date.now() - 30 * 60 * 1000,
        useCount: 25,
        isScheduled: true,
        scheduleInterval: "daily",
        isFavorite: true,
        notifications: true
      }
    ];
    setSavedSearches(exampleSearches);
  }, []);

  const [newSearch, setNewSearch] = useState<Partial<SavedSearch>>({
    name: "",
    query: "",
    searchType: "general",
    sources: ["x", "web"],
    useXAI: true,
    tags: [],
    isScheduled: false,
    isFavorite: false,
    notifications: false
  });

  // Get all unique tags
  const allTags = Array.from(new Set(savedSearches.flatMap(search => search.tags)));

  // Filter and sort searches
  const filteredSearches = savedSearches
    .filter(search => {
      const matchesTag = filterTag === "all" || search.tags.includes(filterTag);
      const matchesSearch = searchFilter === "" || 
        search.name.toLowerCase().includes(searchFilter.toLowerCase()) ||
        search.query.toLowerCase().includes(searchFilter.toLowerCase()) ||
        search.tags.some(tag => tag.toLowerCase().includes(searchFilter.toLowerCase()));
      return matchesTag && matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "recent":
          return (b.lastUsed || 0) - (a.lastUsed || 0);
        case "created":
          return b.createdAt - a.createdAt;
        case "popular":
          return b.useCount - a.useCount;
        case "favorite":
          return Number(b.isFavorite) - Number(a.isFavorite);
        default:
          return 0;
      }
    });

  const handleCreateSearch = () => {
    if (!newSearch.name || !newSearch.query) return;

    const search: SavedSearch = {
      id: Date.now().toString(),
      name: newSearch.name,
      query: newSearch.query,
      searchType: newSearch.searchType || "general",
      sources: newSearch.sources || ["x", "web"],
      useXAI: newSearch.useXAI || false,
      tags: newSearch.tags || [],
      createdAt: Date.now(),
      useCount: 0,
      isScheduled: newSearch.isScheduled || false,
      scheduleInterval: newSearch.scheduleInterval,
      isFavorite: newSearch.isFavorite || false,
      notifications: newSearch.notifications || false
    };

    setSavedSearches(prev => [search, ...prev]);
    setIsCreating(false);
    setNewSearch({
      name: "",
      query: "",
      searchType: "general",
      sources: ["x", "web"],
      useXAI: true,
      tags: [],
      isScheduled: false,
      isFavorite: false,
      notifications: false
    });
  };

  const handleExecuteSearch = (search: SavedSearch) => {
    // Update usage stats
    setSavedSearches(prev => prev.map(s => 
      s.id === search.id 
        ? { ...s, lastUsed: Date.now(), useCount: s.useCount + 1 }
        : s
    ));

    const searchParams = {
      query: search.query,
      searchType: search.searchType,
      sources: search.sources,
      useXAI: search.useXAI,
      filters: search.filters
    };

    onExecuteSearch(searchParams);
  };

  const handleToggleFavorite = (id: string) => {
    setSavedSearches(prev => prev.map(search => 
      search.id === id ? { ...search, isFavorite: !search.isFavorite } : search
    ));
  };

  const handleDeleteSearch = (id: string) => {
    setSavedSearches(prev => prev.filter(search => search.id !== id));
  };

  const handleExportSearches = () => {
    const exportData = {
      savedSearches,
      exportedAt: Date.now(),
      version: "1.0"
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `saved-searches-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getSearchTypeIcon = (type: string) => {
    switch (type) {
      case "mentions": return <Target className="h-4 w-4" />;
      case "trends": return <Search className="h-4 w-4" />;
      case "analysis": return <Brain className="h-4 w-4" />;
      default: return <Search className="h-4 w-4" />;
    }
  };

  const getSearchTypeColor = (type: string) => {
    switch (type) {
      case "mentions": return "border-green-500/50 text-green-400 bg-green-500/10";
      case "trends": return "border-orange-500/50 text-orange-400 bg-orange-500/10";
      case "analysis": return "border-purple-500/50 text-purple-400 bg-purple-500/10";
      default: return "border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="space-y-2">
              <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                <BookmarkIcon className="h-5 w-5 text-[#316FE3]" />
                Saved Searches
                <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                  {savedSearches.length} saved
                </Badge>
              </CardTitle>
              <CardDescription className="text-[#6E7A8C]">
                Manage and execute your saved search templates with automation options
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportSearches}
                className="border-[#202631] hover:bg-[#202631] text-[#F5F7FA]"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button
                onClick={() => setIsCreating(true)}
                className="bg-gradient-to-r from-[#316FE3] to-purple-600 hover:from-[#316FE3]/90 hover:to-purple-600/90 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Search
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters and Search */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label className="text-[#F5F7FA] text-sm">Search in Saved</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#6E7A8C]" />
                <Input
                  value={searchFilter}
                  onChange={(e) => setSearchFilter(e.target.value)}
                  placeholder="Search name, query, or tags..."
                  className="pl-10 bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-[#F5F7FA] text-sm">Filter by Tag</Label>
              <Select value={filterTag} onValueChange={setFilterTag}>
                <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  <SelectItem value="all" className="text-[#F5F7FA] hover:bg-[#202631]">All Tags</SelectItem>
                  {allTags.map(tag => (
                    <SelectItem key={tag} value={tag} className="text-[#F5F7FA] hover:bg-[#202631]">
                      #{tag}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-[#F5F7FA] text-sm">Sort By</Label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  <SelectItem value="recent" className="text-[#F5F7FA] hover:bg-[#202631]">Recently Used</SelectItem>
                  <SelectItem value="created" className="text-[#F5F7FA] hover:bg-[#202631]">Recently Created</SelectItem>
                  <SelectItem value="name" className="text-[#F5F7FA] hover:bg-[#202631]">Name</SelectItem>
                  <SelectItem value="popular" className="text-[#F5F7FA] hover:bg-[#202631]">Most Used</SelectItem>
                  <SelectItem value="favorite" className="text-[#F5F7FA] hover:bg-[#202631]">Favorites First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Create New Search Form */}
      {isCreating && (
        <Card className="bg-gradient-to-br from-[#316FE3]/10 via-[#000000]/70 to-purple-600/10 border-[#316FE3]/30 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <Plus className="h-5 w-5 text-[#316FE3]" />
              Create New Saved Search
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-[#F5F7FA]">Search Name</Label>
                <Input
                  value={newSearch.name || ""}
                  onChange={(e) => setNewSearch(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter a descriptive name..."
                  className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#F5F7FA]">Search Type</Label>
                <Select 
                  value={newSearch.searchType || "general"} 
                  onValueChange={(value) => setNewSearch(prev => ({ ...prev, searchType: value as any }))}
                >
                  <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#000000] border-[#202631]">
                    <SelectItem value="general" className="text-[#F5F7FA] hover:bg-[#202631]">General Search</SelectItem>
                    <SelectItem value="mentions" className="text-[#F5F7FA] hover:bg-[#202631]">Mentions</SelectItem>
                    <SelectItem value="trends" className="text-[#F5F7FA] hover:bg-[#202631]">Trends</SelectItem>
                    <SelectItem value="analysis" className="text-[#F5F7FA] hover:bg-[#202631]">Analysis</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-[#F5F7FA]">Search Query</Label>
              <Input
                value={newSearch.query || ""}
                onChange={(e) => setNewSearch(prev => ({ ...prev, query: e.target.value }))}
                placeholder="Enter your search query..."
                className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-[#F5F7FA]">Tags (comma-separated)</Label>
              <Input
                value={newSearch.tags?.join(", ") || ""}
                onChange={(e) => setNewSearch(prev => ({ 
                  ...prev, 
                  tags: e.target.value.split(",").map(tag => tag.trim()).filter(Boolean)
                }))}
                placeholder="ai, technology, trends..."
                className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 bg-[#000000]/40 border border-[#202631] rounded-lg">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-[#316FE3]" />
                  <span className="text-[#F5F7FA] text-sm">Use xAI</span>
                </div>
                <Switch 
                  checked={newSearch.useXAI || false} 
                  onCheckedChange={(checked) => setNewSearch(prev => ({ ...prev, useXAI: checked }))}
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-[#000000]/40 border border-[#202631] rounded-lg">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-yellow-400" />
                  <span className="text-[#F5F7FA] text-sm">Favorite</span>
                </div>
                <Switch 
                  checked={newSearch.isFavorite || false} 
                  onCheckedChange={(checked) => setNewSearch(prev => ({ ...prev, isFavorite: checked }))}
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-[#000000]/40 border border-[#202631] rounded-lg">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4 text-[#316FE3]" />
                  <span className="text-[#F5F7FA] text-sm">Notifications</span>
                </div>
                <Switch 
                  checked={newSearch.notifications || false} 
                  onCheckedChange={(checked) => setNewSearch(prev => ({ ...prev, notifications: checked }))}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setIsCreating(false)}
                className="border-[#202631] hover:bg-[#202631] text-[#F5F7FA]"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateSearch}
                disabled={!newSearch.name || !newSearch.query}
                className="bg-gradient-to-r from-[#316FE3] to-purple-600 hover:from-[#316FE3]/90 hover:to-purple-600/90 text-white"
              >
                Save Search
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Saved Searches List */}
      {filteredSearches.length === 0 ? (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardContent className="py-12">
            <div className="text-center">
              <BookmarkIcon className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">
                {savedSearches.length === 0 ? "No Saved Searches" : "No Matching Searches"}
              </h3>
              <p className="text-[#6E7A8C]">
                {savedSearches.length === 0 
                  ? "Create your first saved search to get started."
                  : "Try adjusting your filters or search terms."
                }
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredSearches.map((search, index) => (
            <Card 
              key={search.id}
              className={cn(
                "bg-[#000000]/70 border-[#202631] backdrop-blur-sm transition-all duration-300 hover:bg-[#000000]/90 group",
                search.isFavorite && "border-l-4 border-l-yellow-400",
                search.isScheduled && "border-r-4 border-r-green-400"
              )}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1 space-y-3">
                    {/* Header */}
                    <div className="flex items-center gap-3">
                      <h3 className="text-lg font-semibold text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">
                        {search.name}
                      </h3>
                      {search.isFavorite && (
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                      )}
                      <Badge 
                        variant="outline" 
                        className={cn("text-xs", getSearchTypeColor(search.searchType))}
                      >
                        {getSearchTypeIcon(search.searchType)}
                        <span className="ml-1 capitalize">{search.searchType}</span>
                      </Badge>
                      {search.useXAI && (
                        <Badge variant="outline" className="text-xs border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10">
                          <Zap className="h-3 w-3 mr-1" />
                          xAI
                        </Badge>
                      )}
                    </div>

                    {/* Query */}
                    <div className="text-[#F5F7FA] bg-[#000000]/40 border border-[#202631] rounded-lg p-3 font-mono text-sm">
                      {search.query}
                    </div>

                    {/* Tags */}
                    {search.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {search.tags.map((tag, tagIndex) => (
                          <Badge 
                            key={tagIndex}
                            variant="outline" 
                            className="text-xs border-[#202631] text-[#6E7A8C] hover:text-[#F5F7FA] hover:border-[#316FE3]/50 cursor-pointer transition-colors"
                            onClick={() => setFilterTag(tag)}
                          >
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Metadata */}
                    <div className="flex items-center gap-4 text-xs text-[#6E7A8C]">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Created {new Date(search.createdAt).toLocaleDateString()}
                      </div>
                      {search.lastUsed && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Last used {new Date(search.lastUsed).toLocaleTimeString()}
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Play className="h-3 w-3" />
                        Used {search.useCount} times
                      </div>
                      {search.isScheduled && (
                        <div className="flex items-center gap-1 text-green-400">
                          <RefreshCw className="h-3 w-3" />
                          Scheduled {search.scheduleInterval}
                        </div>
                      )}
                      {search.notifications && (
                        <div className="flex items-center gap-1 text-[#316FE3]">
                          <Bell className="h-3 w-3" />
                          Notifications on
                        </div>
                      )}
                    </div>

                    {/* Sources */}
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-[#6E7A8C]">Sources:</span>
                      {search.sources.map((source, sourceIndex) => (
                        <Badge 
                          key={sourceIndex}
                          variant="outline" 
                          className="text-xs border-[#202631] text-[#6E7A8C]"
                        >
                          {source === "x" ? "𝕏" : source}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col gap-2">
                    <Button
                      size="sm"
                      onClick={() => handleExecuteSearch(search)}
                      className="bg-gradient-to-r from-[#316FE3] to-purple-600 hover:from-[#316FE3]/90 hover:to-purple-600/90 text-white"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Execute
                    </Button>
                    
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleFavorite(search.id)}
                        className="h-8 w-8 p-0 hover:bg-[#202631]"
                      >
                        <Star className={cn(
                          "h-3 w-3",
                          search.isFavorite ? "text-yellow-400 fill-yellow-400" : "text-[#6E7A8C]"
                        )} />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigator.clipboard.writeText(search.query)}
                        className="h-8 w-8 p-0 hover:bg-[#202631]"
                      >
                        <Copy className="h-3 w-3 text-[#6E7A8C]" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingId(search.id)}
                        className="h-8 w-8 p-0 hover:bg-[#202631]"
                      >
                        <Edit3 className="h-3 w-3 text-[#6E7A8C]" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteSearch(search.id)}
                        className="h-8 w-8 p-0 hover:bg-red-600/20 hover:text-red-400"
                      >
                        <Trash2 className="h-3 w-3 text-[#6E7A8C] hover:text-red-400" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Quick Stats */}
      {savedSearches.length > 0 && (
        <Card className="bg-gradient-to-br from-[#316FE3]/10 via-[#000000]/70 to-purple-600/10 border-[#316FE3]/30 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-[#F5F7FA]">{savedSearches.length}</div>
                <div className="text-sm text-[#6E7A8C]">Total Saved</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-400">{savedSearches.filter(s => s.isFavorite).length}</div>
                <div className="text-sm text-[#6E7A8C]">Favorites</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-400">{savedSearches.filter(s => s.isScheduled).length}</div>
                <div className="text-sm text-[#6E7A8C]">Scheduled</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-[#316FE3]">{savedSearches.filter(s => s.useXAI).length}</div>
                <div className="text-sm text-[#6E7A8C]">xAI Enhanced</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}