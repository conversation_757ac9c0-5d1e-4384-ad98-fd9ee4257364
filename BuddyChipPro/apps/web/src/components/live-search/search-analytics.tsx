import { useState, useMemo } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { 
  BarChart3, 
  TrendingUp, 
  Clock,
  Search,
  Zap,
  Globe,
  MessageSquare,
  Activity,
  Target,
  Brain,
  Download,
  ArrowUp,
  ArrowDown,
  Minus,
  AlertTriangle
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SearchResult {
  id: string;
  source: "xai" | "tweetio" | "hybrid";
  query: string;
  content?: string;
  citations?: string[];
  results?: {
    id: string;
    text: string;
    createdAt: string;
  }[];
  insights?: {
    id: string;
    text: string;
    createdAt: string;
  }[];
  timestamp: number;
  success: boolean;
  error?: string;
}

interface SearchAnalyticsProps {
  searchHistory: SearchResult[];
  realTimeStats: {
    totalSearches: number;
    xaiSearches: number;
    averageResponseTime: number;
    successRate: number;
  };
}

export function SearchAnalytics({ searchHistory, realTimeStats }: SearchAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<string>("24h");
  const [selectedMetric, setSelectedMetric] = useState<string>("volume");

  // Calculate analytics from search history
  const analytics = useMemo(() => {
    const now = Date.now();
    const timeRanges = {
      "1h": 60 * 60 * 1000,
      "24h": 24 * 60 * 60 * 1000,
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000
    };

    const cutoff = now - timeRanges[timeRange as keyof typeof timeRanges];
    const filteredHistory = searchHistory.filter(search => search.timestamp >= cutoff);

    // Popular queries
    const queryFrequency: Record<string, number> = {};
    filteredHistory.forEach(search => {
      queryFrequency[search.query] = (queryFrequency[search.query] || 0) + 1;
    });

    const popularQueries = Object.entries(queryFrequency)
      .sort(([,a]: [string, number], [,b]: [string, number]) => b - a)
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }));

    // Source distribution
    const sourceStats = {
      xai: filteredHistory.filter(s => s.source === "xai").length,
      tweetio: filteredHistory.filter(s => s.source === "tweetio").length,
      hybrid: filteredHistory.filter(s => s.source === "hybrid").length
    };

    // Success rate over time
    const successRate = filteredHistory.length > 0 
      ? (filteredHistory.filter(s => s.success).length / filteredHistory.length) * 100
      : 0;

    // Time distribution (hourly for 24h, daily for longer periods)
    const timeDistribution: Array<{ time: string; searches: number; xaiSearches: number }> = [];
    
    if (timeRange === "24h" || timeRange === "1h") {
      // Hourly distribution
      for (let i = 23; i >= 0; i--) {
        const hourStart = now - (i * 60 * 60 * 1000);
        const hourEnd = hourStart + (60 * 60 * 1000);
        const hourSearches = filteredHistory.filter(s => s.timestamp >= hourStart && s.timestamp < hourEnd);
        
        timeDistribution.push({
          time: new Date(hourStart).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          searches: hourSearches.length,
          xaiSearches: hourSearches.filter(s => s.source === "xai").length
        });
      }
    } else {
      // Daily distribution
      const days = timeRange === "7d" ? 7 : 30;
      for (let i = days - 1; i >= 0; i--) {
        const dayStart = now - (i * 24 * 60 * 60 * 1000);
        const dayEnd = dayStart + (24 * 60 * 60 * 1000);
        const daySearches = filteredHistory.filter(s => s.timestamp >= dayStart && s.timestamp < dayEnd);
        
        timeDistribution.push({
          time: new Date(dayStart).toLocaleDateString([], { month: 'short', day: 'numeric' }),
          searches: daySearches.length,
          xaiSearches: daySearches.filter(s => s.source === "xai").length
        });
      }
    }

    // Performance metrics
    const avgResponseTime = filteredHistory.length > 0
      ? filteredHistory.reduce((sum, search) => {
          // Estimate response time based on source and complexity
          const baseTime = search.source === "xai" ? 3000 : 1500;
          const complexityMultiplier = (search.query.length / 50) + 1;
          return sum + (baseTime * complexityMultiplier);
        }, 0) / filteredHistory.length
      : 0;

    // Top topics and keywords
    const allWords = filteredHistory
      .map(s => s.query.toLowerCase().split(/\s+/))
      .flat()
      .filter(word => word.length > 3);
    
    const wordFreq: Record<string, number> = {};
    allWords.forEach(word => {
      wordFreq[word] = (wordFreq[word] || 0) + 1;
    });

    const topKeywords = Object.entries(wordFreq)
      .sort(([,a]: [string, number], [,b]: [string, number]) => b - a)
      .slice(0, 20)
      .map(([word, count]) => ({ word, count }));

    // Error analysis
    const errors = filteredHistory.filter(s => !s.success);
    const errorRate = filteredHistory.length > 0 ? (errors.length / filteredHistory.length) * 100 : 0;

    return {
      total: filteredHistory.length,
      popularQueries,
      sourceStats,
      successRate,
      errorRate,
      timeDistribution,
      avgResponseTime,
      topKeywords,
      errors: errors.slice(0, 5)
    };
  }, [searchHistory, timeRange]);

  const exportAnalytics = () => {
    const exportData = {
      timeRange,
      timestamp: Date.now(),
      analytics,
      realTimeStats,
      searchHistory: searchHistory.slice(0, 100) // Limit to recent searches
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `search-analytics-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getChangeIndicator = (current: number, previous: number) => {
    if (current > previous) return <ArrowUp className="h-3 w-3 text-green-400" />;
    if (current < previous) return <ArrowDown className="h-3 w-3 text-red-400" />;
    return <Minus className="h-3 w-3 text-[#6E7A8C]" />;
  };

  return (
    <div className="space-y-6">
      {/* Analytics Header */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="space-y-2">
              <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                <BarChart3 className="h-5 w-5 text-[#316FE3]" />
                Search Analytics Dashboard
              </CardTitle>
              <CardDescription className="text-[#6E7A8C]">
                Comprehensive insights into your search patterns and performance
              </CardDescription>
            </div>
            <div className="flex items-center gap-3">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32 bg-[#000000]/40 border-[#202631] text-[#F5F7FA]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  <SelectItem value="1h" className="text-[#F5F7FA] hover:bg-[#202631]">Last Hour</SelectItem>
                  <SelectItem value="24h" className="text-[#F5F7FA] hover:bg-[#202631]">Last 24h</SelectItem>
                  <SelectItem value="7d" className="text-[#F5F7FA] hover:bg-[#202631]">Last 7 days</SelectItem>
                  <SelectItem value="30d" className="text-[#F5F7FA] hover:bg-[#202631]">Last 30 days</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="sm"
                onClick={exportAnalytics}
                className="border border-[#202631] hover:bg-[#202631] text-[#F5F7FA] hover:text-[#F5F7FA]"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-[#316FE3]/50 group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">
                  {analytics.total}
                </div>
                <div className="text-sm text-[#6E7A8C]">Total Searches</div>
              </div>
              <Search className="h-8 w-8 text-[#316FE3] group-hover:text-blue-400 transition-colors" />
            </div>
            <div className="mt-2 flex items-center gap-1 text-xs">
              {getChangeIndicator(analytics.total, realTimeStats.totalSearches)}
              <span className="text-[#6E7A8C]">{timeRange} period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-purple-500/50 group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-purple-400 transition-colors">
                  {analytics.sourceStats.xai}
                </div>
                <div className="text-sm text-[#6E7A8C]">xAI Searches</div>
              </div>
              <Brain className="h-8 w-8 text-purple-500 group-hover:text-purple-400 transition-colors" />
            </div>
            <div className="mt-2 flex items-center gap-1 text-xs">
              <span className="text-purple-400">
                {analytics.total > 0 ? Math.round((analytics.sourceStats.xai / analytics.total) * 100) : 0}%
              </span>
              <span className="text-[#6E7A8C]">of total searches</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-green-500/50 group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-green-400 transition-colors">
                  {analytics.successRate.toFixed(1)}%
                </div>
                <div className="text-sm text-[#6E7A8C]">Success Rate</div>
              </div>
              <Target className="h-8 w-8 text-green-500 group-hover:text-green-400 transition-colors" />
            </div>
            <div className="mt-2 flex items-center gap-1 text-xs">
              {getChangeIndicator(analytics.successRate, realTimeStats.successRate)}
              <span className="text-[#6E7A8C]">
                {analytics.errors.length} failures
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-orange-500/50 group">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-orange-400 transition-colors">
                  {Math.round(analytics.avgResponseTime)}ms
                </div>
                <div className="text-sm text-[#6E7A8C]">Avg Response</div>
              </div>
              <Clock className="h-8 w-8 text-orange-500 group-hover:text-orange-400 transition-colors" />
            </div>
            <div className="mt-2 flex items-center gap-1 text-xs">
              {getChangeIndicator(analytics.avgResponseTime, realTimeStats.averageResponseTime)}
              <span className="text-[#6E7A8C]">response time</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search Volume Trends */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <TrendingUp className="h-5 w-5 text-[#316FE3]" />
            Search Volume Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Simple bar chart representation */}
            <div className="space-y-2">
              {analytics.timeDistribution.slice(-12).map((dataPoint, index) => {
                const maxSearches = Math.max(...analytics.timeDistribution.map(d => d.searches));
                const widthPercentage = maxSearches > 0 ? (dataPoint.searches / maxSearches) * 100 : 0;
                const xaiWidthPercentage = dataPoint.searches > 0 ? (dataPoint.xaiSearches / dataPoint.searches) * 100 : 0;
                
                return (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-16 text-xs text-[#6E7A8C] text-right">
                      {dataPoint.time}
                    </div>
                    <div className="flex-1 relative">
                      <div className="w-full bg-[#202631] rounded-full h-4">
                        <div 
                          className="bg-[#316FE3]/50 h-4 rounded-full relative overflow-hidden" 
                          style={{ width: `${widthPercentage}%` }}
                        >
                          <div 
                            className="bg-[#316FE3] h-4 rounded-full" 
                            style={{ width: `${xaiWidthPercentage}%` }}
                          />
                        </div>
                      </div>
                      <div className="absolute right-2 top-0 text-xs text-[#F5F7FA] leading-4">
                        {dataPoint.searches}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            
            <div className="flex items-center gap-4 text-xs text-[#6E7A8C] mt-4">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-[#316FE3]/50 rounded-full" />
                <span>Total Searches</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-[#316FE3] rounded-full" />
                <span>xAI Searches</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Popular Queries and Keywords */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Popular Queries */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <Search className="h-5 w-5 text-[#316FE3]" />
              Popular Queries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {analytics.popularQueries.slice(0, 8).map((query, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-[#000000]/40 border border-[#202631] rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-[#316FE3]/20 text-[#316FE3] text-xs rounded-full flex items-center justify-center font-medium">
                      {index + 1}
                    </div>
                    <span className="text-[#F5F7FA] text-sm truncate max-w-[200px]">
                      {query.query}
                    </span>
                  </div>
                  <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                    {query.count}x
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Keywords */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <Activity className="h-5 w-5 text-[#316FE3]" />
              Trending Keywords
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {analytics.topKeywords.slice(0, 15).map((keyword, index) => {
                const maxCount = Math.max(...analytics.topKeywords.map(k => k.count));
                const intensity = keyword.count / maxCount;
                
                return (
                  <Badge 
                    key={index}
                    variant="outline" 
                    className={cn(
                      "text-xs cursor-default",
                      intensity > 0.7 
                        ? "border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/20"
                        : intensity > 0.4
                        ? "border-[#316FE3]/30 text-[#316FE3] bg-[#316FE3]/10"
                        : "border-[#202631] text-[#6E7A8C]"
                    )}
                  >
                    {keyword.word} ({keyword.count})
                  </Badge>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Source Distribution and Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Source Distribution */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <Globe className="h-5 w-5 text-[#316FE3]" />
              Search Sources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4 text-[#316FE3]" />
                    <span className="text-[#F5F7FA] text-sm">xAI Grok</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#F5F7FA] text-sm">{analytics.sourceStats.xai}</span>
                    <Badge variant="outline" className="text-xs border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10">
                      {analytics.total > 0 ? Math.round((analytics.sourceStats.xai / analytics.total) * 100) : 0}%
                    </Badge>
                  </div>
                </div>
                <div className="w-full bg-[#202631] rounded-full h-2">
                  <div 
                    className="bg-[#316FE3] h-2 rounded-full" 
                    style={{ width: `${analytics.total > 0 ? (analytics.sourceStats.xai / analytics.total) * 100 : 0}%` }}
                  />
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-[#6E7A8C]" />
                    <span className="text-[#F5F7FA] text-sm">TweetIO</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#F5F7FA] text-sm">{analytics.sourceStats.tweetio}</span>
                    <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                      {analytics.total > 0 ? Math.round((analytics.sourceStats.tweetio / analytics.total) * 100) : 0}%
                    </Badge>
                  </div>
                </div>
                <div className="w-full bg-[#202631] rounded-full h-2">
                  <div 
                    className="bg-[#6E7A8C] h-2 rounded-full" 
                    style={{ width: `${analytics.total > 0 ? (analytics.sourceStats.tweetio / analytics.total) * 100 : 0}%` }}
                  />
                </div>
              </div>

              {analytics.sourceStats.hybrid > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-purple-400" />
                      <span className="text-[#F5F7FA] text-sm">Hybrid</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-[#F5F7FA] text-sm">{analytics.sourceStats.hybrid}</span>
                      <Badge variant="outline" className="text-xs border-purple-500/50 text-purple-400 bg-purple-500/10">
                        {analytics.total > 0 ? Math.round((analytics.sourceStats.hybrid / analytics.total) * 100) : 0}%
                      </Badge>
                    </div>
                  </div>
                  <div className="w-full bg-[#202631] rounded-full h-2">
                    <div 
                      className="bg-purple-400 h-2 rounded-full" 
                      style={{ width: `${analytics.total > 0 ? (analytics.sourceStats.hybrid / analytics.total) * 100 : 0}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Error Analysis */}
        {analytics.errors.length > 0 && (
          <Card className="bg-[#000000]/70 border-red-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                <AlertTriangle className="h-5 w-5 text-red-400" />
                Recent Errors ({analytics.errorRate.toFixed(1)}%)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {analytics.errors.map((error, index) => (
                  <div key={index} className="p-3 bg-red-500/5 border border-red-500/20 rounded-lg">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <div className="text-[#F5F7FA] text-sm font-medium truncate">
                          {error.query}
                        </div>
                        <div className="text-red-400 text-xs mt-1">
                          {error.error || "Unknown error"}
                        </div>
                      </div>
                      <div className="text-xs text-[#6E7A8C] flex-shrink-0">
                        {new Date(error.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Real-time Stats Summary */}
      <Card className="bg-gradient-to-br from-[#316FE3]/10 via-[#000000]/70 to-purple-600/10 border-[#316FE3]/30 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <Activity className="h-5 w-5 text-[#316FE3]" />
            Session Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-[#F5F7FA]">{realTimeStats.totalSearches}</div>
              <div className="text-sm text-[#6E7A8C]">Session Searches</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-[#316FE3]">{realTimeStats.xaiSearches}</div>
              <div className="text-sm text-[#6E7A8C]">xAI Enhanced</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-[#F5F7FA]">{realTimeStats.averageResponseTime}ms</div>
              <div className="text-sm text-[#6E7A8C]">Avg Response</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-400">{realTimeStats.successRate}%</div>
              <div className="text-sm text-[#6E7A8C]">Success Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}