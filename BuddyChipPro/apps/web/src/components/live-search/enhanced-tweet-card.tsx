import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { 
  Heart, 
  Repeat, 
  MessageCircle, 
  ExternalLink,
  Eye,
  TrendingUp,
  Clock,
  User,
  CheckCircle,
  Copy,
  Share,
  MoreHorizontal,
  Bookmark,
  Flag,
  Activity,
  BarChart3,
  Sparkles
} from "lucide-react";
import { cn } from "../../lib/utils";

interface TweetData {
  id: string;
  content: string;
  author?: string;
  authorHandle?: string;
  authorProfileImage?: string;
  authorVerified?: boolean;
  authorFollowers?: number;
  createdAt?: number;
  timestamp?: number;
  citations?: string[];
  engagement?: {
    likes: number;
    retweets: number;
    replies: number;
    views?: number;
  };
  url?: string;
  isRetweet?: boolean;
  quotedTweet?: any;
  mediaUrls?: string[];
  hashtags?: string[];
  mentions?: string[];
  sentiment?: "positive" | "neutral" | "negative";
  relevanceScore?: number;
  type?: string;
}

interface EnhancedTweetCardProps {
  tweet: TweetData;
  searchQuery?: string;
  showAnalytics?: boolean;
  onAnalyze?: (tweet: TweetData) => void;
}

export function EnhancedTweetCard({ 
  tweet, 
  searchQuery, 
  showAnalytics = false,
  onAnalyze 
}: EnhancedTweetCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  // Handle special xAI content type
  if (tweet.type === "xai-analysis") {
    return (
      <Card className="bg-gradient-to-br from-[#316FE3]/10 via-[#000000]/70 to-purple-600/10 border-[#316FE3]/30 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-[#316FE3] to-purple-600 rounded-full flex items-center justify-center">
              <Sparkles className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-[#F5F7FA]">xAI Grok Analysis</span>
                <Badge variant="outline" className="text-xs border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10">
                  Live Context
                </Badge>
              </div>
              <div className="text-xs text-[#6E7A8C] flex items-center gap-2">
                <Clock className="h-3 w-3" />
                {new Date(tweet.timestamp || Date.now()).toLocaleString()}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="prose prose-invert max-w-none">
            <div className="text-[#F5F7FA] whitespace-pre-wrap leading-relaxed">
              {tweet.content}
            </div>
          </div>
          
          {tweet.citations && tweet.citations.length > 0 && (
            <div className="mt-4 p-3 bg-[#000000]/40 border border-[#202631] rounded-lg">
              <h4 className="text-[#F5F7FA] font-medium mb-2 text-sm flex items-center gap-2">
                <ExternalLink className="h-3 w-3" />
                Sources ({tweet.citations.length})
              </h4>
              <div className="space-y-1">
                {tweet.citations.slice(0, 3).map((citation: string, index: number) => (
                  <a 
                    key={index}
                    href={citation} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="block text-[#316FE3] hover:text-blue-400 text-xs truncate hover:underline"
                  >
                    {citation}
                  </a>
                ))}
                {tweet.citations.length > 3 && (
                  <div className="text-xs text-[#6E7A8C]">
                    +{tweet.citations.length - 3} more sources
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getEngagementTotal = () => {
    if (!tweet.engagement) return 0;
    return (tweet.engagement.likes || 0) + 
           (tweet.engagement.retweets || 0) + 
           (tweet.engagement.replies || 0);
  };

  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment) {
      case "positive": return "text-green-400";
      case "negative": return "text-red-400";
      default: return "text-[#6E7A8C]";
    }
  };

  const getSentimentIcon = (sentiment?: string) => {
    switch (sentiment) {
      case "positive": return "😊";
      case "negative": return "😞";
      default: return "😐";
    }
  };

  const highlightSearchQuery = (text: string) => {
    if (!searchQuery) return text;
    
    const regex = new RegExp(`(${searchQuery})`, 'gi');
    return text.replace(regex, '<mark class="bg-[#316FE3]/30 text-[#316FE3] px-1 rounded">$1</mark>');
  };

  const handleCopyTweet = () => {
    const text = `${tweet.content}\n\nBy: ${tweet.author} (@${tweet.authorHandle})\nURL: ${tweet.url}`;
    navigator.clipboard.writeText(text);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  const isHighEngagement = getEngagementTotal() > 1000;
  const isVerified = tweet.authorVerified;
  const isInfluencer = (tweet.authorFollowers || 0) > 100000;

  return (
    <Card className={cn(
      "bg-[#000000]/70 border-[#202631] backdrop-blur-sm transition-all duration-300 hover:bg-[#000000]/90 group",
      isHighEngagement && "hover:border-orange-500/50",
      isVerified && "border-l-4 border-l-blue-500/50",
      tweet.isRetweet && "border-green-500/30"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start gap-3">
          {/* Author Avatar */}
          <Avatar className="w-12 h-12 border-2 border-[#202631]">
            <AvatarImage src={tweet.authorProfileImage} alt={tweet.author} />
            <AvatarFallback className="bg-[#316FE3] text-white">
              {tweet.author?.charAt(0)?.toUpperCase() || <User className="h-5 w-5" />}
            </AvatarFallback>
          </Avatar>

          {/* Author Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="font-semibold text-[#F5F7FA] truncate">
                {tweet.author || "Unknown Author"}
              </span>
              {isVerified && (
                <CheckCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
              )}
              <span className="text-[#6E7A8C] text-sm truncate">
                @{tweet.authorHandle || "unknown"}
              </span>
              
              {/* Author Badges */}
              <div className="flex items-center gap-1">
                {isInfluencer && (
                  <Badge variant="outline" className="text-xs border-purple-500/50 text-purple-400 bg-purple-500/10">
                    Influencer
                  </Badge>
                )}
                {tweet.authorFollowers && tweet.authorFollowers > 10000 && (
                  <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                    {formatNumber(tweet.authorFollowers)} followers
                  </Badge>
                )}
              </div>
            </div>
            
            {/* Tweet Metadata */}
            <div className="flex items-center gap-4 mt-1 text-xs text-[#6E7A8C]">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {tweet.createdAt ? new Date(tweet.createdAt).toLocaleString() : "Unknown time"}
              </div>
              {tweet.sentiment && (
                <div className={cn("flex items-center gap-1", getSentimentColor(tweet.sentiment))}>
                  <span>{getSentimentIcon(tweet.sentiment)}</span>
                  <span className="capitalize">{tweet.sentiment}</span>
                </div>
              )}
              {tweet.relevanceScore && (
                <div className="flex items-center gap-1 text-[#316FE3]">
                  <TrendingUp className="h-3 w-3" />
                  {Math.round(tweet.relevanceScore * 100)}% relevant
                </div>
              )}
            </div>
          </div>

          {/* Action Menu */}
          <div className="flex items-center gap-1">
            {tweet.isRetweet && (
              <Badge variant="outline" className="text-xs border-green-500/50 text-green-400 bg-green-500/10">
                <Repeat className="h-3 w-3 mr-1" />
                RT
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 w-8 p-0 hover:bg-[#202631]"
            >
              <MoreHorizontal className="h-4 w-4 text-[#6E7A8C]" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Tweet Content */}
        <div className="text-[#F5F7FA] leading-relaxed">
          <div 
            dangerouslySetInnerHTML={{ 
              __html: highlightSearchQuery(tweet.content) 
            }}
          />
        </div>

        {/* Hashtags and Mentions */}
        {(tweet.hashtags || tweet.mentions) && (
          <div className="flex flex-wrap gap-1">
            {tweet.hashtags?.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10">
                #{tag}
              </Badge>
            ))}
            {tweet.mentions?.map((mention, index) => (
              <Badge key={index} variant="outline" className="text-xs border-purple-500/50 text-purple-400 bg-purple-500/10">
                @{mention}
              </Badge>
            ))}
          </div>
        )}

        {/* Media (if present) */}
        {tweet.mediaUrls && tweet.mediaUrls.length > 0 && (
          <div className="grid grid-cols-2 gap-2">
            {tweet.mediaUrls.slice(0, 4).map((url, index) => (
              <div key={index} className="aspect-video bg-[#202631] rounded-lg overflow-hidden">
                <img 
                  src={url} 
                  alt={`Media ${index + 1}`}
                  className="w-full h-full object-cover hover:scale-105 transition-transform"
                />
              </div>
            ))}
          </div>
        )}

        {/* Engagement Metrics */}
        {tweet.engagement && (
          <div className="flex items-center justify-between pt-3 border-t border-[#202631]">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-1 text-[#6E7A8C] hover:text-red-400 transition-colors">
                <Heart className="h-4 w-4" />
                <span className="text-sm">{formatNumber(tweet.engagement.likes || 0)}</span>
              </div>
              <div className="flex items-center gap-1 text-[#6E7A8C] hover:text-green-400 transition-colors">
                <Repeat className="h-4 w-4" />
                <span className="text-sm">{formatNumber(tweet.engagement.retweets || 0)}</span>
              </div>
              <div className="flex items-center gap-1 text-[#6E7A8C] hover:text-blue-400 transition-colors">
                <MessageCircle className="h-4 w-4" />
                <span className="text-sm">{formatNumber(tweet.engagement.replies || 0)}</span>
              </div>
              {tweet.engagement.views && (
                <div className="flex items-center gap-1 text-[#6E7A8C]">
                  <Eye className="h-4 w-4" />
                  <span className="text-sm">{formatNumber(tweet.engagement.views)}</span>
                </div>
              )}
            </div>

            {/* Engagement Score */}
            {isHighEngagement && (
              <Badge variant="outline" className="text-xs border-orange-500/50 text-orange-400 bg-orange-500/10">
                <Activity className="h-3 w-3 mr-1" />
                High Engagement
              </Badge>
            )}
          </div>
        )}

        {/* Expanded Actions */}
        {isExpanded && (
          <div className="flex items-center gap-2 pt-3 border-t border-[#202631]">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyTweet}
              className="border-[#202631] hover:bg-[#202631] text-[#F5F7FA] hover:text-[#F5F7FA]"
            >
              <Copy className="h-3 w-3 mr-1" />
              {isCopied ? "Copied!" : "Copy"}
            </Button>
            
            {tweet.url && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(tweet.url, '_blank')}
                className="border-[#202631] hover:bg-[#202631] text-[#F5F7FA] hover:text-[#F5F7FA]"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                View Original
              </Button>
            )}
            
            {showAnalytics && onAnalyze && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAnalyze(tweet)}
                className="border-[#316FE3]/50 hover:bg-[#316FE3]/10 text-[#316FE3] hover:text-[#316FE3]"
              >
                <BarChart3 className="h-3 w-3 mr-1" />
                Analyze
              </Button>
            )}
            
            <Button
              variant="outline"
              size="sm"
              className="border-[#202631] hover:bg-[#202631] text-[#F5F7FA] hover:text-[#F5F7FA]"
            >
              <Bookmark className="h-3 w-3 mr-1" />
              Save
            </Button>
          </div>
        )}

        {/* Analytics Preview */}
        {showAnalytics && tweet.engagement && (
          <div className="mt-4 p-3 bg-[#000000]/40 border border-[#202631] rounded-lg">
            <h4 className="text-[#F5F7FA] font-medium mb-2 text-sm flex items-center gap-2">
              <BarChart3 className="h-3 w-3" />
              Quick Analytics
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
              <div className="text-center">
                <div className="text-[#F5F7FA] font-medium">{formatNumber(getEngagementTotal())}</div>
                <div className="text-[#6E7A8C]">Total Engagement</div>
              </div>
              <div className="text-center">
                <div className="text-[#F5F7FA] font-medium">
                  {tweet.engagement.retweets && tweet.engagement.likes 
                    ? ((tweet.engagement.retweets / tweet.engagement.likes) * 100).toFixed(1)
                    : 0}%
                </div>
                <div className="text-[#6E7A8C]">RT Rate</div>
              </div>
              <div className="text-center">
                <div className="text-[#F5F7FA] font-medium">
                  {tweet.engagement.replies && tweet.engagement.likes
                    ? ((tweet.engagement.replies / tweet.engagement.likes) * 100).toFixed(1)
                    : 0}%
                </div>
                <div className="text-[#6E7A8C]">Reply Rate</div>
              </div>
              <div className="text-center">
                <div className={cn("font-medium", getSentimentColor(tweet.sentiment))}>
                  {tweet.sentiment || "Unknown"}
                </div>
                <div className="text-[#6E7A8C]">Sentiment</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}