import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { EnhancedTweetCard } from "./enhanced-tweet-card";
import { 
  MessageSquare, 
  ExternalLink,
  Filter,
  SortAsc,
  SortDesc,
  Search,
  Clock,
  TrendingUp,
  Heart,
  Repeat,
  Eye,
  Download,
  RefreshCw,
  Zap,
  Brain,
  Target,
  Globe,
  AlertCircle,
  CheckCircle,
  Copy,
  Share
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SearchResult {
  id: string;
  source: "xai" | "tweetio" | "hybrid";
  query: string;
  content?: string;
  citations?: string[];
  results?: any[];
  insights?: any;
  timestamp: number;
  success: boolean;
  error?: string;
}

interface SearchResultsProps {
  currentSearch: SearchResult | null;
  searchHistory: SearchResult[];
  onSearchSelect: (search: SearchResult) => void;
}

export function SearchResults({ currentSearch, searchHistory, onSearchSelect }: SearchResultsProps) {
  const [filterBy, setFilterBy] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("relevance");
  const [searchFilter, setSearchFilter] = useState("");
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set());

  // Filter and sort results
  const getDisplayResults = () => {
    if (!currentSearch) return [];
    
    let results: any[] = [];
    
    if (currentSearch.source === "xai" && currentSearch.content) {
      // Parse xAI content for structured results
      results = [{
        id: "xai-content",
        type: "xai-analysis",
        content: currentSearch.content,
        citations: currentSearch.citations || [],
        timestamp: currentSearch.timestamp,
        insights: currentSearch.insights
      }];
    } else if (currentSearch.results) {
      results = currentSearch.results;
    }
    
    // Apply filters
    let filtered = results.filter(result => {
      if (filterBy === "all") return true;
      if (filterBy === "high-engagement" && result.engagement) {
        const total = (result.engagement.likes || 0) + (result.engagement.retweets || 0) + (result.engagement.replies || 0);
        return total > 100;
      }
      if (filterBy === "verified" && result.authorVerified !== undefined) {
        return result.authorVerified;
      }
      if (filterBy === "recent") {
        const hourAgo = Date.now() - (60 * 60 * 1000);
        return result.createdAt > hourAgo;
      }
      return true;
    });
    
    // Apply search filter
    if (searchFilter) {
      filtered = filtered.filter(result => 
        result.content?.toLowerCase().includes(searchFilter.toLowerCase()) ||
        result.author?.toLowerCase().includes(searchFilter.toLowerCase()) ||
        result.authorHandle?.toLowerCase().includes(searchFilter.toLowerCase())
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return (b.createdAt || b.timestamp || 0) - (a.createdAt || a.timestamp || 0);
        case "oldest":
          return (a.createdAt || a.timestamp || 0) - (b.createdAt || b.timestamp || 0);
        case "engagement":
          const aTotal = a.engagement ? (a.engagement.likes + a.engagement.retweets + a.engagement.replies) : 0;
          const bTotal = b.engagement ? (b.engagement.likes + b.engagement.retweets + b.engagement.replies) : 0;
          return bTotal - aTotal;
        case "relevance":
        default:
          return 0; // Keep original order for relevance
      }
    });
    
    return filtered;
  };

  const displayResults = getDisplayResults();

  const handleExportResults = () => {
    if (!currentSearch) return;
    
    const exportData = {
      query: currentSearch.query,
      source: currentSearch.source,
      timestamp: currentSearch.timestamp,
      results: displayResults,
      citations: currentSearch.citations
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `search-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleCopyResults = () => {
    if (!currentSearch) return;
    
    let text = `Search Query: ${currentSearch.query}\n`;
    text += `Source: ${currentSearch.source}\n`;
    text += `Timestamp: ${new Date(currentSearch.timestamp).toLocaleString()}\n\n`;
    
    if (currentSearch.source === "xai" && currentSearch.content) {
      text += `xAI Analysis:\n${currentSearch.content}\n\n`;
      if (currentSearch.citations && currentSearch.citations.length > 0) {
        text += `Citations:\n${currentSearch.citations.map(c => `- ${c}`).join('\n')}\n`;
      }
    } else {
      text += `Results (${displayResults.length}):\n\n`;
      displayResults.forEach((result, i) => {
        text += `${i + 1}. ${result.content}\n`;
        if (result.author) text += `   By: ${result.author} (@${result.authorHandle})\n`;
        if (result.engagement) {
          text += `   Engagement: ${result.engagement.likes} likes, ${result.engagement.retweets} retweets, ${result.engagement.replies} replies\n`;
        }
        text += `   URL: ${result.url}\n\n`;
      });
    }
    
    navigator.clipboard.writeText(text);
  };

  if (!currentSearch) {
    return (
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardContent className="py-12">
          <div className="text-center">
            <Search className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No Search Results</h3>
            <p className="text-[#6E7A8C]">
              Start a search to see results here. Use the Search tab to begin.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentSearch.success) {
    return (
      <Card className="bg-[#000000]/70 border-red-900/50 backdrop-blur-sm">
        <CardContent className="py-12">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">Search Failed</h3>
            <p className="text-red-400 mb-4">
              {currentSearch.error || "An unknown error occurred"}
            </p>
            <p className="text-[#6E7A8C] text-sm">
              Query: "{currentSearch.query}"
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Results Header */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="space-y-2">
              <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                {currentSearch.source === "xai" ? (
                  <Brain className="h-5 w-5 text-[#316FE3]" />
                ) : (
                  <MessageSquare className="h-5 w-5 text-[#316FE3]" />
                )}
                Search Results
                <Badge 
                  variant="outline" 
                  className={cn(
                    "text-xs",
                    currentSearch.source === "xai" 
                      ? "border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10" 
                      : "border-[#6E7A8C]/50 text-[#6E7A8C]"
                  )}
                >
                  {currentSearch.source === "xai" ? "xAI Powered" : "TweetIO"}
                </Badge>
              </CardTitle>
              <CardDescription className="text-[#6E7A8C]">
                Query: "{currentSearch.query}" • {displayResults.length} results found
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyResults}
                className="border-[#202631] hover:bg-[#202631] text-[#F5F7FA]"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportResults}
                className="border-[#202631] hover:bg-[#202631] text-[#F5F7FA]"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters and Controls */}
      {displayResults.length > 0 && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#F5F7FA]">Filter Results</label>
                <Select value={filterBy} onValueChange={setFilterBy}>
                  <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#000000] border-[#202631]">
                    <SelectItem value="all" className="text-[#F5F7FA] hover:bg-[#202631]">All Results</SelectItem>
                    <SelectItem value="high-engagement" className="text-[#F5F7FA] hover:bg-[#202631]">High Engagement</SelectItem>
                    <SelectItem value="verified" className="text-[#F5F7FA] hover:bg-[#202631]">Verified Only</SelectItem>
                    <SelectItem value="recent" className="text-[#F5F7FA] hover:bg-[#202631]">Recent (1h)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-[#F5F7FA]">Sort By</label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#000000] border-[#202631]">
                    <SelectItem value="relevance" className="text-[#F5F7FA] hover:bg-[#202631]">Relevance</SelectItem>
                    <SelectItem value="newest" className="text-[#F5F7FA] hover:bg-[#202631]">Newest First</SelectItem>
                    <SelectItem value="oldest" className="text-[#F5F7FA] hover:bg-[#202631]">Oldest First</SelectItem>
                    <SelectItem value="engagement" className="text-[#F5F7FA] hover:bg-[#202631]">Most Engaged</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-[#F5F7FA]">Search in Results</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#6E7A8C]" />
                  <Input
                    value={searchFilter}
                    onChange={(e) => setSearchFilter(e.target.value)}
                    placeholder="Filter results..."
                    className="pl-10 bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Display */}
      {displayResults.length === 0 ? (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardContent className="py-12">
            <div className="text-center">
              <Filter className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No Results Match Filters</h3>
              <p className="text-[#6E7A8C]">
                Try adjusting your filters or search terms.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* xAI Content Analysis Results */}
          {currentSearch.source === "xai" && currentSearch.content && (
            <Card className="bg-gradient-to-br from-[#316FE3]/10 via-[#000000]/70 to-purple-600/10 border-[#316FE3]/30 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                  <Brain className="h-5 w-5 text-[#316FE3]" />
                  xAI Live Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="prose prose-invert max-w-none">
                  <div className="text-[#F5F7FA] whitespace-pre-wrap leading-relaxed">
                    {currentSearch.content}
                  </div>
                </div>
                
                {currentSearch.citations && currentSearch.citations.length > 0 && (
                  <div className="mt-6 p-4 bg-[#000000]/40 border border-[#202631] rounded-lg">
                    <h4 className="text-[#F5F7FA] font-medium mb-3 flex items-center gap-2">
                      <ExternalLink className="h-4 w-4" />
                      Sources & Citations ({currentSearch.citations.length})
                    </h4>
                    <div className="space-y-2">
                      {currentSearch.citations.map((citation, index) => (
                        <div key={index} className="flex items-center gap-2 p-2 bg-[#000000]/40 border border-[#202631] rounded">
                          <Globe className="h-3 w-3 text-[#6E7A8C] flex-shrink-0" />
                          <a 
                            href={citation} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-[#316FE3] hover:text-blue-400 text-sm truncate flex-1 hover:underline"
                          >
                            {citation}
                          </a>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigator.clipboard.writeText(citation)}
                            className="h-6 w-6 p-0 hover:bg-[#202631]"
                          >
                            <Copy className="h-3 w-3 text-[#6E7A8C]" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Tweet/Social Media Results */}
          {currentSearch.results && currentSearch.results.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm text-[#6E7A8C] bg-[#000000]/40 px-4 py-2 rounded-lg border border-[#202631]">
                <span className="text-[#F5F7FA]">
                  Showing {displayResults.length} of {currentSearch.results.length} results
                </span>
                <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                  {currentSearch.source === "xai" ? "Live Data" : "Social Media"}
                </Badge>
              </div>
              
              {displayResults.map((result, index) => (
                <div 
                  key={result.id || index}
                  className="animate-in slide-in-from-bottom-4 duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <EnhancedTweetCard 
                    tweet={result}
                    searchQuery={currentSearch.query}
                    showAnalytics={true}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Search History Sidebar */}
      {searchHistory.length > 1 && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-[#F5F7FA] text-lg flex items-center gap-2">
              <Clock className="h-5 w-5 text-[#316FE3]" />
              Recent Searches
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {searchHistory.slice(0, 5).map((search, index) => (
                <div 
                  key={search.id}
                  onClick={() => onSearchSelect(search)}
                  className={cn(
                    "flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-all duration-200",
                    search.id === currentSearch.id
                      ? "bg-[#316FE3]/20 border-[#316FE3]/50"
                      : "bg-[#000000]/40 border-[#202631] hover:bg-[#000000]/60 hover:border-[#316FE3]/30"
                  )}
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "text-xs flex-shrink-0",
                        search.source === "xai" 
                          ? "border-[#316FE3]/50 text-[#316FE3]" 
                          : "border-[#6E7A8C]/50 text-[#6E7A8C]"
                      )}
                    >
                      {search.source === "xai" ? "xAI" : "TweetIO"}
                    </Badge>
                    <span className="text-[#F5F7FA] truncate">
                      {search.query}
                    </span>
                    {search.success ? (
                      <CheckCircle className="h-3 w-3 text-green-400 flex-shrink-0" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-red-400 flex-shrink-0" />
                    )}
                  </div>
                  <div className="text-xs text-[#6E7A8C] flex-shrink-0">
                    {new Date(search.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}