import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import { Checkbox } from "../ui/checkbox";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { 
  Search, 
  Zap, 
  Calendar,
  Filter,
  TrendingUp,
  Users,
  Globe,
  Newspaper,
  Rss,
  Settings,
  Clock,
  Sparkles,
  Target,
  History
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SearchParams {
  query: string;
  searchType: "general" | "mentions" | "trends" | "analysis";
  sources: string[];
  dateRange?: { from: string; to: string };
  maxResults?: number;
  filters?: any;
  useXAI?: boolean;
}

interface SearchInterfaceProps {
  onSearch: (params: SearchParams) => void;
  isLoading: boolean;
  searchHistory: any[];
}

export function SearchInterface({ onSearch, isLoading, searchHistory }: SearchInterfaceProps) {
  const [query, setQuery] = useState("");
  const [searchType, setSearchType] = useState<"general" | "mentions" | "trends" | "analysis">("general");
  const [useXAI, setUseXAI] = useState(true);
  const [sources, setSources] = useState<string[]>(["x", "web"]);
  const [maxResults, setMaxResults] = useState(20);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [dateRange, setDateRange] = useState<{ from: string; to: string } | undefined>();
  const [filters, setFilters] = useState({
    verified: false,
    minEngagement: 0,
    country: "",
    language: "",
    excludeWords: "",
    xHandles: ""
  });
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // Popular search suggestions
  const popularQueries = [
    "AI trends 2024",
    "crypto news",
    "startup funding",
    "tech innovation",
    "social media marketing",
    "blockchain development",
    "machine learning",
    "remote work",
    "fintech solutions",
    "web3 development"
  ];

  // Generate search suggestions based on input
  useEffect(() => {
    if (query.length > 2) {
      const filtered = popularQueries.filter(q => 
        q.toLowerCase().includes(query.toLowerCase())
      );
      const recent = searchHistory
        .filter(h => h.query.toLowerCase().includes(query.toLowerCase()))
        .map(h => h.query)
        .slice(0, 3);
      
      setSuggestions([...new Set([...filtered, ...recent])].slice(0, 5));
    } else {
      setSuggestions(popularQueries.slice(0, 5));
    }
  }, [query, searchHistory]);

  const handleSourceToggle = (source: string) => {
    setSources(prev => 
      prev.includes(source) 
        ? prev.filter(s => s !== source)
        : [...prev, source]
    );
  };

  const handleSearch = () => {
    if (!query.trim()) return;

    const searchParams: SearchParams = {
      query: query.trim(),
      searchType,
      sources,
      maxResults,
      useXAI,
      ...(dateRange && { dateRange }),
      ...(showAdvanced && { 
        filters: {
          ...filters,
          xHandles: filters.xHandles ? filters.xHandles.split(',').map(h => h.trim()) : undefined
        }
      })
    };

    onSearch(searchParams);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSearch();
    }
  };

  const getSearchTypeIcon = (type: string) => {
    switch (type) {
      case "mentions": return <Users className="h-4 w-4" />;
      case "trends": return <TrendingUp className="h-4 w-4" />;
      case "analysis": return <Target className="h-4 w-4" />;
      default: return <Search className="h-4 w-4" />;
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case "x": return <span className="text-xs font-bold">𝕏</span>;
      case "web": return <Globe className="h-3 w-3" />;
      case "news": return <Newspaper className="h-3 w-3" />;
      case "rss": return <Rss className="h-3 w-3" />;
      default: return <Globe className="h-3 w-3" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Search Card */}
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
            <Search className="h-5 w-5 text-[#316FE3]" />
            Live Search Interface
          </CardTitle>
          <CardDescription className="text-[#6E7A8C]">
            Powered by xAI Grok for real-time insights and TweetIO for comprehensive coverage
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search Input */}
          <div className="space-y-2">
            <Label htmlFor="search-query" className="text-[#F5F7FA] font-medium">Search Query</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#6E7A8C]" />
              <Input
                id="search-query"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter your search query..."
                className="pl-10 bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C] focus:border-[#316FE3]/50 focus:ring-[#316FE3]/20 text-lg py-3"
              />
              {useXAI && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Badge variant="outline" className="text-xs border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10">
                    <Zap className="h-3 w-3 mr-1" />
                    xAI
                  </Badge>
                </div>
              )}
            </div>
            
            {/* Search Suggestions */}
            {suggestions.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {suggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => setQuery(suggestion)}
                    className="text-xs border border-[#202631] hover:bg-[#202631] text-[#6E7A8C] hover:text-[#F5F7FA] transition-colors"
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            )}
          </div>

          {/* Search Type and xAI Toggle */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-[#F5F7FA] font-medium">Search Type</Label>
              <Select value={searchType} onValueChange={(value) => setSearchType(value as any)}>
                <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] hover:bg-[#000000]/60">
                  <div className="flex items-center gap-2">
                    {getSearchTypeIcon(searchType)}
                    <SelectValue />
                  </div>
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  <SelectItem value="general" className="text-[#F5F7FA] hover:bg-[#202631]">
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4" />
                      General Search
                    </div>
                  </SelectItem>
                  <SelectItem value="mentions" className="text-[#F5F7FA] hover:bg-[#202631]">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Mentions & Discussions
                    </div>
                  </SelectItem>
                  <SelectItem value="trends" className="text-[#F5F7FA] hover:bg-[#202631]">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Trending Topics
                    </div>
                  </SelectItem>
                  <SelectItem value="analysis" className="text-[#F5F7FA] hover:bg-[#202631]">
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Content Analysis
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-[#F5F7FA] font-medium">AI Enhancement</Label>
              <div className="flex items-center justify-between p-3 bg-[#000000]/40 border border-[#202631] rounded-md">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-[#316FE3]" />
                  <span className="text-[#F5F7FA] text-sm">Use xAI Grok</span>
                </div>
                <Switch 
                  checked={useXAI} 
                  onCheckedChange={setUseXAI}
                  className="data-[state=checked]:bg-[#316FE3]"
                />
              </div>
            </div>
          </div>

          {/* Sources Selection */}
          <div className="space-y-2">
            <Label className="text-[#F5F7FA] font-medium">Data Sources</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {[
                { id: "x", label: "X/Twitter", icon: "x" },
                { id: "web", label: "Web", icon: "web" },
                { id: "news", label: "News", icon: "news" },
                { id: "rss", label: "RSS", icon: "rss" }
              ].map((source) => (
                <div key={source.id} className="flex items-center space-x-2">
                  <Checkbox 
                    id={source.id}
                    checked={sources.includes(source.id)}
                    onCheckedChange={() => handleSourceToggle(source.id)}
                    className="border-[#202631] data-[state=checked]:bg-[#316FE3] data-[state=checked]:border-[#316FE3]"
                  />
                  <Label 
                    htmlFor={source.id} 
                    className="text-[#F5F7FA] text-sm flex items-center gap-1 cursor-pointer"
                  >
                    {getSourceIcon(source.icon)}
                    {source.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Advanced Filters Toggle */}
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-[#6E7A8C] hover:text-[#F5F7FA] hover:bg-[#202631] p-0"
            >
              <Settings className="h-4 w-4 mr-2" />
              Advanced Filters
              <span className={cn("ml-2 transition-transform", showAdvanced && "rotate-180")}>
                ▼
              </span>
            </Button>
            
            <div className="flex items-center gap-2">
              <Label htmlFor="max-results" className="text-[#6E7A8C] text-sm">Max Results:</Label>
              <Select value={maxResults.toString()} onValueChange={(value) => setMaxResults(parseInt(value))}>
                <SelectTrigger className="w-20 bg-[#000000]/40 border-[#202631] text-[#F5F7FA]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-[#000000] border-[#202631]">
                  <SelectItem value="10" className="text-[#F5F7FA] hover:bg-[#202631]">10</SelectItem>
                  <SelectItem value="20" className="text-[#F5F7FA] hover:bg-[#202631]">20</SelectItem>
                  <SelectItem value="50" className="text-[#F5F7FA] hover:bg-[#202631]">50</SelectItem>
                  <SelectItem value="100" className="text-[#F5F7FA] hover:bg-[#202631]">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Advanced Filters Panel */}
          {showAdvanced && (
            <Card className="bg-[#000000]/40 border-[#202631]">
              <CardContent className="p-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-[#F5F7FA] text-sm">Date Range From</Label>
                    <Input
                      type="date"
                      value={dateRange?.from || ""}
                      onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value, to: prev?.to || "" }))}
                      className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-[#F5F7FA] text-sm">Date Range To</Label>
                    <Input
                      type="date"
                      value={dateRange?.to || ""}
                      onChange={(e) => setDateRange(prev => ({ from: prev?.from || "", to: e.target.value }))}
                      className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-[#F5F7FA] text-sm">X/Twitter Handles</Label>
                    <Input
                      value={filters.xHandles}
                      onChange={(e) => setFilters(prev => ({ ...prev, xHandles: e.target.value }))}
                      placeholder="@handle1, @handle2"
                      className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-[#F5F7FA] text-sm">Exclude Words</Label>
                    <Input
                      value={filters.excludeWords}
                      onChange={(e) => setFilters(prev => ({ ...prev, excludeWords: e.target.value }))}
                      placeholder="word1, word2"
                      className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C]"
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="verified"
                    checked={filters.verified}
                    onCheckedChange={(checked) => setFilters(prev => ({ ...prev, verified: checked as boolean }))}
                    className="border-[#202631] data-[state=checked]:bg-[#316FE3]"
                  />
                  <Label htmlFor="verified" className="text-[#F5F7FA] text-sm">
                    Verified accounts only
                  </Label>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Search Button */}
          <Button 
            onClick={handleSearch}
            disabled={!query.trim() || isLoading || sources.length === 0}
            className="w-full bg-gradient-to-r from-[#316FE3] to-purple-600 hover:from-[#316FE3]/90 hover:to-purple-600/90 text-white font-medium py-3 text-lg disabled:opacity-50"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Searching...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                {useXAI ? <Zap className="h-5 w-5" /> : <Search className="h-5 w-5" />}
                Start Live Search
              </div>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Recent Searches */}
      {searchHistory.length > 0 && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA] text-lg">
              <History className="h-5 w-5 text-[#316FE3]" />
              Recent Searches
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {searchHistory.slice(0, 5).map((search, index) => (
                <div 
                  key={search.id}
                  onClick={() => setQuery(search.query)}
                  className="flex items-center justify-between p-3 bg-[#000000]/40 border border-[#202631] rounded-lg hover:bg-[#000000]/60 cursor-pointer transition-colors group"
                >
                  <div className="flex items-center gap-3">
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "text-xs border-[#202631]",
                        search.source === "xai" ? "text-[#316FE3] border-[#316FE3]/50" : "text-[#6E7A8C]"
                      )}
                    >
                      {search.source === "xai" ? "xAI" : "TweetIO"}
                    </Badge>
                    <span className="text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">
                      {search.query}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-[#6E7A8C]">
                    <Clock className="h-3 w-3" />
                    {new Date(search.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}