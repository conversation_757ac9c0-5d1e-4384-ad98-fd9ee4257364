import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { 
  Brain, 
  TrendingUp, 
  Users, 
  Target,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  BarChart3,
  Zap,
  Globe,
  MessageCircle,
  Eye,
  Activity,
  Sparkles,
  ArrowUp,
  ArrowDown,
  Minus,
  RefreshCw,
  Copy
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SearchResult {
  id: string;
  source: "xai" | "tweetio" | "hybrid";
  query: string;
  content?: string;
  citations?: string[];
  results?: any[];
  insights?: any;
  timestamp: number;
  success: boolean;
  error?: string;
}

interface XAIInsightsProps {
  currentSearch: SearchResult | null;
  onAnalyze: (params: any) => Promise<any>;
}

export function XAIInsights({ currentSearch, onAnalyze }: XAIInsightsProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [selectedAnalysisType, setSelectedAnalysisType] = useState<string>("comprehensive");

  // Parse insights from xAI content
  const parseXAIInsights = (content: string) => {
    // This would ideally use structured parsing
    // For now, we'll extract key metrics and insights from the content
    
    const insights = {
      sentiment: {
        overall: "neutral" as "positive" | "neutral" | "negative",
        confidence: 0.7,
        breakdown: {
          positive: 0.3,
          neutral: 0.4,
          negative: 0.3
        }
      },
      trends: {
        trending: [] as string[],
        declining: [] as string[],
        emerging: [] as string[]
      },
      engagement: {
        averageEngagement: 0,
        highPerformers: [],
        topHashtags: [],
        influentialUsers: []
      },
      opportunities: [] as string[],
      threats: [] as string[],
      recommendations: [] as string[]
    };

    // Simple pattern matching for sentiment
    if (content.toLowerCase().includes("positive") || content.toLowerCase().includes("optimistic")) {
      insights.sentiment.overall = "positive";
      insights.sentiment.breakdown.positive = 0.6;
    } else if (content.toLowerCase().includes("negative") || content.toLowerCase().includes("concerning")) {
      insights.sentiment.overall = "negative";
      insights.sentiment.breakdown.negative = 0.6;
    }

    // Extract trending topics (simplified)
    const trendMatches = content.match(/#\w+/g);
    if (trendMatches) {
      insights.trends.trending = trendMatches.slice(0, 5).map(tag => tag.replace('#', ''));
    }

    // Extract opportunities
    const opportunityPatterns = [
      /opportunity to ([^.]+)/gi,
      /potential for ([^.]+)/gi,
      /consider ([^.]+)/gi
    ];
    
    opportunityPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        insights.opportunities.push(...matches.slice(0, 3));
      }
    });

    return insights;
  };

  const performAnalysis = async () => {
    if (!currentSearch || !currentSearch.success) return;
    
    setIsAnalyzing(true);
    try {
      const analysisParams = {
        content: currentSearch.content || JSON.stringify(currentSearch.results),
        userContext: {
          expertise: ["social media", "marketing", "content strategy"],
          interests: ["engagement", "trends", "audience growth"],
          brand: "BuddyChip Pro",
          tone: "professional"
        },
        analysisType: selectedAnalysisType
      };

      const result = await onAnalyze(analysisParams);
      setAnalysisResult(result);
    } catch (error) {
      console.error("Analysis error:", error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Auto-generate insights from current search
  useEffect(() => {
    if (currentSearch?.success && currentSearch.source === "xai" && currentSearch.content) {
      const insights = parseXAIInsights(currentSearch.content);
      setAnalysisResult({ ...insights, autoGenerated: true });
    }
  }, [currentSearch]);

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case "positive": return <ArrowUp className="h-4 w-4 text-green-400" />;
      case "negative": return <ArrowDown className="h-4 w-4 text-red-400" />;
      default: return <Minus className="h-4 w-4 text-[#6E7A8C]" />;
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "positive": return "text-green-400 border-green-500/50 bg-green-500/10";
      case "negative": return "text-red-400 border-red-500/50 bg-red-500/10";
      default: return "text-[#6E7A8C] border-[#202631] bg-[#000000]/40";
    }
  };

  if (!currentSearch) {
    return (
      <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
        <CardContent className="py-12">
          <div className="text-center">
            <Brain className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No Search Data</h3>
            <p className="text-[#6E7A8C]">
              Perform a search to see xAI-powered insights and analysis.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentSearch.success) {
    return (
      <Card className="bg-[#000000]/70 border-red-900/50 backdrop-blur-sm">
        <CardContent className="py-12">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">Analysis Unavailable</h3>
            <p className="text-red-400 mb-4">
              Cannot analyze failed search results.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Insights Header */}
      <Card className="bg-gradient-to-br from-[#316FE3]/10 via-[#000000]/70 to-purple-600/10 border-[#316FE3]/30 backdrop-blur-sm">
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="space-y-2">
              <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                <Brain className="h-5 w-5 text-[#316FE3]" />
                xAI Live Insights
                {currentSearch.source === "xai" && (
                  <Badge variant="outline" className="text-xs border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10">
                    <Zap className="h-3 w-3 mr-1" />
                    Live Context
                  </Badge>
                )}
              </CardTitle>
              <CardDescription className="text-[#6E7A8C]">
                AI-powered analysis of "{currentSearch.query}" • {currentSearch.source === "xai" ? "Real-time insights" : "TweetIO data analysis"}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {currentSearch.source !== "xai" && (
                <Button
                  onClick={performAnalysis}
                  disabled={isAnalyzing}
                  className="bg-gradient-to-r from-[#316FE3] to-purple-600 hover:from-[#316FE3]/90 hover:to-purple-600/90 text-white"
                >
                  {isAnalyzing ? (
                    <div className="flex items-center gap-2">
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Analyzing...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Brain className="h-4 w-4" />
                      Analyze with xAI
                    </div>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {analysisResult && (
        <>
          {/* Sentiment Analysis */}
          <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                <Activity className="h-5 w-5 text-[#316FE3]" />
                Sentiment Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className={cn("p-4", getSentimentColor(analysisResult.sentiment?.overall || "neutral"))}>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-lg font-bold capitalize">
                        {analysisResult.sentiment?.overall || "Neutral"}
                      </div>
                      <div className="text-sm opacity-75">Overall Sentiment</div>
                    </div>
                    {getSentimentIcon(analysisResult.sentiment?.overall || "neutral")}
                  </div>
                  <div className="mt-2 text-xs opacity-75">
                    {Math.round((analysisResult.sentiment?.confidence || 0.7) * 100)}% confidence
                  </div>
                </Card>

                <div className="space-y-2">
                  <div className="text-sm font-medium text-[#F5F7FA]">Sentiment Breakdown</div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-green-400">Positive</span>
                      <span className="text-[#F5F7FA]">{Math.round((analysisResult.sentiment?.breakdown?.positive || 0) * 100)}%</span>
                    </div>
                    <div className="w-full bg-[#202631] rounded-full h-2">
                      <div 
                        className="bg-green-400 h-2 rounded-full" 
                        style={{ width: `${(analysisResult.sentiment?.breakdown?.positive || 0) * 100}%` }}
                      />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-[#6E7A8C]">Neutral</span>
                      <span className="text-[#F5F7FA]">{Math.round((analysisResult.sentiment?.breakdown?.neutral || 0) * 100)}%</span>
                    </div>
                    <div className="w-full bg-[#202631] rounded-full h-2">
                      <div 
                        className="bg-[#6E7A8C] h-2 rounded-full" 
                        style={{ width: `${(analysisResult.sentiment?.breakdown?.neutral || 0) * 100}%` }}
                      />
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-red-400">Negative</span>
                      <span className="text-[#F5F7FA]">{Math.round((analysisResult.sentiment?.breakdown?.negative || 0) * 100)}%</span>
                    </div>
                    <div className="w-full bg-[#202631] rounded-full h-2">
                      <div 
                        className="bg-red-400 h-2 rounded-full" 
                        style={{ width: `${(analysisResult.sentiment?.breakdown?.negative || 0) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Trending Topics */}
          {analysisResult.trends && (
            <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                  <TrendingUp className="h-5 w-5 text-[#316FE3]" />
                  Trending Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {analysisResult.trends.trending.length > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-green-400 flex items-center gap-1">
                        <ArrowUp className="h-3 w-3" />
                        Trending Up
                      </div>
                      <div className="space-y-1">
                        {analysisResult.trends.trending.slice(0, 5).map((trend: string, index: number) => (
                          <Badge 
                            key={index} 
                            variant="outline" 
                            className="text-xs border-green-500/50 text-green-400 bg-green-500/10"
                          >
                            #{trend}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {analysisResult.trends.emerging.length > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-[#316FE3] flex items-center gap-1">
                        <Sparkles className="h-3 w-3" />
                        Emerging Topics
                      </div>
                      <div className="space-y-1">
                        {analysisResult.trends.emerging.slice(0, 5).map((trend: string, index: number) => (
                          <Badge 
                            key={index} 
                            variant="outline" 
                            className="text-xs border-[#316FE3]/50 text-[#316FE3] bg-[#316FE3]/10"
                          >
                            #{trend}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {analysisResult.trends.declining.length > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm font-medium text-red-400 flex items-center gap-1">
                        <ArrowDown className="h-3 w-3" />
                        Declining
                      </div>
                      <div className="space-y-1">
                        {analysisResult.trends.declining.slice(0, 5).map((trend: string, index: number) => (
                          <Badge 
                            key={index} 
                            variant="outline" 
                            className="text-xs border-red-500/50 text-red-400 bg-red-500/10"
                          >
                            #{trend}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Opportunities & Recommendations */}
          {(analysisResult.opportunities?.length > 0 || analysisResult.recommendations?.length > 0) && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Opportunities */}
              {analysisResult.opportunities?.length > 0 && (
                <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                      <Target className="h-5 w-5 text-green-400" />
                      Opportunities
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysisResult.opportunities.slice(0, 5).map((opportunity: string, index: number) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-green-500/5 border border-green-500/20 rounded-lg">
                          <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5" />
                          <div className="text-sm text-[#F5F7FA]">
                            {opportunity}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Threats/Concerns */}
              {analysisResult.threats?.length > 0 && (
                <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                      <AlertTriangle className="h-5 w-5 text-red-400" />
                      Threats & Concerns
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analysisResult.threats.slice(0, 5).map((threat: string, index: number) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-red-500/5 border border-red-500/20 rounded-lg">
                          <AlertTriangle className="h-4 w-4 text-red-400 flex-shrink-0 mt-0.5" />
                          <div className="text-sm text-[#F5F7FA]">
                            {threat}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Strategic Recommendations */}
              {analysisResult.recommendations?.length > 0 && (
                <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                      <Lightbulb className="h-5 w-5 text-[#316FE3]" />
                      Strategic Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {analysisResult.recommendations.slice(0, 6).map((rec: string, index: number) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-[#316FE3]/5 border border-[#316FE3]/20 rounded-lg">
                          <Lightbulb className="h-4 w-4 text-[#316FE3] flex-shrink-0 mt-0.5" />
                          <div className="text-sm text-[#F5F7FA]">
                            {rec}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Key Metrics */}
          {analysisResult.engagement && (
            <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                  <BarChart3 className="h-5 w-5 text-[#316FE3]" />
                  Engagement Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-[#000000]/40 border border-[#202631] rounded-lg">
                    <div className="text-2xl font-bold text-[#F5F7FA]">
                      {analysisResult.engagement.averageEngagement || "N/A"}
                    </div>
                    <div className="text-sm text-[#6E7A8C]">Avg Engagement</div>
                  </div>
                  <div className="text-center p-4 bg-[#000000]/40 border border-[#202631] rounded-lg">
                    <div className="text-2xl font-bold text-[#F5F7FA]">
                      {analysisResult.engagement.highPerformers?.length || 0}
                    </div>
                    <div className="text-sm text-[#6E7A8C]">High Performers</div>
                  </div>
                  <div className="text-center p-4 bg-[#000000]/40 border border-[#202631] rounded-lg">
                    <div className="text-2xl font-bold text-[#F5F7FA]">
                      {analysisResult.engagement.topHashtags?.length || 0}
                    </div>
                    <div className="text-sm text-[#6E7A8C]">Top Hashtags</div>
                  </div>
                  <div className="text-center p-4 bg-[#000000]/40 border border-[#202631] rounded-lg">
                    <div className="text-2xl font-bold text-[#F5F7FA]">
                      {analysisResult.engagement.influentialUsers?.length || 0}
                    </div>
                    <div className="text-sm text-[#6E7A8C]">Key Influencers</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Citations & Sources */}
          {currentSearch.citations && currentSearch.citations.length > 0 && (
            <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
                  <ExternalLink className="h-5 w-5 text-[#316FE3]" />
                  Sources & Citations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {currentSearch.citations.slice(0, 8).map((citation, index) => (
                    <div key={index} className="flex items-center gap-2 p-3 bg-[#000000]/40 border border-[#202631] rounded-lg">
                      <Globe className="h-4 w-4 text-[#6E7A8C] flex-shrink-0" />
                      <a 
                        href={citation} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-[#316FE3] hover:text-blue-400 text-sm truncate flex-1 hover:underline"
                      >
                        {citation}
                      </a>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Auto-generated Notice */}
          {analysisResult.autoGenerated && (
            <Card className="bg-[#316FE3]/5 border-[#316FE3]/20 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-[#316FE3]">
                  <Zap className="h-4 w-4" />
                  <span>This analysis was automatically generated from xAI live search results</span>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}