import React from 'react'
import { ImSpinner8 } from "react-icons/im";

const AnimatedLogo = () => {
  const pathStyle = {
    strokeDasharray: '100',
    strokeDashoffset: '100',
    animation: 'drawLine 1000ms cubic-bezier(0.65, 0, 0.35, 1) 1000ms forwards'
  };

  const extStyle = {
    strokeDasharray: '20',
    strokeDashoffset: '20',
    animation: 'drawLine 300ms cubic-bezier(0.65, 0, 0.35, 1) forwards'
  };

  return (
    <>
      <style>{`
        @keyframes drawLine {
          to {
            stroke-dashoffset: 0;
          }
        }
      `}</style>
      <svg width="90" height="90" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_2008_323)">
          {/* Main border - appears first */}
          <path 
            d="M7 5.5H23C23.8284 5.5 24.5 6.17157 24.5 7V23C24.5 23.8284 23.8284 24.5 23 24.5H7C6.17157 24.5 5.5 23.8284 5.5 23V7C5.5 6.17157 6.17157 5.5 7 5.5Z" 
            stroke="#F5F7FA"
            style={{...pathStyle, animationDelay: '1000ms'}}
          />
          
          {/* B letter vertical line */}
          <path 
            d="M11 9V21" 
            stroke="#F5F7FA" 
            strokeWidth="2"
            style={{...pathStyle, animationDelay: '1200ms'}}
          />
          
          {/* B letter top curve */}
          <path 
            d="M11 10H16.5C17.8807 10 19 11.1193 19 12.5V12.5C19 13.8807 17.8807 15 16.5 15H11" 
            stroke="#F5F7FA" 
            strokeWidth="2"
            style={{...pathStyle, animationDelay: '1400ms'}}
          />
          
          {/* B letter bottom curve */}
          <path 
            d="M11 15H16.5C17.8807 15 19 16.1193 19 17.5V17.5C19 18.8807 17.8807 20 16.5 20H11" 
            stroke="#F5F7FA" 
            strokeWidth="2"
            style={{...pathStyle, animationDelay: '1600ms'}}
          />
          
          {/* Extension lines - appear last */}
          <path d="M10.5 6V0" stroke="#F5F7FA" style={{...extStyle, animationDelay: '1800ms'}} />
          <path d="M19.5 6V0" stroke="#F5F7FA" style={{...extStyle, animationDelay: '1900ms'}} />
          <path d="M24 10.5L30 10.5" stroke="#F5F7FA" style={{...extStyle, animationDelay: '2000ms'}} />
          <path d="M24 19.5L30 19.5" stroke="#F5F7FA" style={{...extStyle, animationDelay: '2100ms'}} />
          <path d="M19.5 24L19.5 30" stroke="#F5F7FA" style={{...extStyle, animationDelay: '2200ms'}} />
          <path d="M10.5 24L10.5 30" stroke="#F5F7FA" style={{...extStyle, animationDelay: '2300ms'}} />
          <path d="M6 19.5L0 19.5" stroke="#F5F7FA" style={{...extStyle, animationDelay: '2400ms'}} />
          <path d="M6 10.5L0 10.5" stroke="#F5F7FA" style={{...extStyle, animationDelay: '2500ms'}} />
        </g>
        <defs>
          <clipPath id="clip0_2008_323">
            <rect width="30" height="30" fill="white"/>
          </clipPath>
        </defs>
      </svg>
    </>
  );
};

const Loading = () => {
  return (
    <div className='h-[100vh] bg-[#000000] flex items-center justify-center p-8 relative'>
      <div className="logo-container">
        <AnimatedLogo />
      </div>
      <ImSpinner8 className='text-[#316FE3] absolute bottom-[2rem] right-[2rem] text-[1.5rem] animate-spin' />
    </div>
  )
}

export default Loading