/**
 * Custom 404 Not Found Page Component
 * User-friendly 404 page that matches BuddyChip design system
 * with helpful navigation options and analytics tracking.
 */

import { useState, useEffect, useRef } from "react";
import { Link, useRouter } from "@tanstack/react-router";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Input } from "../ui/input";
import { 
  Home, 
  ArrowLeft, 
  Search, 
  MessageSquare, 
  Image, 
  TrendingUp,
  ExternalLink,
  RefreshCw,
  MapPin,
} from "lucide-react";
import { useAuth } from "../auth/use-auth";
import { getUrlRedirectManager, RedirectSuggestion } from "@/lib/url-redirects";
import { useConvex } from "convex/react";
import { useNotFoundAnalytics } from "@/lib/404-analytics";

interface NotFoundPageProps {
  /** The URL that was not found */
  requestedUrl?: string;
  /** Custom message to display */
  message?: string;
  /** Whether to show search functionality */
  showSearch?: boolean;
  /** Whether to show redirect suggestions */
  showSuggestions?: boolean;
}

export function NotFoundPage({ 
  requestedUrl,
  message,
  showSearch = true,
  showSuggestions = true,
}: NotFoundPageProps) {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const convex = useConvex();
  const { track404Error, trackUserAction, trackRedirectAttempt } = useNotFoundAnalytics(convex);
  
  const [searchQuery, setSearchQuery] = useState("");
  const [redirectSuggestions, setRedirectSuggestions] = useState<RedirectSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const hasTrackedRef = useRef(false);

  const currentUrl = requestedUrl || (typeof window !== 'undefined' ? window.location.pathname : '/unknown');

  useEffect(() => {
    console.log('🔍 404 Page loaded for URL:', currentUrl);
    
    // Track the 404 error only once per component mount
    if (!hasTrackedRef.current) {
      hasTrackedRef.current = true;
      track404Error({
        requestedUrl: currentUrl,
        userId: user?.id,
      });
    }

    // Find redirect suggestions if enabled
    if (showSuggestions) {
      const redirectManager = getUrlRedirectManager();
      const suggestions = redirectManager.findRedirectSuggestions(currentUrl);
      setRedirectSuggestions(suggestions);
      
      console.log('💡 Found redirect suggestions:', suggestions);
    }
  }, [currentUrl, showSuggestions, user?.id]);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    console.log('🔍 Searching for:', searchQuery);
    
    await trackUserAction('search', { query: searchQuery });

    // Simple search logic - redirect to live-search with query
    if (isAuthenticated) {
      router.navigate({ to: '/live-search', search: { q: searchQuery } });
    } else {
      // For non-authenticated users, redirect to sign-in
      router.navigate({ to: '/sign-in' });
    }
    
    setIsLoading(false);
  };

  const handleSuggestionClick = async (suggestion: RedirectSuggestion) => {
    console.log('💡 User clicked suggestion:', suggestion.url);
    
    await trackRedirectAttempt({
      originalUrl: currentUrl,
      suggestedUrl: suggestion.url,
      accepted: true,
      confidence: suggestion.confidence,
      timestamp: Date.now(),
    });

    await trackUserAction('suggestion_click', { 
      suggestedUrl: suggestion.url,
      confidence: suggestion.confidence,
    });

    router.navigate({ to: suggestion.url });
  };

  const handleGoBack = async () => {
    console.log('⬅️ User clicked go back');
    await trackUserAction('go_back');
    
    if (window.history.length > 1) {
      window.history.back();
    } else {
      router.navigate({ to: '/' });
    }
  };

  const handleGoHome = async () => {
    console.log('🏠 User clicked go home');
    await trackUserAction('go_home');
    router.navigate({ to: '/' });
  };

  const quickLinks = [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      description: 'Your main control center',
      requireAuth: true,
    },
    {
      label: 'Tweet Assistant',
      href: '/tweet-assistant',
      icon: MessageSquare,
      description: 'AI-powered tweet creation',
      requireAuth: true,
    },
    {
      label: 'Image Generation',
      href: '/image-generation',
      icon: Image,
      description: 'Create stunning visuals',
      requireAuth: true,
    },
    {
      label: 'Live Search',
      href: '/live-search',
      icon: TrendingUp,
      description: 'Real-time Twitter monitoring',
      requireAuth: true,
    },
    {
      label: 'Mentions',
      href: '/mentions',
      icon: MapPin,
      description: 'Track your mentions',
      requireAuth: true,
    },
  ];

  const availableLinks = quickLinks.filter(link => !link.requireAuth || isAuthenticated);

  return (
    <div className="min-h-screen bg-[var(--buddychip-app-bg)] flex items-center justify-center p-4">
      <div className="max-w-2xl w-full space-y-6">
        {/* Main 404 Card */}
        <Card className="bg-[var(--buddychip-card-bg)] border-[var(--buddychip-border)] text-center">
          <div className="p-8 space-y-6">
            {/* 404 Visual */}
            <div className="space-y-4">
              <div className="text-6xl font-bold text-[var(--buddychip-accent)]">404</div>
              <h1 className="text-2xl font-bold text-[var(--buddychip-white)]">
                Page Not Found
              </h1>
              <p className="text-[var(--buddychip-grey-text)] max-w-md mx-auto">
                {message || `The page "${currentUrl}" doesn't exist or has been moved. Let's get you back on track.`}
              </p>
            </div>

            {/* Search Bar */}
            {showSearch && (
              <form onSubmit={handleSearch} className="max-w-md mx-auto">
                <div className="flex gap-2">
                  <Input
                    type="text"
                    placeholder="Search for content..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-[var(--buddychip-black)] border-[var(--buddychip-border)] text-[var(--buddychip-white)]"
                  />
                  <Button 
                    type="submit" 
                    disabled={isLoading}
                    className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/80"
                  >
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Search className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </form>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={handleGoBack}
                variant="outline"
                className="border-[var(--buddychip-border)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-grey-stroke)]"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              
              <Button
                onClick={handleGoHome}
                className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/80"
              >
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>
            </div>
          </div>
        </Card>

        {/* Redirect Suggestions */}
        {showSuggestions && redirectSuggestions.length > 0 && (
          <Card className="bg-[var(--buddychip-card-bg)] border-[var(--buddychip-border)]">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-[var(--buddychip-white)] mb-4">
                Did you mean?
              </h2>
              <div className="space-y-2">
                {redirectSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left p-3 rounded-lg border border-[var(--buddychip-border)] hover:bg-[var(--buddychip-grey-stroke)] transition-colors group"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-[var(--buddychip-white)] font-medium">
                          {suggestion.url}
                        </div>
                        <div className="text-sm text-[var(--buddychip-grey-text)]">
                          {suggestion.reason}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-[var(--buddychip-grey-text)]">
                          {Math.round(suggestion.confidence * 100)}% match
                        </span>
                        <ExternalLink className="h-4 w-4 text-[var(--buddychip-grey-text)] group-hover:text-[var(--buddychip-accent)]" />
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </Card>
        )}

        {/* Quick Links */}
        {availableLinks.length > 0 && (
          <Card className="bg-[var(--buddychip-card-bg)] border-[var(--buddychip-border)]">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-[var(--buddychip-white)] mb-4">
                Quick Links
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {availableLinks.map((link) => (
                  <Link
                    key={link.href}
                    to={link.href}
                    onClick={() => trackUserAction('quick_link_click', { href: link.href })}
                    className="p-3 rounded-lg border border-[var(--buddychip-border)] hover:bg-[var(--buddychip-grey-stroke)] transition-colors group"
                  >
                    <div className="flex items-center gap-3">
                      <link.icon className="h-5 w-5 text-[var(--buddychip-accent)] group-hover:text-[var(--buddychip-white)]" />
                      <div>
                        <div className="text-[var(--buddychip-white)] font-medium">
                          {link.label}
                        </div>
                        <div className="text-sm text-[var(--buddychip-grey-text)]">
                          {link.description}
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </Card>
        )}

        {/* Help Text */}
        <div className="text-center text-sm text-[var(--buddychip-grey-text)]">
          <p>
            Still can't find what you're looking for?{" "}
            {isAuthenticated ? (
              <Link 
                to="/dashboard" 
                className="text-[var(--buddychip-accent)] hover:underline"
                onClick={() => trackUserAction('help_link_click', { target: 'dashboard' })}
              >
                Visit your dashboard
              </Link>
            ) : (
              <Link 
                to="/sign-in" 
                className="text-[var(--buddychip-accent)] hover:underline"
                onClick={() => trackUserAction('help_link_click', { target: 'sign-in' })}
              >
                Sign in to access all features
              </Link>
            )}
          </p>
        </div>
      </div>
    </div>
  );
}
