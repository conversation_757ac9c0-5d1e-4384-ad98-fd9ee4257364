import { ReactNode } from 'react';
import { <PERSON>Header, PageContainer } from '../ui/page-header';

interface PageLayoutProps {
  title: string;
  subtitle?: string;
  actions?: ReactNode;
  children: ReactNode;
  headerChildren?: ReactNode;
  maxWidth?: "default" | "wide" | "full";
  background?: "default" | "gradient";
  className?: string;
}

export function PageLayout({
  title,
  subtitle,
  actions,
  children,
  headerChildren,
  maxWidth = "default",
  background = "default",
  className = ""
}: PageLayoutProps) {
  const bgClass = background === "gradient" 
    ? "min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] text-[var(--buddychip-white)]"
    : "min-h-screen bg-[#0E1117] text-white";

  return (
    <div className={`${bgClass} ${className}`}>
      <PageContainer maxWidth={maxWidth}>
        <PageHeader
          title={title}
          subtitle={subtitle}
          actions={actions}
        >
          {headerChildren}
        </PageHeader>
        {children}
      </PageContainer>
    </div>
  );
}