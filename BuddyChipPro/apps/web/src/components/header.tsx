import React, { useState } from 'react';
import { <PERSON>, useRouterState } from "@tanstack/react-router";
import { SignInButton, SignOutButton, SignedIn, SignedOut } from "@clerk/clerk-react";
import { useQuery, useMutation } from 'convex/react';
import { api } from '@BuddyChipAI/backend';

import { useAuth } from "./auth/use-auth";
import { Button } from "./ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "./ui/avatar";
import { Badge } from "./ui/badge";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from "./ui/dropdown-menu";
import { ChevronDown, Copy, Settings, Plus, Wallet, User, LogOut, Menu } from 'lucide-react';
import { WalletConnectionModal } from './wallet/wallet-connection-modal';
import { NotificationBell } from './reply-guy/notification-bell';
import { toast } from 'sonner';

function UserProfileSection() {
  const [showConnectionModal, setShowConnectionModal] = useState(false);
  const [selectedBlockchain, setSelectedBlockchain] = useState<'ethereum' | 'solana' | 'polygon' | 'base'>('solana');
  const { user } = useAuth();
  
  // Get user's wallets
  let wallets: any[] = [];
  let primaryWallet: any = null;
  
  try {
    wallets = useQuery(api.auth.walletDetection.getUserWallets) || [];
    primaryWallet = useQuery(api.auth.walletDetection.getPrimaryWallet);
  } catch (error) {
    console.warn('Wallet queries failed:', error);
  }
  
  // Mutations
  const setPrimaryWallet = useMutation(api.auth.walletDetection.setPrimaryWallet);
  const markWalletUsed = useMutation(api.walletMutations.markWalletUsed);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Address copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy address');
    }
  };

  const formatAddress = (address: string, length = 6) => {
    return `${address.slice(0, length)}...${address.slice(-4)}`;
  };

  const getBlockchainIcon = (blockchain: string) => {
    switch (blockchain) {
      case 'ethereum':
        return '⟠';
      case 'solana':
        return '◎';
      case 'polygon':
        return '🔷';
      case 'base':
        return '🔵';
      default:
        return '💰';
    }
  };

  const getWalletTypeIcon = (walletType: string) => {
    switch (walletType.toLowerCase()) {
      case 'phantom':
        return '👻';
      case 'metamask':
        return '🦊';
      case 'coinbase':
        return '🟦';
      case 'solflare':
        return '☀️';
      default:
        return '💼';
    }
  };

  const handleWalletSelect = async (walletId: string) => {
    try {
      await setPrimaryWallet({ walletId: walletId as any });
      await markWalletUsed({ walletId: walletId as any });
      toast.success('Primary wallet updated');
    } catch (error) {
      toast.error('Failed to update primary wallet');
    }
  };

  const handleConnectWallet = (blockchain: 'ethereum' | 'solana' | 'polygon' | 'base') => {
    setSelectedBlockchain(blockchain);
    setShowConnectionModal(true);
  };

  const getUserInitials = (name?: string) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className="h-auto p-2 hover:bg-[var(--buddychip-accent-bg)] hover:text-[var(--buddychip-accent)]"
          >
            <div className="flex items-center gap-3">
              {/* User Avatar */}
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.imageUrl} alt={user?.fullName || 'User'} />
                <AvatarFallback className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] text-sm">
                  {getUserInitials(user?.fullName || user?.firstName || undefined)}
                </AvatarFallback>
              </Avatar>
              
              {/* User Info */}
              <div className="flex flex-col items-start min-w-0">
                <span className="text-sm font-medium text-[var(--buddychip-white)] truncate max-w-[120px]">
                  {user?.fullName || user?.firstName || 'User'}
                </span>
                
                {/* Wallet Info or Connect Button */}
                {primaryWallet ? (
                  <div className="flex items-center gap-1">
                    <span className="text-xs">{getBlockchainIcon(primaryWallet.blockchain)}</span>
                    <span className="text-xs font-mono text-[var(--buddychip-grey-text)]">
                      {formatAddress(primaryWallet.address)}
                    </span>
                    {primaryWallet.metadata?.balance && (
                      <Badge variant="secondary" className="text-xs ml-1 bg-[var(--buddychip-accent)]/20 text-[var(--buddychip-accent)] border-[var(--buddychip-accent)]/30">
                        {parseFloat(primaryWallet.metadata.balance).toFixed(2)}
                      </Badge>
                    )}
                  </div>
                ) : (
                  <span className="text-xs text-[var(--buddychip-grey-text)]">No wallet connected</span>
                )}
              </div>
              
              <ChevronDown className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
            </div>
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent 
          align="end" 
          className="w-80 bg-[var(--buddychip-elevated-bg)] border-[var(--buddychip-border-strong)] shadow-lg"
        >
          {/* User Info Header */}
          <DropdownMenuLabel className="pb-2">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={user?.imageUrl} alt={user?.fullName || 'User'} />
                <AvatarFallback className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)]">
                  {getUserInitials(user?.fullName || user?.firstName || undefined)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="font-medium text-[var(--buddychip-white)]">
                  {user?.fullName || user?.firstName || 'User'}
                </span>
                <span className="text-sm text-[var(--buddychip-grey-text)]">
                  {user?.primaryEmailAddress?.emailAddress || ""}
                </span>
              </div>
            </div>
          </DropdownMenuLabel>
          
          <DropdownMenuSeparator />
          
          {/* Wallet Section */}
          <DropdownMenuLabel className="text-xs text-[var(--buddychip-grey-text)] uppercase tracking-wide">
            Wallets
          </DropdownMenuLabel>
          
          {/* No wallets connected */}
          {(!wallets || wallets.length === 0) && (
            <DropdownMenuItem 
              onClick={() => handleConnectWallet('solana')}
              className="py-3 px-3 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent-bg)] hover:text-[var(--buddychip-accent)] focus:bg-[var(--buddychip-accent-bg)] focus:text-[var(--buddychip-accent)]"
            >
              <Plus className="h-4 w-4 mr-2" />
              Connect your first wallet
            </DropdownMenuItem>
          )}
          
          {/* Connected wallets */}
          {wallets.map((wallet) => (
            <DropdownMenuItem
              key={wallet._id}
              onClick={() => handleWalletSelect(wallet._id)}
              className="flex items-center justify-between py-3 px-3 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent-bg)] hover:text-[var(--buddychip-accent)] focus:bg-[var(--buddychip-accent-bg)] focus:text-[var(--buddychip-accent)]"
            >
              <div className="flex items-center gap-2">
                <span>{getBlockchainIcon(wallet.blockchain)}</span>
                <span>{getWalletTypeIcon(wallet.walletType)}</span>
                <div className="flex flex-col">
                  <span className="font-mono text-sm">
                    {formatAddress(wallet.address, 8)}
                  </span>
                  <span className="text-xs text-[var(--buddychip-grey-text)] capitalize">
                    {wallet.blockchain} • {wallet.walletType}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-1">
                {wallet.isPrimary && (
                  <Badge variant="default" className="text-xs bg-[var(--buddychip-accent)] text-[var(--buddychip-white)]">
                    Primary
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    copyToClipboard(wallet.address);
                  }}
                  className="h-6 w-6 p-0 hover:bg-[var(--buddychip-grey-stroke)]"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </DropdownMenuItem>
          ))}
          
          {/* Add new wallet */}
          {wallets && wallets.length > 0 && (
            <DropdownMenuItem 
              onClick={() => handleConnectWallet('solana')}
              className="py-2.5 px-3 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/10 hover:text-[var(--buddychip-accent)] focus:bg-[var(--buddychip-accent)]/10 focus:text-[var(--buddychip-accent)]"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add another wallet
            </DropdownMenuItem>
          )}
          
          <DropdownMenuSeparator />
          
          {/* Account actions */}
          <DropdownMenuItem className="py-2.5 px-3 text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent)]/10 hover:text-[var(--buddychip-accent)] focus:bg-[var(--buddychip-accent)]/10 focus:text-[var(--buddychip-accent)]">
            <Settings className="h-4 w-4 mr-2" />
            Account Settings
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <SignOutButton>
            <DropdownMenuItem className="py-2.5 px-3 text-red-400 hover:bg-red-500/10 hover:text-red-400 focus:bg-red-500/10 focus:text-red-400">
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </DropdownMenuItem>
          </SignOutButton>
        </DropdownMenuContent>
      </DropdownMenu>

      <WalletConnectionModal
        isOpen={showConnectionModal}
        onClose={() => setShowConnectionModal(false)}
        blockchain={selectedBlockchain}
      />
    </>
  );
}

export default function Header() {
  const { user } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const currentPath = useRouterState({
    select: (s) => s.location.pathname,
  });

  // All possible navigation links
  const allLinks = [
    { to: "/", label: "Home", showWhenSignedOut: true },
    { to: "/dashboard", label: "Dashboard", showWhenSignedIn: true },
    { to: "/live-search", label: "Live Search", showWhenSignedIn: true },
    { to: "/mentions", label: "Mentions", showWhenSignedIn: true },
    { to: "/tweet-assistant", label: "Tweet Assistant", showWhenSignedIn: true },
    { to: "/image-generation", label: "AI Images", showWhenSignedIn: true },
  ];

  // Smart navigation: Filter based on auth state and current page
  const getVisibleLinks = () => {
    return allLinks.filter(link => {
      // Remove current page from navigation
      if (link.to === currentPath) return false;
      
      // Show appropriate links based on auth state
      if (user) {
        return link.showWhenSignedIn;
      } else {
        return link.showWhenSignedOut;
      }
    });
  };

  const visibleLinks = getVisibleLinks();

  return (
    <div className="bg-[var(--buddychip-app-bg)] border-b border-[var(--buddychip-border)]">
      <div className="flex flex-row items-center justify-between px-6 py-4">
        {/* Brand Logo */}
        <div className="flex items-center space-x-6">
          <Link 
            to={user ? "/dashboard" : "/"} 
            className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
          >
            {/* Use the actual logo SVG */}
            <div className="h-8 w-8 flex items-center justify-center">
              <img 
                src="/Logo.svg" 
                alt="BuddyChip Logo" 
                className="h-8 w-8 brightness-0 invert dark:brightness-100 dark:invert-0 transition-all duration-200"
              />
            </div>
            <span className="text-xl font-bold text-[var(--buddychip-white)]">
              BuddyChip
            </span>
          </Link>
          
          {/* Desktop Navigation Links */}
          {visibleLinks.length > 0 && (
            <nav className="hidden md:flex gap-6">
              {visibleLinks.map(({ to, label }) => {
                return (
                  <Link
                    key={to}
                    to={to}
                    className="text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)] transition-colors font-medium"
                  >
                    {label}
                  </Link>
                );
              })}
            </nav>
          )}
        </div>
        
        {/* Right Section */}
        <div className="flex items-center gap-3">
          {/* Mobile Menu Button */}
          {visibleLinks.length > 0 && (
            <SignedIn>
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)]"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <Menu className="h-5 w-5" />
              </Button>
            </SignedIn>
          )}

          <SignedOut>
            <SignInButton>
              <Button className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-accent-hover)]">
                Sign In
              </Button>
            </SignInButton>
          </SignedOut>
          
          <SignedIn>
            <NotificationBell />
            <UserProfileSection />
          </SignedIn>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {mobileMenuOpen && visibleLinks.length > 0 && (
        <div className="md:hidden border-t border-[var(--buddychip-border)] bg-[var(--buddychip-card-bg)]">
          <nav className="px-6 py-4 space-y-2">
            {visibleLinks.map(({ to, label }) => (
              <Link
                key={to}
                to={to}
                className="block py-2 text-[var(--buddychip-white)] hover:text-[var(--buddychip-accent)] transition-colors font-medium"
                onClick={() => setMobileMenuOpen(false)}
              >
                {label}
              </Link>
            ))}
          </nav>
        </div>
      )}
    </div>
  );
}
