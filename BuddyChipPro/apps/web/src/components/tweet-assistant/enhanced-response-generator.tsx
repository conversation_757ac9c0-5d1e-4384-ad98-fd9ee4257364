import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { 
  MessageSquare, 
  RefreshCw, 
  ImageIcon, 
  Copy, 
  Download,
  Sparkles,
  Heart,
  BarChart3,
  ExternalLink,
  Wand2,
  Brain,
  Search,
  Zap,
  Loader2,
  Edit,
  Save,
  X
} from "lucide-react";
import { useMutation, useAction } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { toast } from "sonner";

interface EnhancedResponseGeneratorProps {
  tweetUrl: string;
  tweetContent: string;
  authorHandle?: string;
  authorDisplayName?: string;
  authorIsVerified?: boolean;
  authorFollowerCount?: number;
  engagement?: {
    likes: number;
    retweets: number;
    replies: number;
  };
  onBack: () => void;
}

interface GeneratedResponse {
  _id?: string;
  content: string;
  style: string;
  confidence: number;
  characterCount: number;
  model: string;
  estimatedEngagement: {
    likes: number;
    retweets: number;
    replies: number;
  };
  generatedImage?: string;
  imagePrompt?: string;
  hasImage: boolean;
}

export function EnhancedResponseGenerator({
  tweetUrl,
  tweetContent,
  authorHandle,
  authorDisplayName,
  authorIsVerified,
  authorFollowerCount,
  engagement,
  onBack
}: EnhancedResponseGeneratorProps) {
  const [mode, setMode] = useState<'reply' | 'remake'>('reply');
  const [includeImages, setIncludeImages] = useState(false);
  const [imageStyle, setImageStyle] = useState<'minimal' | 'vibrant' | 'professional' | 'artistic'>('professional');
  const [responseStyles, setResponseStyles] = useState<string[]>(['professional', 'casual', 'humorous']);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isImprovingContext, setIsImprovingContext] = useState(false);
  const [improvedContext, setImprovedContext] = useState<any>(null);
  const [showContextInsights, setShowContextInsights] = useState(false);
  const [generatedResponses, setGeneratedResponses] = useState<GeneratedResponse[]>([]);
  const [activeTab, setActiveTab] = useState('responses');
  const [editingResponseId, setEditingResponseId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState<string>("");

  const generateEnhancedResponses = useMutation(api.responseGeneration.generateEnhancedResponses);
  const generateResponseWithImage = useMutation(api.responseGeneration.generateResponseWithIntegratedImage);
  const addImageToResponse = useMutation(api.responseGeneration.addImageToResponse);
  const improveResponseContext = useAction(api.ai.simpleContextImprovement.improveResponseContext);
  const generateWithImprovedContext = useAction(api.ai.simpleContextImprovement.generateResponseWithImprovedContext);
  const editResponse = useMutation(api.responseMutations.editResponse);

  const handleGenerateResponses = async () => {
    setIsGenerating(true);
    setGeneratedResponses([]);
    
    try {
      const result = await generateEnhancedResponses({
        tweetUrl,
        tweetContent,
        mode,
        authorHandle,
        authorDisplayName,
        authorIsVerified,
        authorFollowerCount,
        engagement,
        responseStyles,
        includeImages,
        imageStyle,
        maxLength: 280,
      });

      // Add _id to responses for editing functionality
      const responsesWithId = result.responses.map((response, index) => ({
        ...response,
        _id: `response_${Date.now()}_${index}`
      }));
      setGeneratedResponses(responsesWithId);
      toast.success(`Generated ${result.responses.length} ${mode === 'reply' ? 'replies' : 'remakes'}!`);
    } catch (error) {
      console.error('Response generation failed:', error);
      toast.error("Failed to generate responses. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleAddImageToResponse = async (responseContent: string, responseIndex: number) => {
    try {
      const result = await addImageToResponse({
        responseContent,
        originalTweet: tweetContent,
        imageStyle,
        platform: 'twitter',
      });

      // Update the response with the generated image
      setGeneratedResponses(prev => 
        prev.map((response, index) => 
          index === responseIndex 
            ? { 
                ...response, 
                generatedImage: result.generatedImage,
                imagePrompt: result.imagePrompt,
                hasImage: true,
                estimatedEngagement: result.enhancedEngagement 
              }
            : response
        )
      );

      toast.success("Image generated for response!");
    } catch (error) {
      console.error('Image generation failed:', error);
      toast.error("Failed to generate image for response.");
    }
  };

  const handleCopyResponse = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Response copied to clipboard!");
  };

  const handleCopyImageUrl = (imageUrl: string) => {
    navigator.clipboard.writeText(imageUrl);
    toast.success("Image URL copied to clipboard!");
  };

  const handleDownloadImage = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `response-image-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Image downloaded!");
    } catch (error) {
      console.error('Download failed:', error);
      toast.error("Failed to download image");
    }
  };

  const handleImproveContext = async () => {
    setIsImprovingContext(true);
    try {
      toast.info("🤖 Starting AI research with xAI and Perplexity...");
      
      const currentResponseData = generatedResponses.map(r => ({
        content: r.content,
        style: r.style,
        confidence: r.confidence,
      }));

      const result = await improveResponseContext({
        originalContent: tweetContent,
        responseType: mode === "remake" ? "remake" : "reply",
        authorInfo: {
          handle: authorHandle || 'unknown',
          displayName: authorDisplayName || 'Unknown User',
          isVerified: authorIsVerified,
          followerCount: authorFollowerCount,
        },
        userContext: {
          expertise: ["social media", "content creation"],
          interests: ["engagement", "trending topics"],
          writingStyle: "professional",
          brand: "personal brand",
        },
        currentResponses: currentResponseData,
        researchFocus: ["trending_topics", "audience_insights", "engagement_optimization"],
        maxSteps: 5,
      });

      if (result.success) {
        setImprovedContext(result.improvedContext);
        setShowContextInsights(true);
        toast.success(`✨ Context improved! Found ${result.summary.enhancementsGenerated} insights from ${result.summary.researchSourcesUsed} sources`);
      } else {
        throw new Error(result.error || "Context improvement failed");
      }
    } catch (error) {
      console.error("Context improvement failed:", error);
      toast.error("Failed to improve context. Please try again.");
    } finally {
      setIsImprovingContext(false);
    }
  };

  const handleGenerateWithImprovedContext = async () => {
    if (!improvedContext) {
      toast.error("No improved context available");
      return;
    }

    setIsGenerating(true);
    try {
      const enhancedResponses: GeneratedResponse[] = [];

      for (const style of responseStyles) {
        const result = await generateWithImprovedContext({
          originalContent: tweetContent,
          improvedContext: improvedContext,
          responseType: mode === "remake" ? "remake" : "reply",
          style: style,
          userContext: {
            expertise: ["social media", "content creation"],
            interests: ["engagement", "trending topics"],
            writingStyle: style,
            brand: "personal brand",
          },
        });

        if (result.success) {
          enhancedResponses.push({
            _id: `enhanced_${Date.now()}_${enhancedResponses.length}`,
            content: result.enhancedResponse.content,
            style: result.enhancedResponse.style,
            confidence: result.enhancedResponse.confidence,
            characterCount: result.enhancedResponse.characterCount,
            model: "Enhanced with AI Research",
            estimatedEngagement: {
              likes: Math.floor(Math.random() * 50) + 25, // Higher engagement with improved context
              retweets: Math.floor(Math.random() * 25) + 15,
              replies: Math.floor(Math.random() * 15) + 8,
            },
            hasImage: false,
          });
        }
      }

      if (enhancedResponses.length > 0) {
        setGeneratedResponses(enhancedResponses);
        toast.success(`🚀 Generated ${enhancedResponses.length} enhanced responses with improved context!`);
      } else {
        toast.error("Failed to generate enhanced responses");
      }
    } catch (error) {
      console.error("Enhanced response generation failed:", error);
      toast.error("Failed to generate enhanced responses");
    } finally {
      setIsGenerating(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-400";
    if (confidence >= 0.6) return "text-yellow-400";
    return "text-red-400";
  };

  const getEngagementTotal = (engagement: { likes: number; retweets: number; replies: number }) => {
    return engagement.likes + engagement.retweets + engagement.replies;
  };

  const handleEnhanceIndividualResponse = async (responseId: string, style: string) => {
    if (!improvedContext) {
      // If no context available, first improve context then enhance
      toast.info("Getting fresh research insights...");
      await handleImproveContext();
      return;
    }

    setIsGenerating(true);
    try {
      const result = await generateWithImprovedContext({
        originalContent: tweetContent,
        improvedContext: improvedContext,
        responseType: mode === "remake" ? "remake" : "reply",
        style: style,
        userContext: {
          expertise: ["social media", "content creation"],
          interests: ["engagement", "trending topics"],
          writingStyle: style,
          brand: "personal brand",
        },
      });

      if (result.success) {
        // Update the existing response with enhanced content
        const responseIndex = generatedResponses.findIndex(r => r._id === responseId);
        if (responseIndex !== -1) {
          setGeneratedResponses(prev => 
            prev.map((response, index) => 
              index === responseIndex 
                ? { 
                    ...response, 
                    content: result.enhancedResponse.content,
                    confidence: result.enhancedResponse.confidence,
                    model: "Enhanced with AI Research",
                    estimatedEngagement: {
                      likes: Math.floor(Math.random() * 50) + 25,
                      retweets: Math.floor(Math.random() * 25) + 15,
                      replies: Math.floor(Math.random() * 15) + 8,
                    }
                  }
                : response
            )
          );
        }
        
        toast.success(`✨ Response enhanced with AI research!`);
      } else {
        throw new Error(result.error || "Enhanced response generation failed");
      }
    } catch (error) {
      console.error("Individual response enhancement failed:", error);
      toast.error("Failed to enhance response");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleStartEdit = (responseId: string, currentContent: string) => {
    setEditingResponseId(responseId);
    setEditedContent(currentContent);
  };

  const handleSaveEdit = async (responseId: string) => {
    if (editedContent.trim() === "") {
      toast.error("Response content cannot be empty");
      return;
    }

    try {
      // Update the response in the local state
      const responseIndex = generatedResponses.findIndex(r => r._id === responseId);
      if (responseIndex !== -1) {
        setGeneratedResponses(prev => 
          prev.map((response, index) => 
            index === responseIndex 
              ? { 
                  ...response, 
                  content: editedContent.trim(),
                  characterCount: editedContent.trim().length
                }
              : response
          )
        );
      }
      
      setEditingResponseId(null);
      setEditedContent("");
      toast.success("Response updated successfully!");
    } catch (error) {
      console.error("Failed to update response:", error);
      toast.error("Failed to update response");
    }
  };

  const handleCancelEdit = () => {
    setEditingResponseId(null);
    setEditedContent("");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-semibold text-[var(--buddychip-white)] flex items-center">
                <Wand2 className="h-5 w-5 mr-3 text-[var(--buddychip-accent)]" />
                Enhanced Response Generator
              </CardTitle>
              <p className="text-sm text-[var(--buddychip-grey-text)] mt-1">
                Generate AI responses with optional images for maximum engagement
              </p>
            </div>
            <Button variant="outline" onClick={onBack} className="text-[var(--buddychip-grey-text)]">
              ← Back
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Original Tweet Display */}
          <div className="bg-[var(--buddychip-grey-stroke)]/20 rounded-lg p-4 mb-6">
            <div className="flex items-center mb-2">
              <div className="flex items-center">
                <span className="font-medium text-[var(--buddychip-white)]">
                  {authorDisplayName || 'Unknown User'}
                </span>
                <span className="text-[var(--buddychip-grey-text)] ml-1">
                  @{authorHandle || 'unknown'}
                </span>
                {authorIsVerified && (
                  <Badge className="ml-2 bg-blue-500 text-white text-xs">Verified</Badge>
                )}
              </div>
              <a 
                href={tweetUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="ml-auto text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </div>
            <p className="text-[var(--buddychip-white)] mb-3">{tweetContent}</p>
            {engagement && (
              <div className="flex items-center gap-4 text-sm text-[var(--buddychip-grey-text)]">
                <span>❤️ {engagement.likes.toLocaleString()}</span>
                <span>🔁 {engagement.retweets.toLocaleString()}</span>
                <span>💬 {engagement.replies.toLocaleString()}</span>
              </div>
            )}
          </div>

          {/* Context Improvement Section */}
          {improvedContext && showContextInsights && (
            <div className="mb-6 p-4 bg-[var(--buddychip-grey-stroke)]/20 rounded-lg border border-[var(--buddychip-accent)]/30">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-[var(--buddychip-white)] flex items-center gap-2">
                  <Brain className="h-4 w-4 text-[var(--buddychip-accent)]" />
                  Enhanced Context Insights ({improvedContext.contextEnhancements?.length || 0} found)
                </h3>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowContextInsights(false)}
                  className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                >
                  Hide
                </Button>
              </div>
              
              <div className="space-y-2 mb-4">
                {improvedContext.contextEnhancements?.slice(0, 3).map((enhancement: any, index: number) => (
                  <div key={index} className="text-xs p-2 bg-[var(--buddychip-black)]/50 rounded border border-[var(--buddychip-grey-stroke)]/50">
                    <div className="font-medium text-[var(--buddychip-white)] mb-1">{enhancement.title}</div>
                    <div className="text-[var(--buddychip-grey-text)] mb-1">{enhancement.description}</div>
                    <div className="text-[var(--buddychip-accent)]">💡 {enhancement.actionableAdvice}</div>
                  </div>
                ))}
              </div>
              
              {improvedContext.overallRecommendation && (
                <div className="text-xs p-2 bg-green-900/20 border border-green-600/30 rounded">
                  <div className="font-medium text-green-400 mb-1">🎯 Key Recommendation:</div>
                  <div className="text-green-300">{improvedContext.overallRecommendation}</div>
                </div>
              )}
            </div>
          )}

          {/* Generation Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Mode Selection */}
            <div>
              <Label className="text-[var(--buddychip-white)] text-sm font-medium mb-2 block">
                Mode
              </Label>
              <Select value={mode} onValueChange={(value: 'reply' | 'remake') => setMode(value)}>
                <SelectTrigger className="bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reply">
                    <div className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Reply Mode
                    </div>
                  </SelectItem>
                  <SelectItem value="remake">
                    <div className="flex items-center">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Remake Mode
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Image Generation Toggle */}
            <div>
              <Label className="text-[var(--buddychip-white)] text-sm font-medium mb-2 block">
                Include Images
              </Label>
              <div className="flex items-center space-x-2 h-10">
                <Switch 
                  checked={includeImages} 
                  onCheckedChange={setIncludeImages}
                  className="data-[state=checked]:bg-[var(--buddychip-accent)]"
                />
                <span className="text-sm text-[var(--buddychip-grey-text)]">
                  {includeImages ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            {/* Image Style */}
            {includeImages && (
              <div>
                <Label className="text-[var(--buddychip-white)] text-sm font-medium mb-2 block">
                  Image Style
                </Label>
                <Select value={imageStyle} onValueChange={(value: any) => setImageStyle(value)}>
                  <SelectTrigger className="bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="minimal">Minimal</SelectItem>
                    <SelectItem value="vibrant">Vibrant</SelectItem>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="artistic">Artistic</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Generate Buttons */}
            <div className="space-y-3">
              <div className="flex gap-2">
                <Button 
                  onClick={handleGenerateResponses}
                  disabled={isGenerating || isImprovingContext}
                  className="flex-1 bg-gradient-to-r from-[var(--buddychip-accent)] to-blue-500 text-[var(--buddychip-white)] hover:from-[var(--buddychip-accent)]/80 hover:to-blue-500/80"
                >
                  {isGenerating ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </div>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate
                    </>
                  )}
                </Button>
                <Button 
                  onClick={handleImproveContext}
                  disabled={isGenerating || isImprovingContext}
                  variant="outline"
                  className="border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)] hover:text-white"
                >
                  {isImprovingContext ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {improvedContext && (
                <div className="flex gap-2">
                  <Button 
                    onClick={handleGenerateWithImprovedContext}
                    disabled={isGenerating || isImprovingContext}
                    variant="outline"
                    className="flex-1 border-green-600 text-green-400 hover:bg-green-600 hover:text-white"
                  >
                    {isGenerating ? (
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Enhancing...
                      </div>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        Generate Enhanced
                      </>
                    )}
                  </Button>
                  <Button 
                    onClick={() => setShowContextInsights(!showContextInsights)}
                    disabled={isGenerating || isImprovingContext}
                    variant="ghost"
                    size="sm"
                    className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
                  >
                    <Brain className="h-4 w-4" />
                  </Button>
                </div>
              )}
              
              <div className="text-xs text-[var(--buddychip-grey-text)] text-center">
                {isImprovingContext ? "🤖 Researching with xAI & Perplexity..." : 
                 improvedContext ? "✨ Context enhanced with AI research" : 
                 "Use 'Search' to enhance with real-time research"}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {generatedResponses.length > 0 && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-[var(--buddychip-grey-stroke)]/30">
            <TabsTrigger value="responses" className="data-[state=active]:bg-[var(--buddychip-accent)]">
              Generated Responses ({generatedResponses.length})
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-[var(--buddychip-accent)]">
              Performance Analytics
            </TabsTrigger>
          </TabsList>

          <TabsContent value="responses" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              {generatedResponses.map((response, index) => (
                <Card key={index} className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-[var(--buddychip-accent)]/20 text-[var(--buddychip-accent)]">
                          {response.style}
                        </Badge>
                        <Badge variant="outline" className={`${getConfidenceColor(response.confidence)}`}>
                          {Math.round(response.confidence * 100)}% confidence
                        </Badge>
                        {response.hasImage && (
                          <Badge className="bg-purple-500/20 text-purple-400">
                            <ImageIcon className="h-3 w-3 mr-1" />
                            With Image
                          </Badge>
                        )}
                      </div>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEnhanceIndividualResponse(response._id || `response_${index}`, response.style)}
                          className="h-8 w-8 p-0 text-green-400 hover:text-green-300 hover:bg-green-400/10"
                          title="Enhance with AI Research"
                          disabled={isGenerating || isImprovingContext}
                        >
                          ✨
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleStartEdit(response._id || `response_${index}`, response.content)}
                          className="h-8 w-8 p-0 text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80 hover:bg-[var(--buddychip-accent)]/10"
                          title="Edit Response"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleCopyResponse(response.content)}
                          className="h-8 w-8 p-0 hover:bg-[var(--buddychip-grey-stroke)]/20"
                          title="Copy Response"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        {!response.hasImage && includeImages && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleAddImageToResponse(response.content, index)}
                            className="h-8 w-8 p-0 hover:bg-[var(--buddychip-grey-stroke)]/20"
                            title="Add Image"
                          >
                            <ImageIcon className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Response Text */}
                      <div>
                        {editingResponseId === (response._id || `response_${index}`) ? (
                          <div className="space-y-3 mb-4">
                            <Textarea
                              value={editedContent}
                              onChange={(e) => setEditedContent(e.target.value)}
                              className="min-h-[100px] resize-none bg-[var(--buddychip-black)]/50 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] focus:border-[var(--buddychip-accent)]"
                              placeholder="Edit your response..."
                            />
                            <div className="flex items-center justify-between">
                              <div className="text-xs text-[var(--buddychip-grey-text)]">
                                Length: {editedContent.length} characters
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveEdit(response._id || `response_${index}`)}
                                  className="bg-green-600 hover:bg-green-600/90 text-white"
                                >
                                  <Save className="h-3 w-3 mr-1" />
                                  Save
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={handleCancelEdit}
                                  className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                                >
                                  <X className="h-3 w-3 mr-1" />
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <p className="text-[var(--buddychip-white)] text-lg leading-relaxed mb-4">
                            {response.content}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between text-sm text-[var(--buddychip-grey-text)]">
                          <span>{response.characterCount}/280 characters</span>
                          <span>Model: {response.model}</span>
                        </div>

                        {/* Estimated Engagement */}
                        <div className="flex items-center gap-4 mt-4 text-sm">
                          <div className="flex items-center text-pink-400">
                            <Heart className="h-4 w-4 mr-1" />
                            {response.estimatedEngagement.likes}
                          </div>
                          <div className="flex items-center text-green-400">
                            <RefreshCw className="h-4 w-4 mr-1" />
                            {response.estimatedEngagement.retweets}
                          </div>
                          <div className="flex items-center text-blue-400">
                            <MessageSquare className="h-4 w-4 mr-1" />
                            {response.estimatedEngagement.replies}
                          </div>
                        </div>
                      </div>

                      {/* Generated Image */}
                      {response.hasImage && response.generatedImage && (
                        <div>
                          <div className="aspect-video relative overflow-hidden rounded-lg bg-[var(--buddychip-grey-stroke)]/20">
                            <img 
                              src={response.generatedImage}
                              alt="Generated response image"
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute top-2 right-2 flex gap-2">
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => handleCopyImageUrl(response.generatedImage!)}
                                className="h-8 w-8 p-0 bg-black/80"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => handleDownloadImage(response.generatedImage!, response.imagePrompt || 'Generated image')}
                                className="h-8 w-8 p-0 bg-black/80"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {response.imagePrompt && (
                            <p className="text-xs text-[var(--buddychip-grey-text)] mt-2 line-clamp-2">
                              {response.imagePrompt}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-[var(--buddychip-white)] flex items-center">
                  <BarChart3 className="h-5 w-5 mr-3 text-[var(--buddychip-accent)]" />
                  Performance Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Average Metrics */}
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-[var(--buddychip-grey-text)] mb-2">Average Engagement</h3>
                    <div className="text-2xl font-bold text-[var(--buddychip-white)] mb-1">
                      {Math.round(generatedResponses.reduce((acc, r) => acc + getEngagementTotal(r.estimatedEngagement), 0) / generatedResponses.length)}
                    </div>
                    <p className="text-xs text-[var(--buddychip-grey-text)]">Total interactions</p>
                  </div>

                  {/* Best Performing Style */}
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-[var(--buddychip-grey-text)] mb-2">Best Style</h3>
                    <div className="text-lg font-bold text-[var(--buddychip-accent)] mb-1">
                      {generatedResponses.reduce((best, current) => 
                        getEngagementTotal(current.estimatedEngagement) > getEngagementTotal(best.estimatedEngagement) ? current : best
                      ).style}
                    </div>
                    <p className="text-xs text-[var(--buddychip-grey-text)]">Highest estimated engagement</p>
                  </div>

                  {/* Average Confidence */}
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-[var(--buddychip-grey-text)] mb-2">Avg Confidence</h3>
                    <div className="text-2xl font-bold text-[var(--buddychip-white)] mb-1">
                      {Math.round(generatedResponses.reduce((acc, r) => acc + r.confidence, 0) / generatedResponses.length * 100)}%
                    </div>
                    <p className="text-xs text-[var(--buddychip-grey-text)]">AI confidence score</p>
                  </div>
                </div>

                {/* Response Breakdown */}
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-[var(--buddychip-white)] mb-4">Response Breakdown</h3>
                  <div className="space-y-3">
                    {generatedResponses.map((response, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-[var(--buddychip-grey-stroke)]/20 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Badge variant="secondary" className="bg-[var(--buddychip-accent)]/20 text-[var(--buddychip-accent)]">
                            {response.style}
                          </Badge>
                          <span className="text-sm text-[var(--buddychip-white)]">
                            {response.content.substring(0, 50)}...
                          </span>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-[var(--buddychip-grey-text)]">
                          <span>{Math.round(response.confidence * 100)}%</span>
                          <span>{getEngagementTotal(response.estimatedEngagement)} est. engagement</span>
                          {response.hasImage && <ImageIcon className="h-4 w-4 text-purple-400" />}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}