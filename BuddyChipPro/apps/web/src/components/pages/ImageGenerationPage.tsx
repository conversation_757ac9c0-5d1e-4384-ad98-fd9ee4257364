import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { useAuth } from "@/components/auth/use-auth";
import { ImageGallery } from "@/components/image-generation/image-gallery";
import { ImageGenerator } from "@/components/image-generation/image-generator";
import { PageHeader } from "@/components/ui/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { 
  <PERSON>I<PERSON>, 
  Download, 
  Copy, 
  Trash2, 
  RefreshCw,
  Sparkles,
  Palette,
  Settings,
  Grid3X3,
  Filter,
  Search
} from "lucide-react";
import { toast } from "sonner";

interface GeneratedImage {
  id: string;
  url: string;
  prompt: string;
  style: string;
  model: string;
  dimensions: string;
  createdAt: number;
  isPublic: boolean;
}

function ImageGenerationPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const [prompt, setPrompt] = useState("");
  const [style, setStyle] = useState("realistic");
  const [dimensions, setDimensions] = useState("1024x1024");
  const [model, setModel] = useState("dall-e-3");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [filterStyle, setFilterStyle] = useState("all");
  const [searchFilter, setSearchFilter] = useState("");

  // Convex queries and mutations
  const userImages = useQuery(api.storage.imageStorage.getUserImages) || [];
  const generateImage = useMutation(api.imageGeneration.generateImage);
  const deleteImage = useMutation(api.storage.imageStorage.deleteImage);
  const toggleImagePublic = useMutation(api.storage.imageStorage.toggleImagePublic);

  const handleGenerateImage = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    setIsGenerating(true);
    
    try {
      const result = await generateImage({
        prompt,
        style,
        dimensions,
        model,
        quantity: 1,
      });

      if (result.success) {
        const newImage: GeneratedImage = {
          id: result.imageId,
          url: result.imageUrl,
          prompt,
          style,
          model,
          dimensions,
          createdAt: Date.now(),
          isPublic: false,
        };
        
        setGeneratedImages(prev => [newImage, ...prev]);
        toast.success("Image generated successfully!");
      } else {
        toast.error(result.error || "Failed to generate image");
      }
    } catch (error) {
      console.error("Image generation failed:", error);
      toast.error("Failed to generate image");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDeleteImage = async (imageId: string) => {
    try {
      await deleteImage({ imageId: imageId as any });
      setGeneratedImages(prev => prev.filter(img => img.id !== imageId));
      toast.success("Image deleted");
    } catch (error) {
      toast.error("Failed to delete image");
    }
  };

  const handleTogglePublic = async (imageId: string) => {
    try {
      await toggleImagePublic({ imageId: imageId as any });
      setGeneratedImages(prev => 
        prev.map(img => 
          img.id === imageId 
            ? { ...img, isPublic: !img.isPublic }
            : img
        )
      );
      toast.success("Image visibility updated");
    } catch (error) {
      toast.error("Failed to update visibility");
    }
  };

  const handleDownloadImage = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, '_')}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success("Image downloaded");
    } catch (error) {
      toast.error("Failed to download image");
    }
  };

  const handleCopyPrompt = async (prompt: string) => {
    try {
      await navigator.clipboard.writeText(prompt);
      toast.success("Prompt copied to clipboard");
    } catch (error) {
      toast.error("Failed to copy prompt");
    }
  };

  const filteredImages = generatedImages.filter(image => {
    if (filterStyle !== "all" && image.style !== filterStyle) return false;
    if (searchFilter && !image.prompt.toLowerCase().includes(searchFilter.toLowerCase())) return false;
    return true;
  });

  const toggleImageSelection = (imageId: string) => {
    setSelectedImages(prev => 
      prev.includes(imageId) 
        ? prev.filter(id => id !== imageId)
        : [...prev, imageId]
    );
  };

  const handleBulkDelete = async () => {
    try {
      await Promise.all(selectedImages.map(id => deleteImage({ imageId: id as any })));
      setGeneratedImages(prev => prev.filter(img => !selectedImages.includes(img.id)));
      setSelectedImages([]);
      toast.success(`Deleted ${selectedImages.length} images`);
    } catch (error) {
      toast.error("Failed to delete images");
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] text-[var(--buddychip-white)] flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] text-[var(--buddychip-white)] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Please sign in to generate images</h1>
          <p className="text-[var(--buddychip-grey-text)]">You need to authenticate to use this feature.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--buddychip-light-bg)] via-slate-900 to-[var(--buddychip-black)] text-[var(--buddychip-white)]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <PageHeader
          title="AI Image Generation"
          subtitle="Create stunning visuals with advanced AI models"
        />

        <div className="grid gap-8 lg:grid-cols-12">
          {/* Left Column - Generation Controls */}
          <div className="lg:col-span-4">
            <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[var(--buddychip-white)]">
                  <Palette className="h-5 w-5" />
                  Image Generator
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Prompt Input */}
                <div>
                  <Label htmlFor="prompt" className="text-[var(--buddychip-white)] text-sm font-medium">
                    Prompt
                  </Label>
                  <textarea
                    id="prompt"
                    placeholder="Describe the image you want to generate..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    rows={4}
                    className="w-full mt-2 px-3 py-2 bg-[var(--buddychip-light-bg)] border border-[var(--buddychip-grey-stroke)] rounded-md text-[var(--buddychip-white)] placeholder:text-[var(--buddychip-grey-text)] focus:outline-none focus:border-[var(--buddychip-accent)]"
                  />
                </div>

                {/* Style Selection */}
                <div>
                  <Label className="text-[var(--buddychip-white)] text-sm font-medium">Style</Label>
                  <Select value={style} onValueChange={setStyle}>
                    <SelectTrigger className="mt-2 bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-[var(--buddychip-black)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                      <SelectItem value="realistic" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Realistic</SelectItem>
                      <SelectItem value="artistic" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Artistic</SelectItem>
                      <SelectItem value="cartoon" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Cartoon</SelectItem>
                      <SelectItem value="sketch" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Sketch</SelectItem>
                      <SelectItem value="abstract" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Abstract</SelectItem>
                      <SelectItem value="cyberpunk" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Cyberpunk</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Dimensions */}
                <div>
                  <Label className="text-[var(--buddychip-white)] text-sm font-medium">Dimensions</Label>
                  <Select value={dimensions} onValueChange={setDimensions}>
                    <SelectTrigger className="mt-2 bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-[var(--buddychip-black)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                      <SelectItem value="1024x1024" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Square (1024×1024)</SelectItem>
                      <SelectItem value="1024x1792" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Portrait (1024×1792)</SelectItem>
                      <SelectItem value="1792x1024" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Landscape (1792×1024)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Model Selection */}
                <div>
                  <Label className="text-[var(--buddychip-white)] text-sm font-medium">Model</Label>
                  <Select value={model} onValueChange={setModel}>
                    <SelectTrigger className="mt-2 bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-[var(--buddychip-black)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                      <SelectItem value="dall-e-3" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">DALL-E 3 (Best Quality)</SelectItem>
                      <SelectItem value="dall-e-2" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">DALL-E 2 (Faster)</SelectItem>
                      <SelectItem value="stable-diffusion" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Stable Diffusion</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Generate Button */}
                <Button
                  onClick={handleGenerateImage}
                  disabled={isGenerating || !prompt.trim()}
                  className="w-full bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80 text-[var(--buddychip-white)]"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Image
                    </>
                  )}
                </Button>

                {/* Generation Info */}
                <div className="text-sm text-[var(--buddychip-grey-text)] space-y-1">
                  <p>• Generation typically takes 10-30 seconds</p>
                  <p>• Higher quality models take longer</p>
                  <p>• Images are saved to your gallery</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Image Gallery */}
          <div className="lg:col-span-8">
            <Tabs defaultValue="gallery" className="space-y-6">
              <div className="flex items-center justify-between">
                <TabsList className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)]">
                  <TabsTrigger value="gallery" className="text-[var(--buddychip-white)]">
                    Gallery ({filteredImages.length})
                  </TabsTrigger>
                  <TabsTrigger value="history" className="text-[var(--buddychip-white)]">
                    History
                  </TabsTrigger>
                </TabsList>

                {selectedImages.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-[var(--buddychip-accent)]/20 text-[var(--buddychip-accent)]">
                      {selectedImages.length} selected
                    </Badge>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleBulkDelete}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete Selected
                    </Button>
                  </div>
                )}
              </div>

              <TabsContent value="gallery" className="space-y-4">
                {/* Filters */}
                <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)]">
                  <CardContent className="p-4">
                    <div className="flex gap-4 flex-wrap">
                      <div className="flex-1 min-w-[200px]">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--buddychip-grey-text)]" />
                          <Input
                            placeholder="Search prompts..."
                            value={searchFilter}
                            onChange={(e) => setSearchFilter(e.target.value)}
                            className="pl-10 bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]"
                          />
                        </div>
                      </div>
                      <Select value={filterStyle} onValueChange={setFilterStyle}>
                        <SelectTrigger className="w-[150px] bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-[var(--buddychip-black)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                          <SelectItem value="all" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">All Styles</SelectItem>
                          <SelectItem value="realistic" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Realistic</SelectItem>
                          <SelectItem value="artistic" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Artistic</SelectItem>
                          <SelectItem value="cartoon" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Cartoon</SelectItem>
                          <SelectItem value="sketch" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Sketch</SelectItem>
                          <SelectItem value="abstract" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Abstract</SelectItem>
                          <SelectItem value="cyberpunk" className="text-[var(--buddychip-white)] focus:bg-[var(--buddychip-grey-stroke)] focus:text-[var(--buddychip-white)]">Cyberpunk</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Image Grid */}
                {filteredImages.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredImages.map((image) => (
                      <Card 
                        key={image.id} 
                        className={`bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)] backdrop-blur-sm overflow-hidden hover:border-[var(--buddychip-accent)] transition-colors ${
                          selectedImages.includes(image.id) ? 'ring-2 ring-[var(--buddychip-accent)]' : ''
                        }`}
                      >
                        <div className="relative">
                          <img
                            src={image.url}
                            alt={image.prompt}
                            className="w-full h-48 object-cover cursor-pointer"
                            onClick={() => toggleImageSelection(image.id)}
                          />
                          <div className="absolute top-2 right-2 flex gap-1">
                            <Badge variant="secondary" className="bg-black/50 text-white">
                              {image.style}
                            </Badge>
                            {image.isPublic && (
                              <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                                Public
                              </Badge>
                            )}
                          </div>
                          {selectedImages.includes(image.id) && (
                            <div className="absolute inset-0 bg-[var(--buddychip-accent)]/20 flex items-center justify-center">
                              <div className="w-8 h-8 bg-[var(--buddychip-accent)] rounded-full flex items-center justify-center">
                                <span className="text-white text-sm">✓</span>
                              </div>
                            </div>
                          )}
                        </div>
                        <CardContent className="p-4">
                          <p className="text-sm text-[var(--buddychip-white)] mb-2 line-clamp-2">
                            {image.prompt}
                          </p>
                          <div className="flex items-center justify-between text-xs text-[var(--buddychip-grey-text)] mb-3">
                            <span>{image.model}</span>
                            <span>{image.dimensions}</span>
                            <span>{new Date(image.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownloadImage(image.url, image.prompt)}
                              className="flex-1 text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyPrompt(image.prompt)}
                              className="flex-1 text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleTogglePublic(image.id)}
                              className="flex-1 text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteImage(image.id)}
                              className="flex-1 text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)]">
                    <CardContent className="p-12 text-center">
                      <ImageIcon className="h-12 w-12 text-[var(--buddychip-grey-text)] mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-[var(--buddychip-white)] mb-2">No images yet</h3>
                      <p className="text-[var(--buddychip-grey-text)]">
                        Generate your first image using the controls on the left.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="history">
                <Card className="bg-[var(--buddychip-light-bg)]/60 border-[var(--buddychip-grey-stroke)]">
                  <CardContent className="p-6">
                    <div className="text-center py-8">
                      <Grid3X3 className="h-12 w-12 text-[var(--buddychip-grey-text)] mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-[var(--buddychip-white)] mb-2">Generation History</h3>
                      <p className="text-[var(--buddychip-grey-text)]">
                        View your generation history and statistics here.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ImageGenerationPage;