'use client';
import React from 'react';
import IconButton from './icon-button';
import { IconType } from 'react-icons';
import { MdArrowOutward } from 'react-icons/md';

interface ButtonProps {
  children: React.ReactNode;
  icon?: IconType;
  onClick: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'tertiary';
  iconVariant?: 'primary' | 'secondary' | 'tertiary';
}

const PrimaryButton = ({ icon, children, onClick, className, variant = 'secondary', iconVariant = 'primary' }: ButtonProps) => {
  const variantClasses = {
    primary: 'bg-[#F5F7FA] text-[#000000]',
    secondary: 'bg-[#316FE3] text-[#F5F7FA]',
    tertiary: 'bg-[#000000] text-[#F5F7FA]',
  };

  return (
    <button
    onClick={onClick}
    className={`primary-button group min-w-[280px] w-auto md:w-[370px] h-[48px] sm:h-[52px] md:h-[82px] rounded-[100px] flex items-center justify-between pr-2 pl-6 sm:pl-8 md:pl-10 ${className} ${variantClasses[variant]} text-[14px] sm:text-[16px] md:text-[24px] font-manrope cursor-pointer`}
  >
    <span className="flex-grow text-left">{children}</span>
    {icon && (
      <span className="transition-all duration-300 flex-shrink-0 ml-4">
        <IconButton
          icon={MdArrowOutward}
          className="w-[36px] h-[36px] sm:w-[44px] sm:h-[44px] md:w-[70px] md:h-[70px] overflow-hidden"
          variant={iconVariant}
        />
      </span>
    )}
  </button>
  );
};

export default PrimaryButton;