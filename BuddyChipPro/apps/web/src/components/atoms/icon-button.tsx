'use client'
import React from 'react'
import { IconType } from 'react-icons';

interface IconButtonProps {
  icon?: IconType | null;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'tertiary';
  iconClassName?: string;
}

const IconButton = ({ icon=null, onClick, className, variant = 'primary', iconClassName }: IconButtonProps) => {
    const variantClasses = {
        primary: 'bg-[#F5F7FA] text-[#000000]',
        secondary: 'bg-[#316FE3] text-[#F5F7FA]',
        tertiary: 'bg-[#000000] text-[#F5F7FA]'
    }
  const Icon = icon;


  return (
    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${variantClasses[variant]} ${className} transition-all duration-300 cursor-pointer`} onClick={onClick}>
      {Icon && <Icon className={`w-[28px] h-[28px] ${iconClassName}`} />}
    </div>
  )
}

export default IconButton