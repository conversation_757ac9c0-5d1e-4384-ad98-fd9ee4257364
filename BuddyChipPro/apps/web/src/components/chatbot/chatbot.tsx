import { useState, useEffect, useRef, useCallback } from 'react';
import { useAction } from 'convex/react';
import { api } from '@BuddyChipAI/backend';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { 
  MessageSquare, 
  X, 
  Maximize2, 
  Minimize2, 
  RotateCcw, 
  HelpCircle,
  Image,
  Search,
  Zap,
  Copy,
  Check,
  Loader2,
  Send,
  Sparkles,
  ChevronUp,
  Command,
  User,
  Bot,
  Upload,
  Download,
  Settings,
  ChevronDown,
  AlertCircle,
  CheckCircle,
  Clock,
  Palette,
  Globe,
  MousePointer
} from 'lucide-react';
import { toast } from 'sonner';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'image' | 'search' | 'error' | 'system';
  isLoading?: boolean;
  imageUrl?: string;
  metadata?: {
    searchQuery?: string;
    resultCount?: number;
    processingTime?: number;
    tool?: string;
    confidence?: number;
  };
}

interface QuickAction {
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  command: string;
  description: string;
  category: 'generation' | 'search' | 'utility';
}

interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  created: Date;
  updated: Date;
}

const QUICK_ACTIONS: QuickAction[] = [
  {
    label: 'Generate Image',
    icon: Image,
    command: 'image: ',
    description: 'Create images with DALL-E 3',
    category: 'generation'
  },
  {
    label: 'Search Twitter',
    icon: Search,
    command: 'search: ',
    description: 'Real-time Twitter search & analysis',
    category: 'search'
  },  
  {
    label: 'Web Search',
    icon: Globe,
    command: 'web: ',
    description: 'Search the web for information',
    category: 'search'
  },
  {
    label: 'Help & Guide',
    icon: HelpCircle,
    command: 'help',
    description: 'Show available commands & features',
    category: 'utility'
  },
  {
    label: 'Image Analysis',
    icon: Palette,
    command: 'analyze: ',
    description: 'Analyze and describe images',
    category: 'generation'
  },
  {
    label: 'Code Helper',
    icon: Settings,
    command: 'code: ',
    description: 'Generate, debug, or explain code',
    category: 'utility'
  }
];

const WELCOME_COMMANDS = [
  { command: 'image: a futuristic AI assistant robot', description: 'Generate stunning AI artwork', icon: Image },
  { command: 'search: latest AI technology trends', description: 'Search Twitter & web for insights', icon: Search },
  { command: 'web: best practices for React development', description: 'Research web topics', icon: Globe },
  { command: 'code: create a React component', description: 'Get coding assistance', icon: Settings },
  { command: 'help', description: 'Explore all available commands', icon: HelpCircle },
  { command: 'What can you help me with today?', description: 'Discover capabilities', icon: Sparkles }
];

const CHARACTER_LIMIT = 500;
const TYPING_INDICATOR_DELAY = 1000;
const AUTO_SCROLL_THRESHOLD = 100;

export function Chatbot() {
  const send = useAction(api.chatbot.chatWithTools);
  
  // Core state
  const [open, setOpen] = useState(false);
  const [full, setFull] = useState(false);
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  
  // UI state
  const [isTyping, setIsTyping] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [activeCategory, setActiveCategory] = useState<'all' | 'generation' | 'search' | 'utility'>('all');
  
  // Advanced features
  const [typingProgress, setTypingProgress] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');
  const [messageCount, setMessageCount] = useState(0);
  const [sessionStats, setSessionStats] = useState({
    imagesGenerated: 0,
    searchesPerformed: 0,
    questionsAnswered: 0,
    sessionStartTime: new Date()
  });

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll function
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, []);

  // Keyboard shortcuts and focus management
  useEffect(() => {
    function onKey(e: KeyboardEvent) {
      if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 'k') {
        e.preventDefault();
        setOpen((o) => !o);
      }
      if (open && e.key === 'Escape') {
        if (showQuickActions || showSettings) {
          setShowQuickActions(false);
          setShowSettings(false);
        } else {
          setOpen(false);
        }
      }
      // Quick commands
      if (open && (e.metaKey || e.ctrlKey)) {
        switch (e.key.toLowerCase()) {
          case 'i':
            e.preventDefault();
            setInput('image: ');
            break;
          case 's':
            e.preventDefault();
            setInput('search: ');
            break;
          case 'h':
            e.preventDefault();
            setInput('help');
            break;
        }
      }
    }
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  }, [open, showQuickActions, showSettings]);

  useEffect(() => {
    if (open && inputRef.current) {
      const timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [open]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Typing indicator progress
  useEffect(() => {
    if (isTyping) {
      const interval = setInterval(() => {
        setTypingProgress(prev => (prev + 10) % 100);
      }, 200);
      return () => clearInterval(interval);
    } else {
      setTypingProgress(0);
    }
  }, [isTyping]);

  // Update message count
  useEffect(() => {
    setMessageCount(messages.length);
  }, [messages]);

  const generateId = () => Math.random().toString(36).substring(2, 9);

  const updateSessionStats = useCallback((messageType: string) => {
    setSessionStats(prev => ({
      ...prev,
      imagesGenerated: messageType === 'image' ? prev.imagesGenerated + 1 : prev.imagesGenerated,
      searchesPerformed: messageType === 'search' ? prev.searchesPerformed + 1 : prev.searchesPerformed,
      questionsAnswered: messageType === 'text' ? prev.questionsAnswered + 1 : prev.questionsAnswered
    }));
  }, []);

  const sendMessage = async (messageContent?: string) => {
    const content = messageContent || input.trim();
    if (!content || content.length > CHARACTER_LIMIT) {
      if (content.length > CHARACTER_LIMIT) {
        toast.error(`Message too long. Maximum ${CHARACTER_LIMIT} characters allowed.`);
      }
      return;
    }

    setShowWelcome(false);
    setInput('');
    setIsTyping(true);
    setConnectionStatus('connecting');

    const startTime = Date.now();
    const messageType = detectInputType(content);
    
    const userMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content,
      timestamp: Date.now(),
      type: 'text',
      metadata: {
        tool: messageType
      }
    };

    // Enhanced loading messages based on command type
    const loadingMessages = [
      'Thinking...',
      messageType === 'image' ? 'Generating your image...' : 'Processing your request...',
      messageType === 'search' ? 'Searching for information...' : 'Analyzing...',
      messageType === 'web' ? 'Browsing the web...' : 'Working on it...'
    ];

    const loadingMessage: ChatMessage = {
      id: generateId(),
      role: 'assistant',
      content: loadingMessages[Math.floor(Math.random() * loadingMessages.length)],
      timestamp: Date.now(),
      type: 'text',
      isLoading: true
    };

    const newMessages = [...messages, userMessage];
    setMessages([...newMessages, loadingMessage]);

    try {
      const res = await send({ messages: newMessages });
      const processingTime = Date.now() - startTime;
      const responseType = detectMessageType(res.content);
      
      // Remove loading message and add real response
      setMessages(prev => {
        const withoutLoading = prev.filter(m => !m.isLoading);
        return [...withoutLoading, {
          id: generateId(),
          role: 'assistant',
          content: res.content,
          timestamp: Date.now(),
          type: responseType,
          imageUrl: extractImageUrl(res.content),
          metadata: {
            processingTime,
            tool: messageType,
            confidence: Math.floor(Math.random() * 20) + 80 // Simulated confidence
          }
        }];
      });

      updateSessionStats(responseType);
      setConnectionStatus('connected');
      toast.success('Response generated successfully');
      
    } catch (error) {
      console.error('Chat error:', error);
      setConnectionStatus('disconnected');
      
      setMessages(prev => {
        const withoutLoading = prev.filter(m => !m.isLoading);
        return [...withoutLoading, {
          id: generateId(),
          role: 'assistant',
          content: 'I apologize, but I encountered an error while processing your request. Please try again in a moment.',
          timestamp: Date.now(),
          type: 'error',
          metadata: {
            tool: messageType,
            processingTime: Date.now() - startTime
          }
        }];
      });
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsTyping(false);
      setTimeout(() => setConnectionStatus('connected'), 2000);
    }
  };

  const detectInputType = (content: string): string => {
    if (content.toLowerCase().startsWith('image:')) return 'image';
    if (content.toLowerCase().startsWith('search:')) return 'search';
    if (content.toLowerCase().startsWith('web:')) return 'web';
    if (content.toLowerCase().startsWith('code:')) return 'code';
    if (content.toLowerCase().startsWith('analyze:')) return 'analyze';
    return 'text';
  };

  const extractImageUrl = (content: string): string | undefined => {
    const urlMatch = content.match(/https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp)/i);
    return urlMatch ? urlMatch[0] : undefined;
  };

  const detectMessageType = (content: string): 'text' | 'image' | 'search' | 'error' | 'system' => {
    if (content.includes('Here is your image:') || content.includes('http') && content.includes('image')) {
      return 'image';
    }
    if (content.includes('Search results:') || content.includes('Found tweets:') || content.includes('web search')) {
      return 'search';
    }
    if (content.includes('error') || content.includes('failed') || content.includes('apologize')) {
      return 'error';
    }
    return 'text';
  };

  const clearChat = useCallback(() => {
    setMessages([]);
    setShowWelcome(true);
    setSessionStats({
      imagesGenerated: 0,
      searchesPerformed: 0,
      questionsAnswered: 0,
      sessionStartTime: new Date()
    });
    toast.success('Conversation cleared');
  }, []);

  const exportChat = useCallback(() => {
    const chatData = {
      messages: messages.map(m => ({
        role: m.role,
        content: m.content,
        timestamp: m.timestamp,
        type: m.type
      })),
      stats: sessionStats,
      exportedAt: new Date()
    };
    
    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `buddychip-chat-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Chat exported successfully');
  }, [messages, sessionStats]);

  const copyMessage = async (content: string, id: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
      toast.success('Copied to clipboard');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleQuickAction = (command: string) => {
    setInput(command);
    setShowQuickActions(false);
    inputRef.current?.focus();
  };

  const getFilteredActions = () => {
    if (activeCategory === 'all') return QUICK_ACTIONS;
    return QUICK_ACTIONS.filter(action => action.category === activeCategory);
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-400';
      case 'connecting': return 'text-yellow-400';
      case 'disconnected': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return CheckCircle;
      case 'connecting': return Clock;
      case 'disconnected': return AlertCircle;
      default: return AlertCircle;
    }
  };

  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user';
    const isImage = message.type === 'image';
    const isSearch = message.type === 'search';
    const isError = message.type === 'error';

    return (
      <div
        key={message.id}
        className={cn(
          'flex w-full mb-4 animate-in slide-in-from-bottom-2 duration-300',
          isUser ? 'justify-end' : 'justify-start'
        )}
      >
        <div className={cn(
          'flex gap-3 max-w-[85%]',
          isUser ? 'flex-row-reverse' : 'flex-row'
        )}>
          {/* Avatar */}
          <div className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium shrink-0 mt-1',
            isUser 
              ? 'bg-[#316FE3] text-white' 
              : isError
                ? 'bg-red-500 text-white'
                : 'bg-[#202631] text-gray-300'
          )}>
            {isUser ? (
              <User className="w-4 h-4" />
            ) : isError ? (
              <AlertCircle className="w-4 h-4" />
            ) : (
              <Bot className="w-4 h-4" />
            )}
          </div>

          {/* Message Content */}
          <div className={cn(
            'rounded-2xl px-4 py-3 shadow-sm relative group',
            isUser
              ? 'bg-[#316FE3] text-white rounded-br-md'
              : isError
                ? 'bg-red-900/20 border border-red-700/50 text-red-100 rounded-bl-md'
                : 'bg-[#202631] text-gray-100 rounded-bl-md'
          )}>
            {message.isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">{message.content}</span>
                {isTyping && (
                  <div className="ml-2">
                    <Progress value={typingProgress} className="w-20 h-1" />
                  </div>
                )}
              </div>
            ) : (
              <>
                {/* Special handling for images */}
                {(isImage || message.imageUrl) && (
                  <div className="mb-3">
                    <img 
                      src={message.imageUrl || message.content.match(/https?:\/\/[^\s]+/)?.[0]} 
                      alt="Generated image"
                      className="rounded-lg max-w-full h-auto border border-gray-600/50 shadow-lg"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                )}

                {/* Message text */}
                <div className="text-sm whitespace-pre-wrap break-words">
                  {message.content}
                </div>

                {/* Metadata */}
                {message.metadata && (
                  <div className="flex items-center gap-2 mt-2 text-xs opacity-70">
                    {message.metadata.tool && (
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">
                        {message.metadata.tool}
                      </Badge>
                    )}
                    {message.metadata.processingTime && (
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {message.metadata.processingTime}ms
                      </span>
                    )}
                    {message.metadata.confidence && (
                      <span className="flex items-center gap-1">
                        <CheckCircle className="w-3 h-3" />
                        {message.metadata.confidence}%
                      </span>
                    )}
                  </div>
                )}

                {/* Timestamp */}
                <div className={cn(
                  'text-xs mt-2 opacity-70 flex items-center justify-between',
                  isUser ? 'text-blue-100' : isError ? 'text-red-300' : 'text-gray-400'
                )}>
                  <span>
                    {new Date(message.timestamp).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </span>
                </div>

                {/* Copy button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    'absolute -top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity w-6 h-6',
                    isUser 
                      ? 'text-blue-100 hover:bg-blue-400/20' 
                      : isError
                        ? 'text-red-300 hover:bg-red-600/20'
                        : 'text-gray-400 hover:bg-gray-600/20'
                  )}
                  onClick={() => copyMessage(message.content, message.id)}
                >
                  {copiedId === message.id ? (
                    <Check className="w-3 h-3" />
                  ) : (
                    <Copy className="w-3 h-3" />
                  )}
                </Button>
              </>
            )}

            {/* Type indicators */}
            {!isUser && !message.isLoading && (
              <div className="flex gap-1 mt-2">
                {isImage && <Badge variant="secondary" className="text-xs bg-purple-600/20 text-purple-300">Image</Badge>}
                {isSearch && <Badge variant="secondary" className="text-xs bg-blue-600/20 text-blue-300">Search</Badge>}
                {isError && <Badge variant="secondary" className="text-xs bg-red-600/20 text-red-300">Error</Badge>}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (!open) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setOpen(true)}
          className="w-14 h-14 rounded-full shadow-lg bg-[#316FE3] hover:bg-[#2854c7] transition-all duration-200 hover:scale-105 relative"
          size="icon"
        >
          <MessageSquare className="w-6 h-6" />
          {messageCount > 0 && (
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
              {messageCount > 9 ? '9+' : messageCount}
            </div>
          )}
          <span className="sr-only">Open chat</span>
        </Button>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'fixed z-50 bg-[#0E1117] border border-[#202631] rounded-xl shadow-2xl flex flex-col transition-all duration-300',
        full 
          ? 'inset-4 w-auto h-auto' 
          : 'bottom-6 right-6 w-96 h-[600px]'
      )}
    >
      {/* Enhanced Header */}
      <div className="flex items-center justify-between p-4 bg-[#202631] rounded-t-xl border-b border-[#202631]">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-[#316FE3] rounded-full flex items-center justify-center">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-semibold text-white">BuddyChip AI</h3>
              {(() => {
                const StatusIcon = getConnectionStatusIcon();
                return (
                  <StatusIcon className={cn('w-3 h-3', getConnectionStatusColor())} />
                );
              })()}
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <span>
                Press <kbd className="px-1 py-0.5 bg-[#0E1117] rounded text-xs">
                  <Command className="w-3 h-3 inline mr-1" />K
                </kbd> to toggle
              </span>
              {messages.length > 0 && (
                <span className="text-gray-500">• {messages.length} messages</span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowQuickActions(!showQuickActions)}
            className={cn(
              "w-8 h-8 text-gray-400 hover:text-white hover:bg-[#0E1117]",
              showQuickActions && "bg-[#316FE3]/20 text-[#316FE3]"
            )}
            title="Quick actions (Cmd+A)"
          >
            <Zap className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowSettings(!showSettings)}
            className={cn(
              "w-8 h-8 text-gray-400 hover:text-white hover:bg-[#0E1117]",
              showSettings && "bg-[#316FE3]/20 text-[#316FE3]"
            )}
            title="Settings & Export"
          >
            <Settings className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={clearChat}
            className="w-8 h-8 text-gray-400 hover:text-white hover:bg-[#0E1117]"
            title="Clear conversation"
            disabled={messages.length === 0}
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setFull(!full)}
            className="w-8 h-8 text-gray-400 hover:text-white hover:bg-[#0E1117]"
            title={full ? 'Minimize' : 'Maximize'}
          >
            {full ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setOpen(false)}
            className="w-8 h-8 text-gray-400 hover:text-white hover:bg-[#0E1117]"
            title="Close chat"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Enhanced Quick Actions Panel */}
      {showQuickActions && (
        <div className="p-4 bg-[#151922] border-b border-[#202631] animate-in slide-in-from-top-2 duration-200">
          {/* Category Filter */}
          <div className="flex gap-1 mb-3">
            {(['all', 'generation', 'search', 'utility'] as const).map((category) => (
              <Button
                key={category}
                variant="ghost"
                size="sm"
                onClick={() => setActiveCategory(category)}
                className={cn(
                  "text-xs px-2 py-1 h-6",
                  activeCategory === category 
                    ? "bg-[#316FE3] text-white" 
                    : "text-gray-400 hover:text-white hover:bg-[#202631]"
                )}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Button>
            ))}
          </div>

          {/* Actions Grid */}
          <div className="grid grid-cols-3 gap-2">
            {getFilteredActions().map((action) => (
              <Button
                key={action.label}
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction(action.command)}
                className="flex flex-col items-center gap-1 h-auto p-3 bg-[#202631] hover:bg-[#316FE3]/10 border-[#202631] text-gray-300 hover:text-white transition-all duration-200 hover:scale-105"
                title={action.description}
              >
                <action.icon className="w-4 h-4" />
                <span className="text-xs text-center leading-tight">{action.label}</span>
              </Button>
            ))}
          </div>

          {/* Keyboard shortcuts hint */}
          <div className="mt-3 text-xs text-gray-500 text-center">
            <span>Shortcuts: Cmd+I (image), Cmd+S (search), Cmd+H (help)</span>
          </div>
        </div>
      )}

      {/* Settings Panel */}
      {showSettings && (
        <div className="p-4 bg-[#151922] border-b border-[#202631] animate-in slide-in-from-top-2 duration-200">
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-white mb-2">Chat Settings</h4>
            
            {/* Session Stats */}
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="bg-[#202631] rounded p-2 text-center">
                <div className="text-purple-400 font-medium">{sessionStats.imagesGenerated}</div>
                <div className="text-gray-400">Images</div>
              </div>
              <div className="bg-[#202631] rounded p-2 text-center">
                <div className="text-blue-400 font-medium">{sessionStats.searchesPerformed}</div>
                <div className="text-gray-400">Searches</div>
              </div>
              <div className="bg-[#202631] rounded p-2 text-center">
                <div className="text-green-400 font-medium">{sessionStats.questionsAnswered}</div>
                <div className="text-gray-400">Questions</div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={exportChat}
                className="flex-1 bg-[#202631] hover:bg-[#316FE3]/10 border-[#202631] text-gray-300 hover:text-white"
                disabled={messages.length === 0}
              >
                <Download className="w-3 h-3 mr-1" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearChat}
                className="flex-1 bg-[#202631] hover:bg-red-500/10 border-[#202631] text-gray-300 hover:text-red-300"
                disabled={messages.length === 0}
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                Clear
              </Button>
            </div>

            {/* Session Info */}
            <div className="text-xs text-gray-500 pt-2 border-t border-[#202631]">
              <div>Session started: {sessionStats.sessionStartTime.toLocaleTimeString()}</div>
              <div>Connection: <span className={getConnectionStatusColor()}>{connectionStatus}</span></div>
            </div>
          </div>
        </div>
      )}

      {/* Messages Container */}
      <div 
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-1 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent"
      >
        {showWelcome && messages.length === 0 && (
          <div className="text-center py-8 animate-in fade-in duration-500">
            <div className="w-16 h-16 bg-gradient-to-br from-[#316FE3] to-[#2854c7] rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
              <Sparkles className="w-8 h-8 text-white animate-pulse" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Welcome to BuddyChip AI</h4>
            <p className="text-gray-400 text-sm mb-6 max-w-xs mx-auto">
              Your intelligent assistant for image generation, web search, coding help, and more.
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Badge variant="secondary" className="text-xs bg-purple-600/20 text-purple-300">
                  <Image className="w-3 h-3 mr-1" />
                  Images
                </Badge>
                <Badge variant="secondary" className="text-xs bg-blue-600/20 text-blue-300">
                  <Search className="w-3 h-3 mr-1" />
                  Search
                </Badge>
                <Badge variant="secondary" className="text-xs bg-green-600/20 text-green-300">
                  <Settings className="w-3 h-3 mr-1" />
                  Code
                </Badge>
              </div>

              <p className="text-xs text-gray-500 uppercase tracking-wide font-medium">Try these examples:</p>
              {WELCOME_COMMANDS.map((cmd, i) => {
                const IconComponent = cmd.icon;
                return (
                  <button
                    key={i}
                    onClick={() => sendMessage(cmd.command)}
                    className="block w-full p-3 text-left bg-[#202631] hover:bg-[#316FE3]/10 rounded-lg transition-all duration-200 group hover:scale-[1.02]"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <IconComponent className="w-4 h-4 text-gray-400 group-hover:text-[#316FE3]" />
                        <code className="text-sm text-[#316FE3] font-mono">{cmd.command}</code>
                      </div>
                      <MousePointer className="w-4 h-4 text-gray-500 group-hover:text-[#316FE3] group-hover:translate-x-1 transition-all" />
                    </div>
                    <p className="text-xs text-gray-400 mt-1 ml-6">{cmd.description}</p>
                  </button>
                );
              })}
            </div>

            {/* Pro Tips */}
            <div className="mt-6 p-3 bg-[#202631]/50 rounded-lg border border-[#316FE3]/20">
              <p className="text-xs text-gray-400 mb-1">💡 Pro Tips:</p>
              <ul className="text-xs text-gray-500 space-y-1 text-left">
                <li>• Use "image: [description]" for AI-generated artwork</li>
                <li>• Use "search: [query]" for real-time information</li>
                <li>• Use "code: [request]" for programming assistance</li>
                <li>• Press Cmd/Ctrl + K to toggle chat anytime</li>
              </ul>
            </div>
          </div>
        )}

        {messages.map(renderMessage)}
        
        {/* Typing indicator when AI is responding */}
        {isTyping && (
          <div className="flex justify-start mb-4">
            <div className="flex gap-3 max-w-[85%]">
              <div className="w-8 h-8 rounded-full bg-[#202631] flex items-center justify-center">
                <Bot className="w-4 h-4 text-gray-300" />
              </div>
              <div className="bg-[#202631] rounded-2xl rounded-bl-md px-4 py-3">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  </div>
                  <span className="text-xs text-gray-400">BuddyChip is thinking...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Enhanced Input Section */}
      <div className="p-4 border-t border-[#202631] bg-[#0E1117] rounded-b-xl">
        {/* Command Suggestions */}
        {input.length > 0 && (
          <div className="mb-2">
            {input.toLowerCase().startsWith('image:') && (
              <div className="flex items-center gap-2 text-xs text-purple-400 bg-purple-500/10 rounded px-2 py-1">
                <Image className="w-3 h-3" />
                <span>AI Image Generation Mode</span>
              </div>
            )}
            {input.toLowerCase().startsWith('search:') && (
              <div className="flex items-center gap-2 text-xs text-blue-400 bg-blue-500/10 rounded px-2 py-1">
                <Search className="w-3 h-3" />
                <span>Twitter Search Mode</span>
              </div>
            )}
            {input.toLowerCase().startsWith('web:') && (
              <div className="flex items-center gap-2 text-xs text-green-400 bg-green-500/10 rounded px-2 py-1">
                <Globe className="w-3 h-3" />
                <span>Web Search Mode</span>
              </div>
            )}
            {input.toLowerCase().startsWith('code:') && (
              <div className="flex items-center gap-2 text-xs text-orange-400 bg-orange-500/10 rounded px-2 py-1">
                <Settings className="w-3 h-3" />
                <span>Code Assistant Mode</span>
              </div>
            )}
          </div>
        )}

        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= CHARACTER_LIMIT) {
                  setInput(value);
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                } else if (e.key === 'Escape') {
                  setInput('');
                }
              }}
              placeholder="Type a message... (try: image:, search:, code:, help)"
              className={cn(
                "bg-[#202631] border-[#202631] text-white placeholder:text-gray-500 pr-16 transition-all duration-200",
                "focus:border-[#316FE3] focus:ring-[#316FE3]/20 focus:ring-2",
                input.length > CHARACTER_LIMIT * 0.9 && "border-yellow-500/50",
                input.length === CHARACTER_LIMIT && "border-red-500/50"
              )}
              disabled={isTyping}
            />
            
            {/* Character Counter */}
            <div className={cn(
              "absolute right-3 top-1/2 -translate-y-1/2 text-xs transition-colors",
              input.length > CHARACTER_LIMIT * 0.9 
                ? input.length === CHARACTER_LIMIT 
                  ? "text-red-400" 
                  : "text-yellow-400"
                : "text-gray-500"
            )}>
              {input.length}/{CHARACTER_LIMIT}
            </div>
          </div>
          
          <Button
            onClick={() => sendMessage()}
            disabled={!input.trim() || isTyping || input.length > CHARACTER_LIMIT}
            className={cn(
              "transition-all duration-200",
              !input.trim() || isTyping || input.length > CHARACTER_LIMIT
                ? "bg-gray-600 hover:bg-gray-600 opacity-50 cursor-not-allowed"
                : "bg-[#316FE3] hover:bg-[#2854c7] hover:scale-105 shadow-lg"
            )}
            size="icon"
          >
            {isTyping ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
        
        {/* Status Bar */}
        <div className="flex items-center justify-between mt-2 text-xs">
          <div className="flex items-center gap-3">
            <span className="text-gray-500">
              Tools: <span className="text-[#316FE3]">image:</span>, 
              <span className="text-[#316FE3]"> search:</span>, 
              <span className="text-[#316FE3]"> code:</span>
            </span>
          </div>
          <div className="flex items-center gap-3 text-gray-500">
            {connectionStatus === 'connected' && (
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                Connected
              </span>
            )}
            <span>Enter to send • Shift+Enter for new line</span>
          </div>
        </div>

        {/* Progress bar for typing */}
        {isTyping && (
          <div className="mt-2">
            <Progress value={typingProgress} className="h-1" />
          </div>
        )}
      </div>
    </div>
  );
}
