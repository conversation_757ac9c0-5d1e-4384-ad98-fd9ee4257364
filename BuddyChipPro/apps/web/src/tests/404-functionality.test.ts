/**
 * Tests for 404 Error Page Functionality
 * Tests redirect logic, analytics tracking, and user interactions
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UrlRedirectManager } from '../lib/url-redirects';

// Mock window and localStorage
const mockWindow = {
  location: {
    href: 'http://localhost:3001/test-url',
    pathname: '/test-url',
    search: '?param=value',
  },
  history: {
    length: 2,
    back: vi.fn(),
  },
  performance: {
    getEntriesByType: vi.fn(() => [{ type: 'navigate' }]),
  },
};

const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};

// @ts-ignore
global.window = mockWindow;
// @ts-ignore
global.localStorage = mockLocalStorage;

describe('URL Redirect Manager', () => {
  let redirectManager: UrlRedirectManager;

  beforeEach(() => {
    redirectManager = new UrlRedirectManager();
    vi.clearAllMocks();
  });

  describe('Common Typo Redirects', () => {
    it('should redirect dashboard typos', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dashbaord');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/dashboard');
      expect(suggestions[0].confidence).toBe(0.95);
      expect(suggestions[0].autoRedirect).toBe(true);
    });

    it('should suggest dashboard for dash abbreviation', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dash');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/dashboard');
      expect(suggestions[0].confidence).toBe(0.8);
      expect(suggestions[0].autoRedirect).toBe(false);
    });

    it('should redirect signin to sign-in', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/signin');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/sign-in');
      expect(suggestions[0].autoRedirect).toBe(true);
    });

    it('should redirect signup to sign-up', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/signup');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/sign-up');
      expect(suggestions[0].autoRedirect).toBe(true);
    });
  });

  describe('Feature Redirects', () => {
    it('should suggest tweet-assistant for tweets', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/tweets');
      expect(suggestions.some(s => s.url === '/tweet-assistant')).toBe(true);
    });

    it('should suggest image-generation for images', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/images');
      expect(suggestions.some(s => s.url === '/image-generation')).toBe(true);
    });

    it('should suggest live-search for search', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/search');
      expect(suggestions.some(s => s.url === '/live-search')).toBe(true);
    });
  });

  describe('Legacy URL Patterns', () => {
    it('should handle app prefix removal', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/app/dashboard');
      expect(suggestions.some(s => s.url === '/dashboard')).toBe(true);
    });

    it('should redirect index to root', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/index');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/');
      expect(suggestions[0].autoRedirect).toBe(true);
    });

    it('should remove trailing slashes', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dashboard/');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/dashboard');
      expect(suggestions[0].autoRedirect).toBe(true);
    });
  });

  describe('Auto Redirect Logic', () => {
    it('should return auto redirect for high confidence matches', () => {
      const autoRedirect = redirectManager.getAutoRedirect('/dashbaord');
      expect(autoRedirect).toBe('/dashboard');
    });

    it('should not auto redirect for low confidence matches', () => {
      const autoRedirect = redirectManager.getAutoRedirect('/dash');
      expect(autoRedirect).toBeNull();
    });

    it('should not auto redirect fuzzy matches', () => {
      const autoRedirect = redirectManager.getAutoRedirect('/dashbord'); // Close but not exact
      expect(autoRedirect).toBeNull();
    });
  });

  describe('Fuzzy Matching', () => {
    it('should find similar routes', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dashbord');
      const dashboardSuggestion = suggestions.find(s => s.url === '/dashboard');
      expect(dashboardSuggestion).toBeDefined();
      expect(dashboardSuggestion?.confidence).toBeGreaterThan(0.6);
      expect(dashboardSuggestion?.autoRedirect).toBe(false);
    });

    it('should not suggest exact matches', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dashboard');
      // Should not suggest /dashboard for /dashboard
      expect(suggestions.filter(s => s.url === '/dashboard')).toHaveLength(0);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty URLs', () => {
      const suggestions = redirectManager.findRedirectSuggestions('');
      expect(suggestions).toBeInstanceOf(Array);
    });

    it('should handle URLs with query parameters', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dashbaord?param=value');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/dashboard');
    });

    it('should handle URLs with hash fragments', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dashbaord#section');
      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].url).toBe('/dashboard');
    });

    it('should handle case insensitive matching', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/DASHBOARD');
      // Should find fuzzy match for dashboard
      const dashboardSuggestion = suggestions.find(s => s.url === '/dashboard');
      expect(dashboardSuggestion).toBeDefined();
    });
  });

  describe('Suggestion Ranking', () => {
    it('should sort suggestions by confidence', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/dash');
      // Should be sorted by confidence (highest first)
      for (let i = 1; i < suggestions.length; i++) {
        expect(suggestions[i - 1].confidence).toBeGreaterThanOrEqual(suggestions[i].confidence);
      }
    });

    it('should limit suggestions to top 3', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/unknown-long-url-that-might-match-multiple-things');
      expect(suggestions.length).toBeLessThanOrEqual(3);
    });

    it('should remove duplicate suggestions', () => {
      const suggestions = redirectManager.findRedirectSuggestions('/signin');
      const urls = suggestions.map(s => s.url);
      const uniqueUrls = [...new Set(urls)];
      expect(urls.length).toBe(uniqueUrls.length);
    });
  });
});

describe('404 Analytics Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('[]');
  });

  it('should store navigation errors in localStorage', () => {
    const errorData = {
      type: 'navigation_error',
      message: 'Test error',
      url: '/test-url',
      timestamp: Date.now(),
    };

    // Simulate error storage
    const existingErrors = JSON.parse(mockLocalStorage.getItem('navigation_errors') || '[]');
    existingErrors.push(errorData);
    mockLocalStorage.setItem('navigation_errors', JSON.stringify(existingErrors.slice(-10)));

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'navigation_errors',
      expect.stringContaining('navigation_error')
    );
  });

  it('should limit stored errors to last 10', () => {
    const errors = Array.from({ length: 15 }, (_, i) => ({
      type: 'navigation_error',
      message: `Error ${i}`,
      timestamp: Date.now() + i,
    }));

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(errors));
    
    // Simulate adding a new error
    const existingErrors = JSON.parse(mockLocalStorage.getItem('navigation_errors') || '[]');
    existingErrors.push({ type: 'new_error', timestamp: Date.now() });
    const limitedErrors = existingErrors.slice(-10);
    
    expect(limitedErrors.length).toBe(10);
    expect(limitedErrors[0].message).toBe('Error 6'); // Should start from 6th error
  });
});

console.log('✅ 404 functionality tests defined');
console.log('📊 Test coverage includes:');
console.log('  - URL redirect logic');
console.log('  - Typo correction');
console.log('  - Fuzzy matching');
console.log('  - Auto-redirect behavior');
console.log('  - Analytics integration');
console.log('  - Edge cases and error handling');
