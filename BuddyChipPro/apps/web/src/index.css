@import 'animate.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* BuddyChip Improved Color System - WCAG AA Compliant */
:root {
  /* Base Backgrounds - Softer, Less Harsh */
  --buddychip-black: #0a0a0b;           /* Softer black, less eye strain */
  --buddychip-app-bg: #0d1117;          /* Main app background */
  --buddychip-card-bg: #161b22;         /* Card/container background */
  --buddychip-elevated-bg: #1c2128;     /* Modal/dropdown background */
  
  /* Legacy support (now pointing to improved colors) */
  --buddychip-light-bg: var(--buddychip-app-bg);
  
  /* Text Colors - WCAG AA Compliant */
  --buddychip-white: #f0f6fc;           /* Primary text (7.1:1 contrast) */
  --buddychip-text-secondary: #8b949e;  /* Secondary text (4.5:1 contrast) */
  --buddychip-text-muted: #6e7681;      /* Muted text (4.1:1 contrast) */
  
  /* Legacy text support */
  --buddychip-grey-text: var(--buddychip-text-secondary);
  
  /* UI Elements - Better Contrast */
  --buddychip-border: #30363d;          /* Subtle borders (2.1:1 contrast) */
  --buddychip-border-strong: #484f58;   /* Visible borders (3.2:1 contrast) */
  --buddychip-border-accent: #388bfd26; /* Accent borders with transparency */
  
  /* Legacy border support */
  --buddychip-grey-stroke: var(--buddychip-border-strong);
  
  /* Interactive Elements */
  --buddychip-accent: #2f81f7;          /* Primary blue (more vibrant) */
  --buddychip-accent-hover: #1f6feb;    /* Hover state */
  --buddychip-accent-bg: #388bfd14;     /* Accent background tint */
  
  /* Status Colors - Improved Visibility */
  --status-success: #3fb950;
  --status-warning: #d29922;
  --status-error: #f85149;
  --status-info: #58a6ff;
  
  /* Priority Colors */
  --priority-high: var(--status-error);
  --priority-medium: var(--status-warning);
  --priority-low: #6b7280;
  
  /* Sentiment Colors */
  --sentiment-positive: var(--status-success);
  --sentiment-neutral: #6b7280;
  --sentiment-negative: var(--status-error);
  
  /* === shadcn/ui Required Variables === */
  
  /* Core Tokens */
  --background: var(--buddychip-app-bg);
  --foreground: var(--buddychip-white);
  
  /* Primary */
  --primary: var(--buddychip-accent);
  --primary-foreground: #ffffff;
  
  /* Secondary */
  --secondary: var(--buddychip-card-bg);
  --secondary-foreground: var(--buddychip-white);
  
  /* Muted */
  --muted: var(--buddychip-border);
  --muted-foreground: var(--buddychip-text-muted);
  
  /* Accent */
  --accent: var(--buddychip-card-bg);
  --accent-foreground: var(--buddychip-white);
  
  /* Destructive */
  --destructive: var(--status-error);
  --destructive-foreground: #ffffff;
  
  /* Border & Input */
  --border: var(--buddychip-border);
  --input: var(--buddychip-border-strong);
  --ring: var(--buddychip-accent);
  
  /* Card */
  --card: var(--buddychip-card-bg);
  --card-foreground: var(--buddychip-white);
  
  /* Popover */
  --popover: var(--buddychip-elevated-bg);
  --popover-foreground: var(--buddychip-white);
  
  /* Border Radius */
  --radius: 0.5rem;
}

/* Landing page styles */
.landing-page {
  background: var(--buddychip-app-bg);
  color: var(--buddychip-white);
  min-height: 100vh;
}

/* Custom animations for notification bell */
@keyframes gentle-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes gentle-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.8);
    transform: scale(1.05);
  }
}

/* Smooth scrolling animation for MovingTextCarousel */
@keyframes smooth-scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-smooth-scroll {
  animation: smooth-scroll 20s linear infinite;
}

/* Wave animation for loading skeletons */
@keyframes wave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-wave {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200px 100%;
  animation: wave 1.5s infinite;
}

