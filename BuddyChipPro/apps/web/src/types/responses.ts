/**
 * Response Types and Interfaces
 * 
 * TypeScript types for AI response generation system.
 */

// Response styles enum
export type ResponseStyle = "professional" | "casual" | "humorous" | "technical" | "supportive" | "engaging";

// Response type enum
export type ResponseType = "reply" | "quote" | "thread" | "dm";

// Response status enum
export type ResponseStatus = "draft" | "pending_review" | "approved" | "declined" | "posted" | "failed";

// Risk level enum
export type RiskLevel = "low" | "medium" | "high";

// Risk tolerance enum
export type RiskTolerance = "conservative" | "balanced" | "aggressive";

// Quality metrics interface
export interface QualityMetrics {
  relevance: number;      // 0-1: How relevant is the response to the original content
  clarity: number;        // 0-1: How clear and understandable is the response
  engagement: number;     // 0-1: How likely is it to drive engagement
  brandSafety: number;    // 0-1: How safe is it for brand reputation
  styleAdherence: number; // 0-1: How well does it match the requested style
  length: number;         // 0-1: Appropriate length for the platform
  overall: number;        // 0-1: Overall quality score
}

// Risk assessment interface
export interface RiskAssessment {
  overallRisk: RiskLevel;
  politicalRisk: boolean;
  brandRisk: boolean;
  factualRisk: boolean;
  toneRisk: boolean;
  riskReasons: string[];
}

// Generation context interface
export interface GenerationContext {
  tone: string;
  goals: string[];
  constraints: string[];
  personaType: ResponseStyle;
}

// Engagement metrics interface
export interface EngagementMetrics {
  likes: number;
  retweets: number;
  replies: number;
  quotes: number;
  views?: number;
  lastUpdated: number;
}

// Response interface (mirrors Convex schema)
export interface Response {
  _id: string;
  userId: string;
  targetType: "tweet" | "mention";
  targetId: string;
  originalContent: string;
  originalAuthor: string;
  originalUrl: string;
  responseText: string;
  responseType: ResponseType;
  generationModel: string;
  generationPrompt: string;
  generationContext: GenerationContext;
  qualityScore?: number;
  qualityMetrics?: QualityMetrics;
  riskAssessment: RiskAssessment;
  status: ResponseStatus;
  reviewedBy?: string;
  reviewedAt?: number;
  reviewNotes?: string;
  postedAt?: number;
  postedTweetId?: string;
  postedUrl?: string;
  engagement?: EngagementMetrics;
  createdAt: number;
  updatedAt: number;
}

// Style configuration interface
export interface StyleConfig {
  name: ResponseStyle;
  description: string;
  tone: string;
  characteristics: string[];
  constraints: string[];
  examples: string[];
}

// Response generation request interface
export interface ResponseGenerationRequest {
  userId: string;
  targetType: "tweet" | "mention";
  targetId: string;
  styles: ResponseStyle[];
  responseType?: ResponseType;
}

// Response generation result interface
export interface ResponseGenerationResult {
  generatedResponses: string[];
  totalGenerated: number;
  requestedStyles: ResponseStyle[];
}

// Style performance analytics interface
export interface StylePerformanceAnalytics {
  style: ResponseStyle;
  totalResponses: number;
  averageQuality: number;
  averageEngagement: number;
  totalEngagement: number;
  approvalRate: number;
  topResponse?: Response;
}

// Response statistics interface
export interface ResponseStatistics {
  total: number;
  byStyle: Record<ResponseStyle, number>;
  byStatus: Record<ResponseStatus, number>;
  qualityMetrics: {
    averageQuality: number;
    highQuality: number;
    mediumQuality: number;
    lowQuality: number;
  };
  riskDistribution: Record<RiskLevel, number>;
}

// User response preferences interface
export interface ResponsePreferences {
  defaultStyles: ResponseStyle[];
  autoGenerateStyles: boolean;
  qualityThreshold: number;
  riskTolerance: RiskTolerance;
  autoApproveThreshold: number;
}

// AI settings interface (from user settings)
export interface AIConfig {
  defaultModel: string;
  responsePersona: ResponseStyle;
  riskTolerance: RiskTolerance;
  autoApproveThreshold: number;
  enableAiAnalysis: boolean;
}

// Response filter options interface
export interface ResponseFilterOptions {
  style?: ResponseStyle;
  status?: ResponseStatus;
  minQuality?: number;
  timeRange?: "24h" | "7d" | "30d" | "all";
  limit?: number;
}

// Response generation options interface
export interface ResponseGenerationOptions {
  targetType: "tweet" | "mention";
  targetId: string;
  originalContent: string;
  originalAuthor: string;
  originalUrl: string;
  userId: string;
  styles: ResponseStyle[];
  responseType?: ResponseType;
  qualityThreshold?: number;
  riskTolerance?: RiskTolerance;
}

// Response quality analysis result interface
export interface QualityAnalysisResult {
  qualityMetrics: QualityMetrics;
  recommendations: string[];
  warnings: string[];
  suggestions: string[];
}

// Batch response operation interface
export interface BatchResponseOperation {
  action: "approve" | "reject" | "delete" | "archive";
  responseIds: string[];
  reason?: string;
}

// Response search options interface
export interface ResponseSearchOptions {
  searchTerm: string;
  userId: string;
  style?: ResponseStyle;
  status?: ResponseStatus;
  limit?: number;
}

// Twitter Account interface to fix component errors
export interface TwitterAccount {
  _id: string;
  userId: string;
  handle: string;
  displayName: string;
  isActive: boolean;
  isMonitoringEnabled?: boolean;
  mentionKeywords?: string[];
  responseSettings?: {
    autoRespond?: boolean;
    responseDelay?: number;
    maxResponsesPerDay?: number;
    responseStyle?: string;
    customPrompts?: string[];
    autoGenerateResponses?: boolean;
    minimumFollowerCount?: number;
    excludeKeywords?: string[];
  };
  createdAt: number;
  updatedAt?: number;
}

// Dashboard Statistics interface
export interface DashboardStats {
  tweetsAnswered?: number;
  totalTweets?: number;
  totalMentions?: number;
  totalResponses?: number;
  stats?: {
    accounts: {
      total: number;
      active: number;
      monitoring: number;
    };
    tweets: {
      total: number;
      recent: number;
      pending: number;
      analyzed: number;
      responseWorthy: number;
      averageEngagement: number;
    };
    mentions: {
      total: number;
      recent: number;
      unread: number;
      unprocessed: number;
      highPriority: number;
      responseOpportunities: number;
    };
    responses: {
      total: number;
      recent: number;
      draft: number;
      approved: number;
      posted: number;
      averageConfidence: number;
    };
    engagement: {
      totalLikes: number;
      totalRetweets: number;
      totalReplies: number;
      averageEngagementRate: number;
      topPerformingTweet: any;
    };
    activity: {
      tweetsScraped: number;
      mentionsFound: number;
      responsesGenerated: number;
      lastActivity: number | null;
    };
  };
}

// Response Statistics interface
export interface ResponseStats {
  totalResponses?: number;
  todayResponses?: number;
  weeklyResponses?: number;
  averageConfidence?: number;
  total?: number;
  today?: number;
  thisWeek?: number;
  byStatus?: {
    draft: number;
    approved: number;
    posted: number;
    declined: number;
    failed: number;
  };
  engagement?: {
    total: number;
    average: number;
  };
}

// Tweet Data interface
export interface TweetData {
  _id: string;
  twitterAccountId: string;
  tweetId: string;
  content: string;
  author: string;
  authorHandle: string;
  authorProfileImage?: string;
  isRetweet?: boolean;
  retweetedFrom?: string;
  createdAt: number;
  scrapedAt: number;
  timestamp?: number;
  citations?: string[];
  engagement: {
    likes: number;
    retweets: number;
    replies: number;
    views?: number;
  };
  analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
  analysisScore?: number;
  analysisReason?: string;
  embeddingId?: string;
  url?: string;
  metadata?: {
    isThread?: boolean;
    threadPosition?: number;
    hasMedia?: boolean;
    mediaType?: string;
    language?: string;
  };
}

// User Context interface for Tweet Assistant
export interface UserContext {
  userId?: string;
  interests?: string[];
  expertise?: string[];
  writingStyle?: string;
  brand?: string;
}

// Generated Response interface for Tweet Assistant
export interface GeneratedResponse {
  style: string;
  content: string;
  confidence: number;
  characterCount: number;
  estimatedEngagement: {
    likes: number;
    replies: number;
    retweets: number;
  };
}

// Export utility types
export type StyleIconMap = Record<ResponseStyle, any>;
export type StyleColorMap = Record<ResponseStyle, string>;
export type QualityScoreRange = "high" | "medium" | "low";
export type ResponseMetricKey = keyof QualityMetrics;