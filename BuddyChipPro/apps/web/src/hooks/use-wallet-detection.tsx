import { useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useMutation } from 'convex/react';
import { api } from '@BuddyChipAI/backend';

/**
 * Hook to automatically detect and store wallets from Clerk authentication
 * Runs when user logs in or authentication state changes
 */
export function useWalletDetection() {
  const { user, isLoaded } = useUser();
  const detectAndStoreWallet = useMutation(api.auth.walletDetection.detectAndStoreClerkWallet);

  useEffect(() => {
    if (!isLoaded || !user) return;

    // Auto-detect Clerk wallets on login
    const detectWallets = async () => {
      try {
        console.log('🔍 Checking for Clerk Web3 wallets:', user.web3Wallets);
        const result = await detectAndStoreWallet();
        if (result) {
          console.log('✅ Clerk wallet detected and stored:', result);
        } else {
          console.log('ℹ️ No Clerk wallet detected');
        }
      } catch (error) {
        console.log('⚠️ No Clerk wallet detected or error storing wallet:', error);
        // This is expected for users who didn't login with a wallet
      }
    };

    detectWallets();
  }, [isLoaded, user, detectAndStoreWallet]);

  return null; // This hook doesn't return anything, just runs side effects
}