import { z } from "zod";
import { validateEmail, validatePassword, validateName, validateUsername } from "./auth-utils";

/**
 * Zod schemas for authentication forms with custom validation
 */

// Custom email validator using our utility function
const emailValidator = z.string().refine(
  (email) => validateEmail(email).isValid,
  (email) => ({ message: validateEmail(email).error || "Invalid email" })
);

// Custom password validator
const passwordValidator = z.string().refine(
  (password) => validatePassword(password, 2).isValid,
  (password) => ({ message: validatePassword(password, 2).error || "Password too weak" })
);

// Custom name validator
const nameValidator = (fieldName: string) => z.string().refine(
  (name) => validateName(name, fieldName).isValid,
  (name) => ({ message: validateName(name, fieldName).error || `Invalid ${fieldName}` })
);

// Custom username validator
const usernameValidator = z.string().refine(
  (username) => validateUsername(username).isValid,
  (username) => ({ message: validateUsername(username).error || "Invalid username" })
);

/**
 * Sign-up form schema
 */
export const signUpSchema = z.object({
  firstName: nameValidator("First name"),
  lastName: nameValidator("Last name"),
  email: emailValidator,
  password: passwordValidator,
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: "You must accept the terms and conditions"
  }),
  marketingEmails: z.boolean().optional().default(false),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

/**
 * Sign-in form schema
 */
export const signInSchema = z.object({
  email: emailValidator,
  password: z.string().min(1, "Password is required"),
  rememberMe: z.boolean().optional().default(false),
});

/**
 * Profile setup schema (onboarding step)
 */
export const profileSetupSchema = z.object({
  displayName: z.string()
    .min(2, "Display name must be at least 2 characters")
    .max(50, "Display name must be less than 50 characters")
    .regex(/^[a-zA-Z0-9\s\-_']+$/, "Display name contains invalid characters"),
  bio: z.string()
    .max(160, "Bio must be less than 160 characters")
    .optional(),
  avatar: z.string().url("Invalid avatar URL").optional(),
  timezone: z.string().optional(),
  language: z.enum(["en", "es", "fr", "de", "it", "pt", "ja", "ko", "zh"]).default("en"),
});

/**
 * Twitter connection schema
 */
export const twitterConnectionSchema = z.object({
  twitterHandle: z.string()
    .min(1, "Twitter handle is required")
    .max(15, "Twitter handle must be 15 characters or less")
    .regex(/^[a-zA-Z0-9_]+$/, "Twitter handle can only contain letters, numbers, and underscores")
    .transform(handle => handle.replace(/^@/, '')), // Remove @ if present
  allowMentionMonitoring: z.boolean().default(true),
  allowDirectMessages: z.boolean().default(false),
  notificationPreferences: z.object({
    mentions: z.boolean().default(true),
    replies: z.boolean().default(true),
    directMessages: z.boolean().default(false),
  }).optional(),
});

/**
 * Preferences schema (onboarding step)
 */
export const preferencesSchema = z.object({
  // Notification preferences
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    sms: z.boolean().default(false),
    marketing: z.boolean().default(false),
  }),
  
  // Privacy preferences
  privacy: z.object({
    profileVisibility: z.enum(["public", "private", "friends"]).default("public"),
    showOnlineStatus: z.boolean().default(true),
    allowSearchEngineIndexing: z.boolean().default(true),
  }),
  
  // AI assistance preferences
  aiPreferences: z.object({
    responseStyle: z.enum(["professional", "casual", "witty", "formal"]).default("casual"),
    autoReply: z.boolean().default(false),
    contentSuggestions: z.boolean().default(true),
    sentimentAnalysis: z.boolean().default(true),
  }),
  
  // Content preferences
  contentPreferences: z.object({
    topics: z.array(z.string()).max(10, "Maximum 10 topics allowed").default([]),
    languages: z.array(z.string()).min(1, "At least one language required").default(["en"]),
    contentTypes: z.array(z.enum(["text", "images", "videos", "links"])).default(["text"]),
  }),
});

/**
 * Password reset schema
 */
export const passwordResetSchema = z.object({
  email: emailValidator,
});

/**
 * New password schema (for password reset)
 */
export const newPasswordSchema = z.object({
  password: passwordValidator,
  confirmPassword: z.string(),
  token: z.string().min(1, "Reset token is required"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

/**
 * Change password schema (for authenticated users)
 */
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: passwordValidator,
  confirmNewPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: "New passwords don't match",
  path: ["confirmNewPassword"],
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "New password must be different from current password",
  path: ["newPassword"],
});

/**
 * Email verification schema
 */
export const emailVerificationSchema = z.object({
  code: z.string()
    .length(6, "Verification code must be 6 digits")
    .regex(/^\d{6}$/, "Verification code must contain only numbers"),
});

/**
 * Two-factor authentication setup schema
 */
export const twoFactorSetupSchema = z.object({
  method: z.enum(["sms", "email", "authenticator"]),
  phoneNumber: z.string()
    .regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone number format")
    .optional(),
  backupCodes: z.array(z.string()).length(8, "Must have exactly 8 backup codes").optional(),
});

/**
 * Two-factor authentication verification schema
 */
export const twoFactorVerificationSchema = z.object({
  code: z.string()
    .length(6, "Authentication code must be 6 digits")
    .regex(/^\d{6}$/, "Authentication code must contain only numbers"),
  method: z.enum(["sms", "email", "authenticator"]),
  rememberDevice: z.boolean().default(false),
});

/**
 * Account deletion schema
 */
export const accountDeletionSchema = z.object({
  password: z.string().min(1, "Password is required to delete account"),
  confirmDeletion: z.literal("DELETE", {
    errorMap: () => ({ message: 'Type "DELETE" to confirm account deletion' }),
  }),
  reason: z.enum([
    "not_useful",
    "too_expensive", 
    "privacy_concerns",
    "technical_issues",
    "switching_service",
    "other"
  ]).optional(),
  feedback: z.string().max(500, "Feedback must be less than 500 characters").optional(),
});

// Type exports for use in components
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type SignInFormData = z.infer<typeof signInSchema>;
export type ProfileSetupFormData = z.infer<typeof profileSetupSchema>;
export type TwitterConnectionFormData = z.infer<typeof twitterConnectionSchema>;
export type PreferencesFormData = z.infer<typeof preferencesSchema>;
export type PasswordResetFormData = z.infer<typeof passwordResetSchema>;
export type NewPasswordFormData = z.infer<typeof newPasswordSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type EmailVerificationFormData = z.infer<typeof emailVerificationSchema>;
export type TwoFactorSetupFormData = z.infer<typeof twoFactorSetupSchema>;
export type TwoFactorVerificationFormData = z.infer<typeof twoFactorVerificationSchema>;
export type AccountDeletionFormData = z.infer<typeof accountDeletionSchema>;
