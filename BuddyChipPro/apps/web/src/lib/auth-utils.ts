/**
 * Authentication utility functions for BuddyChip
 * Provides validation, formatting, and helper functions for auth flows
 */

// Email validation regex (RFC 5322 compliant)
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Password strength regex patterns
const PASSWORD_PATTERNS = {
  minLength: /.{8,}/,
  hasUppercase: /[A-Z]/,
  hasLowercase: /[a-z]/,
  hasNumber: /\d/,
  hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,
};

// Username validation (alphanumeric + underscores, 3-20 chars)
const USERNAME_REGEX = /^[a-zA-Z0-9_]{3,20}$/;

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  suggestions?: string[];
}

export interface PasswordStrength {
  score: number; // 0-4
  feedback: string;
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
  };
}

/**
 * Validates email address format
 */
export function validateEmail(email: string): ValidationResult {
  console.log(`📧 Validating email: ${email}`);
  
  if (!email) {
    return { isValid: false, error: "Email is required" };
  }
  
  if (!EMAIL_REGEX.test(email)) {
    return { 
      isValid: false, 
      error: "Please enter a valid email address",
      suggestions: ["Check for typos", "Make sure it includes @ and a domain"]
    };
  }
  
  return { isValid: true };
}

/**
 * Validates username format
 */
export function validateUsername(username: string): ValidationResult {
  console.log(`👤 Validating username: ${username}`);
  
  if (!username) {
    return { isValid: false, error: "Username is required" };
  }
  
  if (!USERNAME_REGEX.test(username)) {
    return { 
      isValid: false, 
      error: "Username must be 3-20 characters, letters, numbers, and underscores only",
      suggestions: ["Use only letters, numbers, and underscores", "Must be 3-20 characters long"]
    };
  }
  
  return { isValid: true };
}

/**
 * Analyzes password strength and provides feedback
 */
export function analyzePasswordStrength(password: string): PasswordStrength {
  console.log(`🔒 Analyzing password strength for ${password.length} character password`);
  
  const requirements = {
    minLength: PASSWORD_PATTERNS.minLength.test(password),
    hasUppercase: PASSWORD_PATTERNS.hasUppercase.test(password),
    hasLowercase: PASSWORD_PATTERNS.hasLowercase.test(password),
    hasNumber: PASSWORD_PATTERNS.hasNumber.test(password),
    hasSpecialChar: PASSWORD_PATTERNS.hasSpecialChar.test(password),
  };
  
  const metRequirements = Object.values(requirements).filter(Boolean).length;
  const score = Math.min(metRequirements, 4);
  
  const getFeedback = (score: number): string => {
    switch (score) {
      case 0:
      case 1:
        return "Very weak - Add more character types";
      case 2:
        return "Weak - Consider adding numbers or symbols";
      case 3:
        return "Good - Almost there!";
      case 4:
        return "Strong - Great password!";
      default:
        return "Excellent - Very secure!";
    }
  };
  
  return {
    score,
    feedback: getFeedback(score),
    requirements,
  };
}

/**
 * Validates password with custom requirements
 */
export function validatePassword(password: string, minScore: number = 2): ValidationResult {
  console.log(`🔐 Validating password with minimum score: ${minScore}`);
  
  if (!password) {
    return { isValid: false, error: "Password is required" };
  }
  
  const strength = analyzePasswordStrength(password);
  
  if (strength.score < minScore) {
    const suggestions = [];
    if (!strength.requirements.minLength) suggestions.push("Use at least 8 characters");
    if (!strength.requirements.hasUppercase) suggestions.push("Add uppercase letters");
    if (!strength.requirements.hasLowercase) suggestions.push("Add lowercase letters");
    if (!strength.requirements.hasNumber) suggestions.push("Add numbers");
    if (!strength.requirements.hasSpecialChar) suggestions.push("Add special characters");
    
    return {
      isValid: false,
      error: `Password is too weak (${strength.feedback})`,
      suggestions,
    };
  }
  
  return { isValid: true };
}

/**
 * Validates name field (first name, last name, display name)
 */
export function validateName(name: string, fieldName: string = "Name"): ValidationResult {
  console.log(`📝 Validating ${fieldName}: ${name}`);
  
  if (!name) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  if (name.length < 2) {
    return { isValid: false, error: `${fieldName} must be at least 2 characters` };
  }
  
  if (name.length > 50) {
    return { isValid: false, error: `${fieldName} must be less than 50 characters` };
  }
  
  // Check for valid characters (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  if (!nameRegex.test(name)) {
    return { 
      isValid: false, 
      error: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes`,
      suggestions: ["Remove numbers and special characters", "Use only letters and common punctuation"]
    };
  }
  
  return { isValid: true };
}

/**
 * Formats display name from email or username
 */
export function formatDisplayName(input: string): string {
  console.log(`✨ Formatting display name from: ${input}`);
  
  // If it's an email, extract the part before @
  if (input.includes('@')) {
    input = input.split('@')[0];
  }
  
  // Replace dots, underscores, and hyphens with spaces
  let formatted = input.replace(/[._-]/g, ' ');
  
  // Capitalize first letter of each word
  formatted = formatted.replace(/\b\w/g, l => l.toUpperCase());
  
  return formatted.trim();
}

/**
 * Generates a secure random password
 */
export function generateSecurePassword(length: number = 12): string {
  console.log(`🎲 Generating secure password of length: ${length}`);
  
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = lowercase + uppercase + numbers + symbols;
  
  // Ensure at least one character from each category
  let password = '';
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Debounce function for real-time validation
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Sanitizes user input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Checks if a string contains common password patterns to avoid
 */
export function hasCommonPatterns(password: string): boolean {
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i,
  ];
  
  return commonPatterns.some(pattern => pattern.test(password));
}

/**
 * Estimates time to crack password (very rough estimate)
 */
export function estimateCrackTime(password: string): string {
  const charset = {
    lowercase: /[a-z]/.test(password) ? 26 : 0,
    uppercase: /[A-Z]/.test(password) ? 26 : 0,
    numbers: /\d/.test(password) ? 10 : 0,
    symbols: /[^a-zA-Z0-9]/.test(password) ? 32 : 0,
  };
  
  const charsetSize = Object.values(charset).reduce((sum, size) => sum + size, 0);
  const combinations = Math.pow(charsetSize, password.length);
  
  // Assume 1 billion attempts per second
  const secondsToCrack = combinations / (2 * 1000000000);
  
  if (secondsToCrack < 60) return "Less than a minute";
  if (secondsToCrack < 3600) return `${Math.round(secondsToCrack / 60)} minutes`;
  if (secondsToCrack < 86400) return `${Math.round(secondsToCrack / 3600)} hours`;
  if (secondsToCrack < 31536000) return `${Math.round(secondsToCrack / 86400)} days`;
  return `${Math.round(secondsToCrack / 31536000)} years`;
}
