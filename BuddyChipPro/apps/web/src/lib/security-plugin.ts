/**
 * Vite Security Headers Plugin
 * 🔐 SECURITY: Apply production security headers during development and build
 */

import type { Plugin } from 'vite';

/**
 * Security headers configuration
 */
const SECURITY_HEADERS = {
  // Content Security Policy - restrictive for production security
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.clerk.dev https://accounts.google.com https://apis.google.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: blob: https: https://img.clerk.com https://lh3.googleusercontent.com https://pbs.twimg.com https://abs.twimg.com",
    "connect-src 'self' https://api.openai.com https://openrouter.ai https://api.x.ai https://api.twitterapi.io https://api.clerk.dev https://clerk.dev wss://clerk.dev https://*.convex.cloud wss://*.convex.cloud https://accounts.google.com",
    "frame-src 'self' https://accounts.google.com https://clerk.dev",
    "object-src 'none'",
    "embed-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join('; '),

  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',

  // Enable XSS protection
  'X-XSS-Protection': '1; mode=block',

  // Control framing (prevent clickjacking)
  'X-Frame-Options': 'DENY',

  // Referrer policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',

  // Permissions policy - disable unnecessary browser features
  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
  ].join(', '),

  // Strict Transport Security (HTTPS enforcement)
  'Strict-Transport-Security': 'max-age=********; includeSubDomains; preload',
};

/**
 * CORS configuration
 */
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*', // Will be dynamically set based on environment
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin',
  'Access-Control-Max-Age': '86400',
};

/**
 * Vite plugin to add security headers
 */
export function securityHeadersPlugin(): Plugin {
  return {
    name: 'security-headers',
    configureServer(server) {
      // Apply security headers to development server
      server.middlewares.use((req, res, next) => {
        // Set security headers
        Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
          res.setHeader(key, value);
        });

        // Set CORS headers for development
        if (process.env.NODE_ENV === 'development') {
          Object.entries(CORS_HEADERS).forEach(([key, value]) => {
            if (key === 'Access-Control-Allow-Origin') {
              // Allow localhost and 127.0.0.1 for development
              const origin = req.headers.origin;
              if (origin && (origin.includes('localhost') || origin.includes('127.0.0.1'))) {
                res.setHeader(key, origin);
              } else {
                res.setHeader(key, 'http://localhost:3000');
              }
            } else {
              res.setHeader(key, value);
            }
          });
        }

        // Handle preflight requests
        if (req.method === 'OPTIONS') {
          res.statusCode = 200;
          res.end();
          return;
        }

        next();
      });
    },
    generateBundle() {
      // For production builds, headers should be configured at the hosting level
      console.log('🔒 Security headers configured for production deployment');
      console.log('📋 Make sure to configure these headers in your hosting environment:');
      console.log('   - Vercel: Use vercel.json configuration');
      console.log('   - Netlify: Use _headers file');
      console.log('   - Cloudflare: Use Page Rules or Workers');
      console.log('   - Other: Configure in your web server');
    },
  };
}

/**
 * Production security headers for hosting configuration
 * Copy these to your hosting provider's configuration
 */
export const PRODUCTION_HEADERS_CONFIG = {
  vercel: {
    // vercel.json configuration
    headers: [
      {
        source: '/(.*)',
        headers: Object.entries(SECURITY_HEADERS).map(([key, value]) => ({
          key,
          value,
        })),
      },
    ],
  },
  netlify: {
    // _headers file content
    headers: Object.entries(SECURITY_HEADERS)
      .map(([key, value]) => `  ${key}: ${value}`)
      .join('\n'),
  },
};