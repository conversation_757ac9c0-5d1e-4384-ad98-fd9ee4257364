import { createFileRoute } from '@tanstack/react-router'
import { lazy, Suspense } from 'react'
import { AuthGuards } from '../components/auth/route-guard'
import Loader from '../components/loader'

const LiveSearchPage = lazy(() => import('../components/pages/LiveSearchPage'))

function LiveSearchComponent() {
  return (
    <AuthGuards.Dashboard>
      <Suspense fallback={<Loader />}>
        <LiveSearchPage />
      </Suspense>
    </AuthGuards.Dashboard>
  )
}

export const Route = createFileRoute('/live-search')({
  component: LiveSearchComponent,
})