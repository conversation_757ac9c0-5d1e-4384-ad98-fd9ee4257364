import Header from "@/components/header";
import Loader from "@/components/loader";
import { Chatbot } from "@/components/chatbot/chatbot";
import { Toaster } from "@/components/ui/sonner";
import { useWalletDetection } from "@/hooks/use-wallet-detection";
import {
  HeadContent,
  Outlet,
  createRootRouteWithContext,
  useRouterState,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import "../index.css";

export interface RouterAppContext {}

export const Route = createRootRouteWithContext<RouterAppContext>()({
  component: RootComponent,
  head: () => ({
    meta: [
      {
        title: "BuddyChipAI",
      },
      {
        name: "description",
        content: "BuddyChipAI - AI-powered social media management and engagement platform",
      },
    ],
    links: [
      {
        rel: "icon",
        href: "/Logo.svg",
        type: "image/svg+xml",
      },
      {
        rel: "shortcut icon",
        href: "/Logo.svg",
      },
      {
        rel: "apple-touch-icon",
        href: "/Logo.svg",
      },
    ],
  }),
});

function RootComponent() {
  const isFetching = useRouterState({
    select: (s) => s.isLoading,
  });

  const currentPath = useRouterState({
    select: (s) => s.location.pathname,
  });

  // Auto-detect wallets from Clerk authentication
  useWalletDetection();

  // Don't show main header on landing page (it has its own navigation)
  const showHeader = currentPath !== '/';

  return (
    <>
      <HeadContent />
      <div className="min-h-screen bg-[#0E1117]">
        {showHeader && <Header />}
        {isFetching ? <Loader /> : <Outlet />}
        <Chatbot />
      </div>
      <Toaster richColors />
      <TanStackRouterDevtools position="bottom-left" />
    </>
  );
}
