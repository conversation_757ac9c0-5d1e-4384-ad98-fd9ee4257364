import { createFileRoute } from '@tanstack/react-router'
import { lazy, Suspense } from 'react'
import { AuthGuards } from '../components/auth/route-guard'
import Loader from '../components/loader'

const ImageGenerationPage = lazy(() => import('../components/pages/ImageGenerationPage'))

function ImageGenerationComponent() {
  return (
    <AuthGuards.Dashboard>
      <Suspense fallback={<Loader />}>
        <ImageGenerationPage />
      </Suspense>
    </AuthGuards.Dashboard>
  )
}

export const Route = createFileRoute('/image-generation')({
  component: ImageGenerationComponent,
})