import { createFileRoute } from '@tanstack/react-router'
import { MentionsCenter } from '../components/reply-guy/mentions-center'
import { CollapsibleFilters } from '../components/reply-guy/collapsible-filters'
import { AuthGuards } from '../components/auth/enhanced-auth-guards'
import { AuthDebugComponent } from "../components/auth/auth-debug-component";
import { useState } from "react";
import { Button } from "../components/ui/button";
import { DEBUG_CONFIG, DebugWrapper } from "../lib/debug-config";
import { Clock, RefreshCw, Settings } from "lucide-react";
import { useAction, useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";

export const Route = createFileRoute('/mentions')({
  component: MentionsPage,
})

function MentionsPage() {
  const [showDebug, setShowDebug] = useState(false);

  // --- Filter States ---------------------------------------------------------------
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [selectedAccount, setSelectedAccount] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<string>("7d");
  const [selectedSentiment, setSelectedSentiment] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("newest");

  // --- Data for filters -------------------------------------------------------------
  const currentUser = useQuery(api.userQueries.getCurrentUser);
  const twitterAccounts = useQuery(api.users.getUserTwitterAccounts, currentUser ? {} : "skip");

  // --- Refresh button state & logic ------------------------------------------------
  const refreshMentions = useAction(api.mentions.mentionMutations.refreshMentions);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);

  const handleRefresh = async () => {
    if (isRefreshing) return;
    setIsRefreshing(true);
    try {
      await refreshMentions({
        enableViralDetection: true,
        priorityMode: true,
        maxConcurrency: 3,
      });
      setLastUpdate(Date.now());
    } catch (err) {
      console.error("❌ Failed to refresh mentions", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  // --- Settings button logic ------------------------------------------------------
  const openSettingsTab = () => {
    // Use hash navigation so MentionsCenter can listen & switch tabs
    if (typeof window !== "undefined") {
      window.location.hash = "settings";
      // Fallback scroll to bottom if hash-based tab switching is not yet implemented
      setTimeout(() => {
        const settingsEl = document.getElementById("mentions-settings-section");
        if (settingsEl) settingsEl.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  };

  return (
    <AuthGuards.Dashboard>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-400 bg-clip-text text-transparent mb-3">
              Mentions Center
            </h1>
            <p className="text-lg text-[var(--buddychip-white)]">
              Monitor mentions and engage with your community
            </p>
          </div>
          <div className="flex items-center gap-3">
            <CollapsibleFilters
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              selectedPriority={selectedPriority}
              setSelectedPriority={setSelectedPriority}
              selectedAccount={selectedAccount}
              setSelectedAccount={setSelectedAccount}
              timeRange={timeRange}
              setTimeRange={setTimeRange}
              selectedSentiment={selectedSentiment}
              setSelectedSentiment={setSelectedSentiment}
              sortBy={sortBy}
              setSortBy={setSortBy}
              twitterAccounts={twitterAccounts}
            />
            <Button 
              variant="ghost" 
              size="sm" 
              className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] transition-all duration-200"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`${isRefreshing ? "animate-spin" : ""} h-4 w-4 mr-2`} />
              {isRefreshing ? "Refreshing" : "Refresh"}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
              onClick={openSettingsTab}
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <DebugWrapper category="auth">
              <Button 
                onClick={() => setShowDebug(!showDebug)}
                variant="outline"
                size="sm"
                className="border-[#316FE3] text-[#316FE3] hover:bg-[#316FE3] hover:text-white"
              >
                {showDebug ? "Hide" : "Show"} Auth Debug
              </Button>
            </DebugWrapper>
          </div>
        </div>
        
        {/* Debug component only rendered if auth debugging is enabled */}
        <DebugWrapper category="auth" enabled={showDebug}>
          <div className="space-y-4 mb-6">
            <AuthDebugComponent />
          </div>
        </DebugWrapper>
        
        <MentionsCenter 
          searchQuery={searchQuery}
          selectedPriority={selectedPriority}
          selectedAccount={selectedAccount}
          timeRange={timeRange}
          selectedSentiment={selectedSentiment}
          sortBy={sortBy}
        />
      </div>
    </AuthGuards.Dashboard>
  )
}