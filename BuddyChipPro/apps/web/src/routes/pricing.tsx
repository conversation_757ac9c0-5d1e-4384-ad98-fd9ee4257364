import { createFileRoute } from '@tanstack/react-router';
import { PricingTable } from '@clerk/clerk-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Check, X, Star, Zap, Crown } from 'lucide-react';
import { useQuery } from 'convex/react';
import { api } from '@BuddyChipAI/backend';

export const Route = createFileRoute('/pricing')({
  component: PricingPage,
});

function PricingPage() {
  const planComparison = useQuery(api.billing.accessControl.getPlanComparison);
  const userPermissions = useQuery(api.billing.accessControl.getUserPermissions);

  if (!planComparison) {
    return (
      <div className="min-h-screen bg-[var(--buddychip-dark-bg)] flex items-center justify-center">
        <div className="text-[var(--buddychip-white)]">Loading pricing...</div>
      </div>
    );
  }

  const currentPlan = userPermissions?.planId || 'starter';

  return (
    <div className="min-h-screen bg-[var(--buddychip-dark-bg)] text-[var(--buddychip-white)]">
      {/* Header */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[var(--buddychip-accent)] to-[var(--buddychip-secondary)] bg-clip-text text-transparent">
            Choose Your Plan
          </h1>
          <p className="text-xl text-[var(--buddychip-grey-text)] max-w-3xl mx-auto">
            Unlock the full potential of AI-powered Twitter engagement with BuddyChip Pro. 
            Choose the plan that fits your needs and scale as you grow.
          </p>
        </div>

        {/* Current Plan Badge */}
        {userPermissions && (
          <div className="text-center mb-8">
            <Badge variant="secondary" className="text-lg px-4 py-2">
              Current Plan: {planComparison[currentPlan].name}
            </Badge>
          </div>
        )}

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {/* Starter Plan */}
          <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] relative">
            <CardHeader className="text-center pb-8">
              <div className="flex justify-center mb-4">
                <Star className="h-8 w-8 text-[var(--buddychip-accent)]" />
              </div>
              <CardTitle className="text-2xl text-[var(--buddychip-white)]">Starter</CardTitle>
              <CardDescription className="text-[var(--buddychip-grey-text)]">
                Perfect for individuals getting started
              </CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-[var(--buddychip-white)]">$19</span>
                <span className="text-[var(--buddychip-grey-text)]">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                <FeatureItem included={true}>Up to 3 Twitter accounts</FeatureItem>
                <FeatureItem included={true}>100 AI responses/month</FeatureItem>
                <FeatureItem included={true}>Basic AI models</FeatureItem>
                <FeatureItem included={true}>1,000 API requests/day</FeatureItem>
                <FeatureItem included={false}>Premium AI models</FeatureItem>
                <FeatureItem included={false}>Image generation</FeatureItem>
                <FeatureItem included={false}>Advanced analytics</FeatureItem>
              </ul>
              {currentPlan === 'starter' ? (
                <Button disabled className="w-full">Current Plan</Button>
              ) : (
                <Button variant="outline" className="w-full">
                  Downgrade to Starter
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Pro Plan */}
          <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-accent)] relative transform scale-105">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)] px-4 py-1">
                Most Popular
              </Badge>
            </div>
            <CardHeader className="text-center pb-8">
              <div className="flex justify-center mb-4">
                <Zap className="h-8 w-8 text-[var(--buddychip-accent)]" />
              </div>
              <CardTitle className="text-2xl text-[var(--buddychip-white)]">Pro</CardTitle>
              <CardDescription className="text-[var(--buddychip-grey-text)]">
                Ideal for professionals and small teams
              </CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-[var(--buddychip-white)]">$49</span>
                <span className="text-[var(--buddychip-grey-text)]">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                <FeatureItem included={true}>Up to 10 Twitter accounts</FeatureItem>
                <FeatureItem included={true}>500 AI responses/month</FeatureItem>
                <FeatureItem included={true}>Premium AI models (GPT-4, Claude)</FeatureItem>
                <FeatureItem included={true}>50 image generations/month</FeatureItem>
                <FeatureItem included={true}>5,000 API requests/day</FeatureItem>
                <FeatureItem included={true}>Advanced analytics</FeatureItem>
                <FeatureItem included={true}>Priority support</FeatureItem>
              </ul>
              {currentPlan === 'pro' ? (
                <Button disabled className="w-full">Current Plan</Button>
              ) : (
                <Button className="w-full bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80">
                  {currentPlan === 'starter' ? 'Upgrade to Pro' : 'Downgrade to Pro'}
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Enterprise Plan */}
          <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] relative">
            <CardHeader className="text-center pb-8">
              <div className="flex justify-center mb-4">
                <Crown className="h-8 w-8 text-[var(--buddychip-secondary)]" />
              </div>
              <CardTitle className="text-2xl text-[var(--buddychip-white)]">Enterprise</CardTitle>
              <CardDescription className="text-[var(--buddychip-grey-text)]">
                Complete solution for agencies and large organizations
              </CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold text-[var(--buddychip-white)]">$99</span>
                <span className="text-[var(--buddychip-grey-text)]">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                <FeatureItem included={true}>Unlimited Twitter accounts</FeatureItem>
                <FeatureItem included={true}>Unlimited AI responses</FeatureItem>
                <FeatureItem included={true}>All AI models + latest versions</FeatureItem>
                <FeatureItem included={true}>Unlimited image generation</FeatureItem>
                <FeatureItem included={true}>25,000 API requests/day</FeatureItem>
                <FeatureItem included={true}>Bulk processing</FeatureItem>
                <FeatureItem included={true}>White-label options</FeatureItem>
                <FeatureItem included={true}>24/7 priority support</FeatureItem>
              </ul>
              {currentPlan === 'enterprise' ? (
                <Button disabled className="w-full">Current Plan</Button>
              ) : (
                <Button 
                  variant="outline" 
                  className="w-full border-[var(--buddychip-secondary)] text-[var(--buddychip-secondary)] hover:bg-[var(--buddychip-secondary)] hover:text-[var(--buddychip-white)]"
                >
                  Upgrade to Enterprise
                </Button>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Clerk Pricing Table */}
        <div className="mt-16">
          <h2 className="text-3xl font-bold text-center mb-8 text-[var(--buddychip-white)]">
            Secure Billing with Clerk
          </h2>
          <div className="max-w-4xl mx-auto bg-[var(--buddychip-light-bg)] rounded-lg p-8">
            <PricingTable />
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h2 className="text-3xl font-bold text-center mb-8 text-[var(--buddychip-white)]">
            Frequently Asked Questions
          </h2>
          <div className="max-w-4xl mx-auto space-y-6">
            <FAQItem 
              question="Can I change my plan anytime?"
              answer="Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and billing is prorated."
            />
            <FAQItem 
              question="What happens if I exceed my limits?"
              answer="We'll notify you when you're approaching your limits. You can upgrade your plan or wait for the next billing cycle for limits to reset."
            />
            <FAQItem 
              question="Is there a free trial?"
              answer="Yes! All new users get a 7-day free trial of the Pro plan to explore all features."
            />
            <FAQItem 
              question="How secure is my data?"
              answer="We use enterprise-grade security with end-to-end encryption. Your data is never shared with third parties."
            />
          </div>
        </div>
      </div>
    </div>
  );
}

function FeatureItem({ included, children }: { included: boolean; children: React.ReactNode }) {
  return (
    <li className="flex items-center space-x-3">
      {included ? (
        <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
      ) : (
        <X className="h-5 w-5 text-red-500 flex-shrink-0" />
      )}
      <span className={included ? 'text-[var(--buddychip-white)]' : 'text-[var(--buddychip-grey-text)] line-through'}>
        {children}
      </span>
    </li>
  );
}

function FAQItem({ question, answer }: { question: string; answer: string }) {
  return (
    <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
      <CardHeader>
        <CardTitle className="text-lg text-[var(--buddychip-white)]">{question}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-[var(--buddychip-grey-text)]">{answer}</p>
      </CardContent>
    </Card>
  );
}
