import { createFileRoute } from "@tanstack/react-router";
import { SignUp } from "@clerk/clerk-react";
import { AuthLayout } from "@/components/auth/auth-layout";
import { clerkAppearance } from "@/lib/clerk-appearance";

/**
 * /sign-up – custom sign-up page wrapped in AuthLayout.
 */
export const Route = createFileRoute("/sign-up")({
  component: SignUpPage,
});

function SignUpPage() {
  return (
    <AuthLayout>
      <SignUp
        appearance={clerkAppearance as any}
        routing="hash"
        redirectUrl="/onboarding"
        signInUrl="/sign-in"
      />
    </AuthLayout>
  );
} 