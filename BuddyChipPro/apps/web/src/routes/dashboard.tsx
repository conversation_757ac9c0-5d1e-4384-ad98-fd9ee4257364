import { createFileRoute } from '@tanstack/react-router'
import { lazy, Suspense } from 'react'
import { AuthGuards } from '../components/auth/route-guard'
import Loader from '../components/loader'

const DashboardPage = lazy(() => import('../components/pages/DashboardPage'))

function DashboardComponent() {
  return (
    <AuthGuards.Dashboard>
      <Suspense fallback={<Loader />}>
        <DashboardPage />
      </Suspense>
    </AuthGuards.Dashboard>
  )
}

export const Route = createFileRoute('/dashboard')({
  component: DashboardComponent,
})