import { createFileRoute } from "@tanstack/react-router";
import { SignIn } from "@clerk/clerk-react";
import { AuthLayout } from "@/components/auth/auth-layout";
import { clerkAppearance } from "@/lib/clerk-appearance";

/**
 * /sign-in – custom sign-in page wrapped in AuthLayout.
 */
export const Route = createFileRoute("/sign-in")({
  component: SignInPage,
});

function SignInPage() {
  return (
    <AuthLayout>
      <SignIn
        appearance={clerkAppearance as any}
        routing="hash"
        redirectUrl="/onboarding"
        signUpUrl="/sign-up"
      />
    </AuthLayout>
  );
} 