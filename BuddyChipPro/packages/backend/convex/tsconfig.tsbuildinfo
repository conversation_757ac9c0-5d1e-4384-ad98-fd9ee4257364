{"fileNames": ["../node_modules/typescript/lib/lib.es5.d.ts", "../node_modules/typescript/lib/lib.es2015.d.ts", "../node_modules/typescript/lib/lib.es2016.d.ts", "../node_modules/typescript/lib/lib.es2017.d.ts", "../node_modules/typescript/lib/lib.es2018.d.ts", "../node_modules/typescript/lib/lib.es2019.d.ts", "../node_modules/typescript/lib/lib.es2020.d.ts", "../node_modules/typescript/lib/lib.es2021.d.ts", "../node_modules/typescript/lib/lib.dom.d.ts", "../node_modules/typescript/lib/lib.es2015.core.d.ts", "../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../node_modules/typescript/lib/lib.es2016.intl.d.ts", "../node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../node_modules/typescript/lib/lib.es2017.date.d.ts", "../node_modules/typescript/lib/lib.es2017.object.d.ts", "../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../node_modules/typescript/lib/lib.es2017.string.d.ts", "../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../node_modules/typescript/lib/lib.es2019.array.d.ts", "../node_modules/typescript/lib/lib.es2019.object.d.ts", "../node_modules/typescript/lib/lib.es2019.string.d.ts", "../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../node_modules/typescript/lib/lib.es2020.date.d.ts", "../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../node_modules/typescript/lib/lib.es2020.string.d.ts", "../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../node_modules/typescript/lib/lib.es2020.number.d.ts", "../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../node_modules/typescript/lib/lib.es2021.string.d.ts", "../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../node_modules/typescript/lib/lib.decorators.d.ts", "../node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../../node_modules/@types/react/global.d.ts", "../../../node_modules/csstype/index.d.ts", "../../../node_modules/@types/react/index.d.ts", "../../../node_modules/@types/react/jsx-runtime.d.ts", "../../../node_modules/convex/dist/esm-types/values/value.d.ts", "../../../node_modules/convex/dist/esm-types/type_utils.d.ts", "../../../node_modules/convex/dist/esm-types/values/validators.d.ts", "../../../node_modules/convex/dist/esm-types/values/validator.d.ts", "../../../node_modules/convex/dist/esm-types/values/base64.d.ts", "../../../node_modules/convex/dist/esm-types/values/errors.d.ts", "../../../node_modules/convex/dist/esm-types/values/compare.d.ts", "../../../node_modules/convex/dist/esm-types/values/index.d.ts", "../../../node_modules/convex/dist/esm-types/server/authentication.d.ts", "../../../node_modules/convex/dist/esm-types/server/data_model.d.ts", "../../../node_modules/convex/dist/esm-types/server/filter_builder.d.ts", "../../../node_modules/convex/dist/esm-types/server/index_range_builder.d.ts", "../../../node_modules/convex/dist/esm-types/server/pagination.d.ts", "../../../node_modules/convex/dist/esm-types/server/search_filter_builder.d.ts", "../../../node_modules/convex/dist/esm-types/server/query.d.ts", "../../../node_modules/convex/dist/esm-types/server/system_fields.d.ts", "../../../node_modules/convex/dist/esm-types/server/schema.d.ts", "../../../node_modules/convex/dist/esm-types/server/database.d.ts", "../../../node_modules/convex/dist/esm-types/server/api.d.ts", "../../../node_modules/convex/dist/esm-types/server/scheduler.d.ts", "../../../node_modules/convex/dist/esm-types/server/vector_search.d.ts", "../../../node_modules/convex/dist/esm-types/server/registration.d.ts", "../../../node_modules/convex/dist/esm-types/server/impl/registration_impl.d.ts", "../../../node_modules/convex/dist/esm-types/server/storage.d.ts", "../../../node_modules/convex/dist/esm-types/server/cron.d.ts", "../../../node_modules/convex/dist/esm-types/server/router.d.ts", "../../../node_modules/convex/dist/esm-types/server/components/paths.d.ts", "../../../node_modules/convex/dist/esm-types/server/components/index.d.ts", "../../../node_modules/convex/dist/esm-types/server/index.d.ts", "./schema.ts", "./_generated/datamodel.d.ts", "./_generated/server.d.ts", "./http.ts", "./todos.ts", "./users.ts", "../../../node_modules/openai/_shims/manual-types.d.ts", "../../../node_modules/openai/_shims/auto/types.d.ts", "../../../node_modules/openai/streaming.d.ts", "../../../node_modules/openai/error.d.ts", "../../../node_modules/openai/_shims/multipartbody.d.ts", "../../../node_modules/openai/uploads.d.ts", "../../../node_modules/openai/core.d.ts", "../../../node_modules/openai/_shims/index.d.ts", "../../../node_modules/openai/pagination.d.ts", "../../../node_modules/openai/resources/shared.d.ts", "../../../node_modules/openai/resources/batches.d.ts", "../../../node_modules/openai/resources/chat/completions/messages.d.ts", "../../../node_modules/openai/resources/chat/completions/completions.d.ts", "../../../node_modules/openai/resources/completions.d.ts", "../../../node_modules/openai/resources/embeddings.d.ts", "../../../node_modules/openai/resources/files.d.ts", "../../../node_modules/openai/resources/images.d.ts", "../../../node_modules/openai/resources/models.d.ts", "../../../node_modules/openai/resources/moderations.d.ts", "../../../node_modules/openai/resources/audio/speech.d.ts", "../../../node_modules/openai/resources/audio/transcriptions.d.ts", "../../../node_modules/openai/resources/audio/translations.d.ts", "../../../node_modules/openai/resources/audio/audio.d.ts", "../../../node_modules/openai/resources/beta/threads/messages.d.ts", "../../../node_modules/openai/resources/beta/threads/runs/steps.d.ts", "../../../node_modules/openai/resources/beta/threads/runs/runs.d.ts", "../../../node_modules/openai/lib/eventstream.d.ts", "../../../node_modules/openai/lib/assistantstream.d.ts", "../../../node_modules/openai/resources/beta/threads/threads.d.ts", "../../../node_modules/openai/resources/beta/assistants.d.ts", "../../../node_modules/openai/resources/chat/completions.d.ts", "../../../node_modules/openai/lib/abstractchatcompletionrunner.d.ts", "../../../node_modules/openai/lib/chatcompletionstream.d.ts", "../../../node_modules/openai/lib/responsesparser.d.ts", "../../../node_modules/openai/resources/responses/input-items.d.ts", "../../../node_modules/openai/lib/responses/eventtypes.d.ts", "../../../node_modules/openai/lib/responses/responsestream.d.ts", "../../../node_modules/openai/resources/responses/responses.d.ts", "../../../node_modules/openai/lib/parser.d.ts", "../../../node_modules/openai/lib/chatcompletionstreamingrunner.d.ts", "../../../node_modules/openai/lib/jsonschema.d.ts", "../../../node_modules/openai/lib/runnablefunction.d.ts", "../../../node_modules/openai/lib/chatcompletionrunner.d.ts", "../../../node_modules/openai/resources/beta/chat/completions.d.ts", "../../../node_modules/openai/resources/beta/chat/chat.d.ts", "../../../node_modules/openai/resources/beta/realtime/sessions.d.ts", "../../../node_modules/openai/resources/beta/realtime/transcription-sessions.d.ts", "../../../node_modules/openai/resources/beta/realtime/realtime.d.ts", "../../../node_modules/openai/resources/beta/beta.d.ts", "../../../node_modules/openai/resources/containers/files/content.d.ts", "../../../node_modules/openai/resources/containers/files/files.d.ts", "../../../node_modules/openai/resources/containers/containers.d.ts", "../../../node_modules/openai/resources/graders/grader-models.d.ts", "../../../node_modules/openai/resources/evals/runs/output-items.d.ts", "../../../node_modules/openai/resources/evals/runs/runs.d.ts", "../../../node_modules/openai/resources/evals/evals.d.ts", "../../../node_modules/openai/resources/fine-tuning/methods.d.ts", "../../../node_modules/openai/resources/fine-tuning/alpha/graders.d.ts", "../../../node_modules/openai/resources/fine-tuning/alpha/alpha.d.ts", "../../../node_modules/openai/resources/fine-tuning/checkpoints/permissions.d.ts", "../../../node_modules/openai/resources/fine-tuning/checkpoints/checkpoints.d.ts", "../../../node_modules/openai/resources/fine-tuning/jobs/checkpoints.d.ts", "../../../node_modules/openai/resources/fine-tuning/jobs/jobs.d.ts", "../../../node_modules/openai/resources/fine-tuning/fine-tuning.d.ts", "../../../node_modules/openai/resources/graders/graders.d.ts", "../../../node_modules/openai/resources/uploads/parts.d.ts", "../../../node_modules/openai/resources/uploads/uploads.d.ts", "../../../node_modules/openai/resources/vector-stores/files.d.ts", "../../../node_modules/openai/resources/vector-stores/file-batches.d.ts", "../../../node_modules/openai/resources/vector-stores/vector-stores.d.ts", "../../../node_modules/openai/index.d.ts", "../../../node_modules/openai/resource.d.ts", "../../../node_modules/openai/resources/chat/chat.d.ts", "../../../node_modules/openai/resources/chat/completions/index.d.ts", "../../../node_modules/openai/resources/chat/index.d.ts", "../../../node_modules/openai/resources/index.d.ts", "../../../node_modules/openai/index.d.mts", "./lib/openrouter_client.ts", "./lib/prompt_templates.ts", "./types/twitter.ts", "./errors/twitter_errors.ts", "./types/optimized.ts", "./lib/advancedcaching.ts", "./lib/airesponsecache.ts", "./lib/model_selector.ts", "./lib/ai_fallback_client.ts", "./lib/analysisutils.ts", "./lib/bandwidthmonitor.ts", "./lib/config.ts", "./lib/config_validator.ts", "./lib/debugconfig.ts", "./lib/embeddingutils.ts", "./lib/production_config.ts", "./lib/secure_logger.ts", "./lib/error_handler.ts", "./lib/fal_client.ts", "./lib/input_sanitizer.ts", "./lib/intelligentbatching.ts", "./lib/mentioncache.ts", "./lib/openai_client.ts", "./lib/optimizationconfig.ts", "./lib/projections.ts", "./lib/querylimitenforcer.ts", "./lib/rate_limiter.ts", "./lib/security_headers.ts", "./lib/twitter_api_monitor.ts", "./lib/twitter_rate_limiting.ts", "./lib/twitter_monitoring.ts", "./lib/twitter_utils.ts", "./lib/twitter_client.ts", "./lib/twitter_health_check.ts", "./lib/unified_image_client.ts", "./lib/xai_client.ts", "./types/auth.ts", "./types/convex.ts", "./_generated/api.d.ts", "./ai/aiagent.ts", "./ai/batchsentimentprocessing.ts", "./ai/ensembleorchestrator.ts", "./ai/sentimentanalysis.ts", "./ai/tweetanalysis.ts", "./ai/viraldetection.ts", "./ai/simplecontextimprovement.ts", "./ai/responsegeneration.ts", "./ai/unifiedimagegeneration.ts", "./ai/xailivesearch.ts", "./ai/testnewmodels.ts", "./ai/index.ts", "./mentions/queries.ts", "./mentions/mutations.ts", "./mentions/optimizedqueries.ts", "./mentions/index.ts", "./responses/queries.ts", "./responses/mutations.ts", "./responses/index.ts", "../../../node_modules/@babel/types/lib/index.d.ts", "../../../node_modules/@types/babel__generator/index.d.ts", "../../../node_modules/@babel/parser/typings/babel-parser.d.ts", "../../../node_modules/@types/babel__template/index.d.ts", "../../../node_modules/@types/babel__traverse/index.d.ts", "../../../node_modules/@types/babel__core/index.d.ts", "../../../node_modules/base-x/src/index.d.ts", "../../../node_modules/bs58/index.d.ts", "../../../node_modules/@types/deep-eql/index.d.ts", "../../../node_modules/@types/chai/index.d.ts", "../../../node_modules/@types/cookie/index.d.ts", "../../../node_modules/@types/diff-match-patch/index.d.ts", "../../../node_modules/@types/estree/index.d.ts", "../../../node_modules/@types/json-schema/index.d.ts", "../../../node_modules/@types/node/compatibility/disposable.d.ts", "../../../node_modules/@types/node/compatibility/indexable.d.ts", "../../../node_modules/@types/node/compatibility/iterators.d.ts", "../../../node_modules/@types/node/compatibility/index.d.ts", "../../../node_modules/@types/node/globals.typedarray.d.ts", "../../../node_modules/@types/node/buffer.buffer.d.ts", "../../../node_modules/buffer/index.d.ts", "../../../node_modules/undici-types/header.d.ts", "../../../node_modules/undici-types/readable.d.ts", "../../../node_modules/undici-types/file.d.ts", "../../../node_modules/undici-types/fetch.d.ts", "../../../node_modules/undici-types/formdata.d.ts", "../../../node_modules/undici-types/connector.d.ts", "../../../node_modules/undici-types/client.d.ts", "../../../node_modules/undici-types/errors.d.ts", "../../../node_modules/undici-types/dispatcher.d.ts", "../../../node_modules/undici-types/global-dispatcher.d.ts", "../../../node_modules/undici-types/global-origin.d.ts", "../../../node_modules/undici-types/pool-stats.d.ts", "../../../node_modules/undici-types/pool.d.ts", "../../../node_modules/undici-types/handlers.d.ts", "../../../node_modules/undici-types/balanced-pool.d.ts", "../../../node_modules/undici-types/agent.d.ts", "../../../node_modules/undici-types/mock-interceptor.d.ts", "../../../node_modules/undici-types/mock-agent.d.ts", "../../../node_modules/undici-types/mock-client.d.ts", "../../../node_modules/undici-types/mock-pool.d.ts", "../../../node_modules/undici-types/mock-errors.d.ts", "../../../node_modules/undici-types/proxy-agent.d.ts", "../../../node_modules/undici-types/env-http-proxy-agent.d.ts", "../../../node_modules/undici-types/retry-handler.d.ts", "../../../node_modules/undici-types/retry-agent.d.ts", "../../../node_modules/undici-types/api.d.ts", "../../../node_modules/undici-types/interceptors.d.ts", "../../../node_modules/undici-types/util.d.ts", "../../../node_modules/undici-types/cookies.d.ts", "../../../node_modules/undici-types/patch.d.ts", "../../../node_modules/undici-types/websocket.d.ts", "../../../node_modules/undici-types/eventsource.d.ts", "../../../node_modules/undici-types/filereader.d.ts", "../../../node_modules/undici-types/diagnostics-channel.d.ts", "../../../node_modules/undici-types/content-type.d.ts", "../../../node_modules/undici-types/cache.d.ts", "../../../node_modules/undici-types/index.d.ts", "../../../node_modules/@types/node/globals.d.ts", "../../../node_modules/@types/node/assert.d.ts", "../../../node_modules/@types/node/assert/strict.d.ts", "../../../node_modules/@types/node/async_hooks.d.ts", "../../../node_modules/@types/node/buffer.d.ts", "../../../node_modules/@types/node/child_process.d.ts", "../../../node_modules/@types/node/cluster.d.ts", "../../../node_modules/@types/node/console.d.ts", "../../../node_modules/@types/node/constants.d.ts", "../../../node_modules/@types/node/crypto.d.ts", "../../../node_modules/@types/node/dgram.d.ts", "../../../node_modules/@types/node/diagnostics_channel.d.ts", "../../../node_modules/@types/node/dns.d.ts", "../../../node_modules/@types/node/dns/promises.d.ts", "../../../node_modules/@types/node/domain.d.ts", "../../../node_modules/@types/node/dom-events.d.ts", "../../../node_modules/@types/node/events.d.ts", "../../../node_modules/@types/node/fs.d.ts", "../../../node_modules/@types/node/fs/promises.d.ts", "../../../node_modules/@types/node/http.d.ts", "../../../node_modules/@types/node/http2.d.ts", "../../../node_modules/@types/node/https.d.ts", "../../../node_modules/@types/node/inspector.d.ts", "../../../node_modules/@types/node/module.d.ts", "../../../node_modules/@types/node/net.d.ts", "../../../node_modules/@types/node/os.d.ts", "../../../node_modules/@types/node/path.d.ts", "../../../node_modules/@types/node/perf_hooks.d.ts", "../../../node_modules/@types/node/process.d.ts", "../../../node_modules/@types/node/punycode.d.ts", "../../../node_modules/@types/node/querystring.d.ts", "../../../node_modules/@types/node/readline.d.ts", "../../../node_modules/@types/node/readline/promises.d.ts", "../../../node_modules/@types/node/repl.d.ts", "../../../node_modules/@types/node/sea.d.ts", "../../../node_modules/@types/node/sqlite.d.ts", "../../../node_modules/@types/node/stream.d.ts", "../../../node_modules/@types/node/stream/promises.d.ts", "../../../node_modules/@types/node/stream/consumers.d.ts", "../../../node_modules/@types/node/stream/web.d.ts", "../../../node_modules/@types/node/string_decoder.d.ts", "../../../node_modules/@types/node/test.d.ts", "../../../node_modules/@types/node/timers.d.ts", "../../../node_modules/@types/node/timers/promises.d.ts", "../../../node_modules/@types/node/tls.d.ts", "../../../node_modules/@types/node/trace_events.d.ts", "../../../node_modules/@types/node/tty.d.ts", "../../../node_modules/@types/node/url.d.ts", "../../../node_modules/@types/node/util.d.ts", "../../../node_modules/@types/node/v8.d.ts", "../../../node_modules/@types/node/vm.d.ts", "../../../node_modules/@types/node/wasi.d.ts", "../../../node_modules/@types/node/worker_threads.d.ts", "../../../node_modules/@types/node/zlib.d.ts", "../../../node_modules/@types/node/index.d.ts", "../../../node_modules/form-data/index.d.ts", "../../../node_modules/@types/node-fetch/externals.d.ts", "../../../node_modules/@types/node-fetch/index.d.ts", "../../../node_modules/@types/react-dom/index.d.ts"], "fileIdsList": [[226, 245, 288], [245, 288], [226, 227, 228, 229, 230, 245, 288], [226, 228, 245, 288], [234, 245, 288], [245, 288, 303, 331, 338, 339, 340], [245, 285, 288], [245, 287, 288], [288], [245, 288, 293, 323], [245, 288, 289, 294, 300, 301, 308, 320, 331], [245, 288, 289, 290, 300, 308], [240, 241, 242, 245, 288], [245, 288, 291, 332], [245, 288, 292, 293, 301, 309], [245, 288, 293, 320, 328], [245, 288, 294, 296, 300, 308], [245, 287, 288, 295], [245, 288, 296, 297], [245, 288, 298, 300], [245, 287, 288, 300], [245, 288, 300, 301, 302, 320, 331], [245, 288, 300, 301, 302, 315, 320, 323], [245, 283, 288], [245, 283, 288, 296, 300, 303, 308, 320, 331], [245, 288, 300, 301, 303, 304, 308, 320, 328, 331], [245, 288, 303, 305, 320, 328, 331], [243, 244, 245, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337], [245, 288, 300, 306], [245, 288, 307, 331], [245, 288, 296, 300, 308, 320], [245, 288, 309], [245, 288, 310], [245, 287, 288, 311], [245, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337], [245, 288, 313], [245, 288, 314], [245, 288, 300, 315, 316], [245, 288, 315, 317, 332, 334], [245, 288, 300, 320, 321, 323], [245, 288, 322, 323], [245, 288, 320, 321], [245, 288, 323], [245, 288, 324], [245, 285, 288, 320], [245, 288, 300, 326, 327], [245, 288, 326, 327], [245, 288, 293, 308, 320, 328], [245, 288, 329], [245, 288, 308, 330], [245, 288, 303, 314, 331], [245, 288, 293, 332], [245, 288, 320, 333], [245, 288, 307, 334], [245, 288, 335], [245, 288, 300, 302, 311, 320, 323, 331, 334, 336], [245, 288, 320, 337], [54, 245, 288], [52, 53, 245, 288], [232, 245, 288], [57, 68, 77, 245, 288], [63, 245, 288], [74, 77, 82, 245, 288], [63, 74, 75, 245, 288], [63, 65, 70, 71, 72, 245, 288], [63, 65, 245, 288], [65, 77, 245, 288], [57, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 83, 245, 288], [65, 245, 288], [58, 245, 288], [65, 66, 67, 68, 69, 245, 288], [56, 57, 58, 59, 65, 74, 75, 76, 84, 245, 288], [77, 245, 288], [56, 74, 245, 288], [57, 58, 59, 65, 71, 245, 288], [57, 63, 65, 245, 288], [56, 65, 245, 288], [56, 245, 288], [56, 58, 59, 60, 61, 62, 245, 288], [57, 58, 63, 245, 288], [56, 59, 63, 245, 288], [245, 288, 303, 320, 338], [91, 92, 97, 245, 288], [93, 94, 96, 98, 245, 288], [97, 245, 288], [94, 96, 97, 98, 99, 101, 103, 104, 105, 106, 107, 108, 109, 113, 128, 139, 142, 146, 154, 155, 157, 160, 163, 166, 245, 288], [97, 104, 117, 121, 130, 132, 133, 134, 161, 245, 288], [97, 98, 114, 115, 116, 117, 119, 120, 245, 288], [121, 122, 129, 132, 161, 245, 288], [97, 98, 103, 122, 134, 161, 245, 288], [98, 121, 122, 123, 129, 132, 161, 245, 288], [94, 245, 288], [100, 121, 128, 134, 245, 288], [128, 245, 288], [97, 117, 124, 126, 128, 161, 245, 288], [121, 128, 129, 245, 288], [130, 131, 133, 245, 288], [161, 245, 288], [110, 111, 112, 162, 245, 288], [97, 98, 162, 245, 288], [93, 97, 111, 113, 162, 245, 288], [97, 111, 113, 162, 245, 288], [97, 99, 100, 101, 162, 245, 288], [97, 99, 100, 114, 115, 116, 118, 119, 162, 245, 288], [119, 120, 135, 138, 162, 245, 288], [134, 162, 245, 288], [97, 121, 122, 123, 129, 130, 132, 133, 162, 245, 288], [100, 136, 137, 138, 162, 245, 288], [97, 162, 245, 288], [97, 99, 100, 120, 162, 245, 288], [93, 97, 99, 100, 114, 115, 116, 118, 119, 120, 162, 245, 288], [97, 99, 100, 115, 162, 245, 288], [93, 97, 100, 114, 116, 118, 119, 120, 162, 245, 288], [100, 103, 162, 245, 288], [103, 245, 288], [93, 97, 99, 100, 102, 103, 104, 162, 245, 288], [102, 103, 245, 288], [97, 99, 103, 162, 245, 288], [163, 164, 245, 288], [93, 97, 103, 104, 162, 245, 288], [97, 99, 141, 162, 245, 288], [97, 99, 140, 162, 245, 288], [97, 99, 100, 128, 143, 145, 162, 245, 288], [97, 99, 145, 162, 245, 288], [97, 99, 100, 128, 144, 162, 245, 288], [97, 98, 99, 162, 245, 288], [148, 162, 245, 288], [97, 143, 162, 245, 288], [150, 162, 245, 288], [97, 99, 162, 245, 288], [147, 149, 151, 153, 162, 245, 288], [97, 99, 147, 152, 162, 245, 288], [143, 162, 245, 288], [128, 162, 245, 288], [100, 101, 104, 105, 106, 107, 108, 109, 113, 128, 139, 142, 146, 154, 155, 157, 160, 165, 245, 288], [97, 99, 128, 162, 245, 288], [93, 97, 99, 100, 124, 125, 127, 128, 162, 245, 288], [97, 106, 156, 162, 245, 288], [97, 99, 158, 160, 162, 245, 288], [97, 99, 160, 162, 245, 288], [97, 99, 100, 158, 159, 162, 245, 288], [98, 245, 288], [95, 97, 98, 245, 288], [245, 255, 259, 288, 331], [245, 255, 288, 320, 331], [245, 250, 288], [245, 252, 255, 288, 328, 331], [245, 288, 308, 328], [245, 288, 338], [245, 250, 288, 338], [245, 252, 255, 288, 308, 331], [245, 247, 248, 251, 254, 288, 300, 320, 331], [245, 255, 262, 288], [245, 247, 253, 288], [245, 255, 276, 277, 288], [245, 251, 255, 288, 323, 331, 338], [245, 276, 288, 338], [245, 249, 250, 288, 338], [245, 255, 288], [245, 249, 250, 251, 252, 253, 254, 255, 256, 257, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 277, 278, 279, 280, 281, 282, 288], [245, 255, 270, 288], [245, 255, 262, 263, 288], [245, 253, 255, 263, 264, 288], [245, 254, 288], [245, 247, 250, 255, 288], [245, 255, 259, 263, 264, 288], [245, 259, 288], [245, 253, 255, 258, 288, 331], [245, 247, 252, 255, 262, 288], [245, 288, 320], [245, 250, 255, 276, 288, 336, 338], [84, 88, 89, 90, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 245, 288], [63, 84, 85, 245, 288], [84, 86, 245, 288], [55, 63, 87, 168, 169, 177, 182, 206, 245, 288], [55, 63, 87, 206, 245, 288], [55, 63, 86, 87, 168, 206, 245, 288], [55, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 245, 288], [55, 63, 87, 168, 206, 245, 288], [55, 63, 87, 168, 174, 194, 206, 245, 288], [55, 63, 87, 168, 245, 288], [55, 63, 87, 168, 176, 202, 206, 245, 288], [55, 63, 87, 245, 288], [55, 63, 87, 168, 194, 206, 245, 288], [55, 63, 87, 194, 203, 206, 245, 288], [55, 170, 245, 288], [55, 84, 87, 245, 288], [55, 84, 172, 245, 288], [55, 168, 175, 245, 288], [55, 63, 86, 87, 245, 288], [55, 245, 288], [55, 87, 179, 245, 288], [55, 183, 184, 245, 288], [55, 84, 245, 288], [55, 167, 245, 288], [55, 86, 172, 245, 288], [55, 183, 245, 288], [55, 63, 87, 179, 245, 288], [55, 170, 171, 197, 198, 199, 245, 288], [55, 63, 87, 179, 200, 206, 245, 288], [55, 175, 186, 190, 245, 288], [55, 219, 220, 221, 245, 288], [55, 63, 86, 87, 169, 170, 176, 179, 199, 200, 206, 245, 288], [55, 63, 86, 87, 172, 173, 178, 192, 245, 288], [55, 63, 86, 87, 172, 192, 206, 245, 288], [55, 223, 224, 245, 288], [55, 63, 86, 87, 206, 245, 288], [55, 63, 84, 245, 288], [55, 86, 245, 288], [55, 63, 86, 245, 288]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "170d4db14678c68178ee8a3d5a990d5afb759ecb6ec44dbd885c50f6da6204f6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "d4d7d3f832882a4b2d611a7eaaa80c780c3342b5732090130fa9af4a40bd051e", "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "8567c4f44c0d1c40726745701a7bbd715c0e8301b6b15bc25b208cec0317bd3d", "impliedFormat": 99}, {"version": "56c7652b9e41b2acf8fc249f13bbf293f2fd5d20a6826a779fb13f2b41310285", "impliedFormat": 99}, {"version": "d619113674b97169b14dd63cec0cd38ca586550be0b898342d84860c6966e016", "impliedFormat": 99}, {"version": "bfc119214b3543fbaabe2c6e1d5c1daa9c0186d4f7fc3a87d72975d2600ea0c1", "impliedFormat": 99}, {"version": "f37104775d567bf587acc198edd4baa7222f79810463d469375c8ef0d292a157", "impliedFormat": 99}, {"version": "c5ee44dca52898ad7262cadc354f5e6f434a007c2d904a53ecfb4ee0e419b403", "impliedFormat": 99}, {"version": "cb44dd6fd99ade30c70496a3fa535590aed5f2bb64ba7bc92aa34156c10c0f25", "impliedFormat": 99}, {"version": "d52cc473d0d96c4d8a8e9768846f8a38d24b053750b1a1d1c01f9d8112fe05c7", "impliedFormat": 99}, {"version": "b7867a291de5014acdf689168c09a286fe698951b09485da6ca28c7dee2632b5", "impliedFormat": 99}, {"version": "2ad00018e95065d0b14bbd4dcc4ececec08d104860651668452f5c6305692b41", "impliedFormat": 99}, {"version": "c4dd27a0c3897b8f1b7082f70d70f38231f0e0973813680c8ca08ddf0e7d16c1", "impliedFormat": 99}, {"version": "b23fad2190be146426a7de0fa403e24fccbc9c985d49d22f8b9f39803db47699", "impliedFormat": 99}, {"version": "2b972d3d61798fcef479dfc84ad519c805fcf4cdc7a5a270b698975371872614", "impliedFormat": 99}, {"version": "895d89df016d846222abdd633b1f6e3a7f4c820f56901dbda853916d302c16f2", "impliedFormat": 99}, {"version": "fe05dff4d835a34d8b61468deeb948abf13e77378cb2ec24607f132f2a4065f4", "impliedFormat": 99}, {"version": "ab59a5f7526fc8309ee5a5a28e3e358f6ed457bdb599dd6542becb706c0419dc", "impliedFormat": 99}, {"version": "2f5e26625dab50706134c929d653f6756ec1c90155a5abc2a5a6f7dbdbc42d5b", "impliedFormat": 99}, {"version": "76c33b84606e8124aa33a2ace448ae9b035d1ad59de61e447bba7b94750f8854", "impliedFormat": 99}, {"version": "64a8c0db1ac49d639d35064e7f20360b8ebb2f64266136adf94a604d698b4ff7", "impliedFormat": 99}, {"version": "0a2602130be5a581a921d84f465ce0f81e62c961b4d2ffe10e9bcd4060dd41cf", "impliedFormat": 99}, {"version": "7c1c1d4c8fe888eecca43aa8d1bb12811c4915ffd27718b939c9bb127f2225bf", "impliedFormat": 99}, {"version": "0d4079e5d31dee0ea3f724aad8ff19a01e248d5e4d234ee81dfe561731b484d9", "impliedFormat": 99}, {"version": "886e27d585b99cea11db1f8ec5504e7d3da92f48fc819db0e8fc1b615a47f9b5", "impliedFormat": 99}, {"version": "5c4621a72b5994b6c8d84ca2dc6592ab7288c70a72e86df68b89187f801ebfa7", "impliedFormat": 99}, {"version": "9f2a41d65629c9d3218d3451b5b73dd96956f9078720e5ea2acf469ea6895240", "impliedFormat": 99}, {"version": "2d1924bb4fa9f785437228ca40cd05162795b36295b9addaed7aaef2e8e5c7e5", "impliedFormat": 99}, {"version": "47634f6761f27d52983664d8f1367085d8885d3def57642ae7b490f0c4e4833a", "impliedFormat": 99}, {"version": "34c57354a2a1b8e654bc730ab55aeeb857ee342ebe848660a078803e0bbd940a", "impliedFormat": 99}, {"version": "675e46f900b0941dc2657a49ccb533c1dac12aa296fe1ac0c36285b7bf3d7b20", "impliedFormat": 99}, "eccd58b9d00aea403c390d4b7b901c6ffd4f8fd38f71ae71998904fc4495dfe7", "d70591b280b4c77c0b82243785056025463e4d4b115412deb71dc443114b4d99", "1f03c8b25070f951c41b3a276a968c4fcb9c5e1da6cf33541d59a7b7867ef2fd", "c31944bf0e8deb3225ebfec39c198cbfb1863e73f7ad053ecd283bb8a0c9318a", "7625aea2d7d973405f1b3736c9308c191cc1fb9d8170a91fa9c632ac94dab01b", "15267c70456797f2af15fd281ba382ff44737565b4b8797d072cb5ab19540d76", {"version": "b1535397a73ca6046ca08957788a4c9a745730c7b2b887e9b9bc784214f3abac", "impliedFormat": 1}, {"version": "1dab12d45a7ab2b167b489150cc7d10043d97eadc4255bfee8d9e07697073c61", "impliedFormat": 1}, {"version": "611c4448eee5289fb486356d96a8049ce8e10e58885608b1d218ab6000c489b3", "impliedFormat": 1}, {"version": "5de017dece7444a2041f5f729fe5035c3e8a94065910fbd235949a25c0c5b035", "impliedFormat": 1}, {"version": "d47961927fe421b16a444286485165f10f18c2ef7b2b32a599c6f22106cd223b", "impliedFormat": 1}, {"version": "341672ca9475e1625c105a6a99f46e8b4f14dff977e53a828deef7b5e932638f", "impliedFormat": 1}, {"version": "d3b5d359e0523d0b9f85016266c9a50ce9cda399aeac1b9eeecb63ba577e4d27", "impliedFormat": 1}, {"version": "5b9f65234e953177fcc9088e69d363706ccd0696a15d254ac5787b28bdfb7cb0", "impliedFormat": 1}, {"version": "510a5373df4110d355b3fb5c72dfd3906782aeacbb44de71ceee0f0dece36352", "impliedFormat": 1}, {"version": "eb76f85d8a8893360da026a53b39152237aaa7f033a267009b8e590139afd7de", "impliedFormat": 1}, {"version": "1c19f268e0f1ed1a6485ca80e0cfd4e21bdc71cb974e2ac7b04b5fce0a91482b", "impliedFormat": 1}, {"version": "84a28d684e49bae482c89c996e8aeaabf44c0355237a3a1303749da2161a90c1", "impliedFormat": 1}, {"version": "89c36d61bae1591a26b3c08db2af6fdd43ffaab0f96646dead5af39ff0cf44d3", "impliedFormat": 1}, {"version": "fcd615891bdf6421c708b42a6006ed8b0cf50ca0ac2b37d66a5777d8222893ce", "impliedFormat": 1}, {"version": "1c87dfe5efcac5c2cd5fc454fe5df66116d7dc284b6e7b70bd30c07375176b36", "impliedFormat": 1}, {"version": "6362fcd24c5b52eb88e9cf33876abd9b066d520fc9d4c24173e58dcddcfe12d5", "impliedFormat": 1}, {"version": "aa064f60b7e64c04a759f5806a0d82a954452300ee27566232b0cf5dad5b6ba6", "impliedFormat": 1}, {"version": "7ffb4e58ca1b9ed5f26bed3dc0287c4abd7a2ba301ca55e2546d01a7f7f73de7", "impliedFormat": 1}, {"version": "65a6307cc74644b8813e553b468ea7cc7a1e5c4b241db255098b35f308bfc4b5", "impliedFormat": 1}, {"version": "bd8e8f02d1b0ebfa518f7d8b5f0db06ae260c192e211a1ef86397f4b49ee198f", "impliedFormat": 1}, {"version": "71b32ccf8c508c2f7445b1b2c144dd7eef9434f7bfa6a92a9ebd0253a75cb54a", "impliedFormat": 1}, {"version": "4fd8e7e446c8379cfb1f165961b1d2f984b40d73f5ad343d93e33962292ec2e0", "impliedFormat": 1}, {"version": "45079ac211d6cfda93dd7d0e7fc1cf2e510dad5610048ef71e47328b765515be", "impliedFormat": 1}, {"version": "7ae8f8b4f56ba486dc9561d873aae5b3ad263ffb9683c8f9ffc18d25a7fd09a4", "impliedFormat": 1}, {"version": "e0ab56e00ef473df66b345c9d64e42823c03e84d9a679020746d23710c2f9fce", "impliedFormat": 1}, {"version": "d99deead63d250c60b647620d1ddaf497779aef1084f85d3d0a353cbc4ea8a60", "impliedFormat": 1}, {"version": "ba64b14db9d08613474dc7c06d8ffbcb22a00a4f9d2641b2dcf97bc91da14275", "impliedFormat": 1}, {"version": "530197974beb0a02c5a9eb7223f03e27651422345c8c35e1a13ddc67e6365af5", "impliedFormat": 1}, {"version": "512c43b21074254148f89bd80ae00f7126db68b4d0bd1583b77b9c8af91cc0d3", "impliedFormat": 1}, {"version": "0bfacd36c923f059779049c6c74c00823c56386397a541fefc8d8672d26e0c42", "impliedFormat": 1}, {"version": "19d04b82ed0dc5ba742521b6da97f22362fe40d6efa5ca5650f08381e5c939b2", "impliedFormat": 1}, {"version": "f02ac71075b54b5c0a384dddbd773c9852dba14b4bf61ca9f1c8ba6b09101d3e", "impliedFormat": 1}, {"version": "bbf0ae18efd0b886897a23141532d9695435c279921c24bcb86090f2466d0727", "impliedFormat": 1}, {"version": "067670de65606b4aa07964b0269b788a7fe48026864326cd3ab5db9fc5e93120", "impliedFormat": 1}, {"version": "7a094146e95764e687120cdb840d7e92fe9960c2168d697639ad51af7230ef5e", "impliedFormat": 1}, {"version": "21290aaea56895f836a0f1da5e1ef89285f8c0e85dc85fd59e2b887255484a6f", "impliedFormat": 1}, {"version": "a07254fded28555a750750f3016aa44ec8b41fbf3664b380829ed8948124bafe", "impliedFormat": 1}, {"version": "f14fbd9ec19692009e5f2727a662f841bbe65ac098e3371eb9a4d9e6ac05bca7", "impliedFormat": 1}, {"version": "46f640a5efe8e5d464ced887797e7855c60581c27575971493998f253931b9a3", "impliedFormat": 1}, {"version": "cdf62cebf884c6fde74f733d7993b7e255e513d6bc1d0e76c5c745ac8df98453", "impliedFormat": 1}, {"version": "e6dd8526d318cce4cb3e83bef3cb4bf3aa08186ddc984c4663cf7dee221d430e", "impliedFormat": 1}, {"version": "bc79e5e54981d32d02e32014b0279f1577055b2ebee12f4d2dc6451efd823a19", "impliedFormat": 1}, {"version": "ce9f76eceb4f35c5ecd9bf7a1a22774c8b4962c2c52e5d56a8d3581a07b392f9", "impliedFormat": 1}, {"version": "7d390f34038ca66aef27575cffb5a25a1034df470a8f7789a9079397a359bf8b", "impliedFormat": 1}, {"version": "18084f07f6e85e59ce11b7118163dff2e452694fffb167d9973617699405fbd1", "impliedFormat": 1}, {"version": "6af607dd78a033679e46c1c69c126313a1485069bdec46036f0fbfe64e393979", "impliedFormat": 1}, {"version": "44c556b0d0ede234f633da4fb95df7d6e9780007003e108e88b4969541373db1", "impliedFormat": 1}, {"version": "ef1491fb98f7a8837af94bfff14351b28485d8b8f490987820695cedac76dc99", "impliedFormat": 1}, {"version": "0d4ba4ad7632e46bab669c1261452a1b35b58c3b1f6a64fb456440488f9008cf", "impliedFormat": 1}, {"version": "74a0fa488591d372a544454d6cd93bbadd09c26474595ea8afed7125692e0859", "impliedFormat": 1}, {"version": "0a9ae72be840cc5be5b0af985997029c74e3f5bcd4237b0055096bb01241d723", "impliedFormat": 1}, {"version": "920004608418d82d0aad39134e275a427255aaf1dafe44dca10cc432ef5ca72a", "impliedFormat": 1}, {"version": "3ac2bd86af2bab352d126ccdde1381cd4db82e3d09a887391c5c1254790727a1", "impliedFormat": 1}, {"version": "2efc9ad74a84d3af0e00c12769a1032b2c349430d49aadebdf710f57857c9647", "impliedFormat": 1}, {"version": "f18cc4e4728203a0282b94fc542523dfd78967a8f160fabc920faa120688151f", "impliedFormat": 1}, {"version": "cc609a30a3dd07d6074290dadfb49b9f0f2c09d0ae7f2fa6b41e2dae2432417b", "impliedFormat": 1}, {"version": "c473f6bd005279b9f3a08c38986f1f0eaf1b0f9d094fec6bc66309e7504b6460", "impliedFormat": 1}, {"version": "0043ff78e9f07cbbbb934dd80d0f5fe190437715446ec9550d1f97b74ec951ac", "impliedFormat": 1}, {"version": "bdc013746db3189a2525e87e2da9a6681f78352ef25ae513aa5f9a75f541e0ae", "impliedFormat": 1}, {"version": "4f567b8360c2be77e609f98efc15de3ffcdbe2a806f34a3eba1ee607c04abab6", "impliedFormat": 1}, {"version": "615bf0ac5606a0e79312d70d4b978ac4a39b3add886b555b1b1a35472327034e", "impliedFormat": 1}, {"version": "818e96d8e24d98dfd8fd6d9d1bbabcac082bcf5fbbe64ca2a32d006209a8ee54", "impliedFormat": 1}, {"version": "18b0b9a38fe92aa95a40431676b2102139c5257e5635fe6a48b197e9dcb660f1", "impliedFormat": 1}, {"version": "86b382f98cb678ff23a74fe1d940cbbf67bcd3162259e8924590ecf8ee24701e", "impliedFormat": 1}, {"version": "aeea2c497f27ce34df29448cbe66adb0f07d3a5d210c24943d38b8026ffa6d3c", "impliedFormat": 1}, {"version": "0fbe1a754e3da007cc2726f61bc8f89b34b466fe205b20c1e316eb240bebe9e8", "impliedFormat": 1}, {"version": "aa2f3c289c7a3403633e411985025b79af473c0bf0fdd980b9712bd6a1705d59", "impliedFormat": 1}, {"version": "e140d9fa025dadc4b098c54278271a032d170d09f85f16f372e4879765277af8", "impliedFormat": 1}, {"version": "70d9e5189fd4dabc81b82cf7691d80e0abf55df5030cc7f12d57df62c72b5076", "impliedFormat": 1}, {"version": "a96be3ed573c2a6d4c7d4e7540f1738a6e90c92f05f684f5ee2533929dd8c6b2", "impliedFormat": 1}, {"version": "2a545aa0bc738bd0080a931ccf8d1d9486c75cbc93e154597d93f46d2f3be3b4", "impliedFormat": 1}, {"version": "137272a656222e83280287c3b6b6d949d38e6c125b48aff9e987cf584ff8eb42", "impliedFormat": 1}, {"version": "5277b2beeb856b348af1c23ffdaccde1ec447abede6f017a0ab0362613309587", "impliedFormat": 1}, {"version": "d4b6804b4c4cb3d65efd5dc8a672825cea7b39db98363d2d9c2608078adce5f8", "impliedFormat": 1}, {"version": "929f67e0e7f3b3a3bcd4e17074e2e60c94b1e27a8135472a7d002a36cd640629", "impliedFormat": 1}, {"version": "0c73536b65135298d43d1ef51dd81a6eba3b69ef0ce005db3de11365fda30a55", "impliedFormat": 1}, {"version": "2a545aa0bc738bd0080a931ccf8d1d9486c75cbc93e154597d93f46d2f3be3b4", "impliedFormat": 99}, "60c5403423c42252ee555a56f29e5c8fedf6705b264157813a146e8a795d1f44", "1900d1cca90003b303409519a92af8817a2aa78fd08ba82e846756b09a951868", "de6f351a51059db71bdb6d15fddfd7dda9617b0702b3b6d20c75331fd63b2107", "7ccbfbbfcc50474122dd2545a0754780ae23d30c00dd0ff072daa22823a2f903", "7269bab9b13d5f0258d5c0667a825ff4991ccd610a792b4af3ccc007316f9a4f", "ab959aaa0820cc9284e2fc47040b0d4eb5956c1fbc24dea5b579fafda21cf46a", "c3992ad9c006abc01baa9f65fef46d33bddcfa3c1fa6a35a45b7f02bfa8e15fe", "ba12fb3c9bb23f94ccdb4ce337dce1a131382d286564d585d921fb4a8d84e9d9", "870de7fbe71a3538a22559605d7ea9a287b89bdf425756b966a92d00305c9dab", "2039fac3b10ec865580ce954f7ab58c32d9fa839fdf9e2fd0481a9b4a58147a8", "ec667477a46e76dd7ce1ef100b2222eee227b009cce164f0e49413b66f213c35", "475914f46f712eb0c045fc683b931c0fa50f2ec1af20b84eae1aa845be04d7ff", "8ee4b7a9353063c77808ab52c647f2017d8c511ba03738d63bd4411ae409b3ff", "9aeba30b9fecd5467c94a9aa721c2d5378b180912ae4589e45b1b3ccc59ad4dc", "7b0f79751c94287eefc951a5fd11d1598e9eea4bf3cb7f4bdd2276cf5decd5eb", "0fec7599fad1c0000560e47985ff0e38863ea576fde3dbf9f266d13532537215", "59636cefa5b52ab1e67ab29329aaa6381f8168cddd76b81b4d6e8c4da755a608", "8a8d201995af3a9ad13505c52a682d940fa6990ca7d982737db666c9a438a265", "57f137f67b6d4e9c0c9be1512d9d3fbe688993b9d13821200081f6f3974f20d4", "86d93a3059aac9461801602c21e119104cb576772b5a24d5db15f850ca54e81b", "d8ae848985b95b9ed05c6a755932b528b1d9131b828f77aa508ed2e111532faa", "046d95de756bb8bb0aa01336fa3a503953f9f0183d0f8334404e96788ae6bb90", "d732cd857912e851c2ee8ae4c66e96e36585ea34d92cca2127f6d843ec64cb35", "5aa7bb6562cbfd854e3d257476407dc89ccb02fda33a880e3448c16cb702dd36", "44b251c9d4c863097969329bedd8e3cb304e07993678b58a5a8d131f34f06ff9", "c3929a9aa59f70e62c4eea35062e27b330446d62f62e141c20df7f6056c75310", "ee5a960084abc8e8202879331967cd66de62d0a54446d5291b8bff3caecbaedd", "ebbb5112e1d9080c9cebac9c93a123856a3d2abc5f2a726d508644f7b7689d6e", "09bf3c7f8ff3e697c7fc3671e1b49860d4add576b6bf90a0b7fd12356a3916d4", "2902512ae57e2d5a7c9ad31a934d3368853db5a7d0b479bd5abfe2e7306f56a4", "c907682b2c1eb8f7e7fa2c65da933ed58841aef712d117bcf0a16b1c19739b4b", "36a36321c322bdda0464f089b4fee38cb7ba2d4f1a091877a9ba426ddee5002b", "4694c29358d080280fe7c6a80b728aef6592876f2f3e89712361386fd34fe7c2", "fd79729f860b5d2dcbceee1f20e6ed0d5b8f680ffbac1238c3be3141bc40efde", "adee849a6ccd9e9471bcee5334b0ff5b03ce478af30d28205c05e9989e62e465", "20a7711de863f1ed7c3669ce6b6bb1831e14e6ccf62c61b72c90fd1fac180552", "8ebb69a07ed1ed77c0381972e6c46372d052b5a7a7ca0da3883323689eb7e927", "adaca6192a01d7fc190aded1fa5de7b01f40aa0233e5403ed814cc8f81ca4b48", "6fdd1f166c77d86e51f97ef7bd30db4972f808902961e2f4f29a4137879ce222", "0a7269ca8e321c1bf020af9e05b55a18a1d8d5afebc365be0f7228d6d56ff7c0", "6eab578fbff7fc76d8148793dab17f30bce63bb8de189d7f02789e0f6c2fe1eb", "0615e8ba9b1260b9b3e2a9d359eb9cb794f64a89856eb94cb4b911c8d732a0ee", "f975b1a55053367a01b9e1eff260534f7c6c33e8162590ae4b9772c2ffa51550", "5c36d7dcc74b7c9453776a1dc21a71247f4e1a0f7f6971f90dd35def2bbbe354", "e7600c39b7f494e660f5fe7a859ccf9180dbf076861e5305368c1b4066387450", "db5693dc4f995ac78de32aea764a42f24474f510cdb1c5cf0d5e6065ea70d9d4", "04eafb8a594f60e43fb1652dea181b25b37f98630f94e821222c891637885f04", "8d3309f2503c8af8bb6e9b9139fd8723328f2138a2d2d6b5b47d9c02212f938d", "cd04db8465fc1069ccdaad911ba88eb55171c6150479b1e5f59dfd4def01e12e", "e9e0a1d8b39d60ae7867491d7502c60f911c16190950f094463f1338a122c303", "a85434f43a338f243c0516e560430c898987fd3b57e671504c3fbefb826db566", "7b8a8ff4117461b28baf416b2bbc2836ca8986125f8cd01f258b7f5978d95174", "d957d9cada7f9a90ead082f85eae578d01752c27ecf407e4c5227f33e399d07d", "d71dc9995ed453c0a2e6e4ff72b45d233a9173764989f900a1c4feb561f24941", "b19050242c3c3369248cc2a1a175b56ba0e324f04de611472dfca061c9e920bf", "10b88b7fde7b2ca07f709fa2e54c1dce7e30ebc6878656a20f4ad18e58287f07", "4a222144ff6db7d12494113da8550eee84d1d037c32a31c294ce6790a13cb71c", "cff5623ca6095470cefb09cdac9f41c26f9a35bbddbd58734dbebe2086592fad", {"version": "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "impliedFormat": 1}, {"version": "b6d03c9cfe2cf0ba4c673c209fcd7c46c815b2619fd2aad59fc4229aaef2ed43", "impliedFormat": 1}, {"version": "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "impliedFormat": 1}, {"version": "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "impliedFormat": 1}, {"version": "81df92841a7a12d551fcbc7e4e83dbb7d54e0c73f33a82162d13e9ae89700079", "impliedFormat": 1}, {"version": "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "impliedFormat": 1}, {"version": "a3c89483a38d76c77a13b69b268a9e5d13abcdf0957ca3f28c9702c47ce8050f", "impliedFormat": 1}, {"version": "40ec7e761ea0ddfc0add7aedb95dacfc579b25010ed0e2190abc1d34bb67f81e", "impliedFormat": 1}, {"version": "427fe2004642504828c1476d0af4270e6ad4db6de78c0b5da3e4c5ca95052a99", "impliedFormat": 1}, {"version": "c8905dbea83f3220676a669366cd8c1acef56af4d9d72a8b2241b1d044bb4302", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "1748c03e7a7d118f7f6648c709507971eb0d416f489958492c5ae625de445184", "impliedFormat": 1}, {"version": "460627dd2a599c2664d6f9e81ed4765ef520dc2786551d9dcab276df57b98c02", "impliedFormat": 1}, {"version": "151ff381ef9ff8da2da9b9663ebf657eac35c4c9a19183420c05728f31a6761d", "impliedFormat": 1}, {"version": "f3d8c757e148ad968f0d98697987db363070abada5f503da3c06aefd9d4248c1", "impliedFormat": 1}, {"version": "6c7176368037af28cb72f2392010fa1cef295d6d6744bca8cfb54985f3a18c3e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "437e20f2ba32abaeb7985e0afe0002de1917bc74e949ba585e49feba65da6ca1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d802f0e6b5188646d307f070d83512e8eb94651858de8a82d1e47f60fb6da4e2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4967529644e391115ca5592184d4b63980569adf60ee685f968fd59ab1557188", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "a12d953aa755b14ac1d28ecdc1e184f3285b01d6d1e58abc11bf1826bc9d80e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a38efe83ff77c34e0f418a806a01ca3910c02ee7d64212a59d59bca6c2c38fa1", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "3fe4022ba1e738034e38ad9afacbf0f1f16b458ed516326f5bf9e4a31e9be1dc", "impliedFormat": 1}, {"version": "a957197054b074bcdf5555d26286e8461680c7c878040d0f4e2d5509a7524944", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4314c7a11517e221f7296b46547dbc4df047115b182f544d072bdccffa57fc72", "impliedFormat": 1}, {"version": "e9b97d69510658d2f4199b7d384326b7c4053b9e6645f5c19e1c2a54ede427fc", "impliedFormat": 1}, {"version": "c2510f124c0293ab80b1777c44d80f812b75612f297b9857406468c0f4dafe29", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "f478f6f5902dc144c0d6d7bdc919c5177cac4d17a8ca8653c2daf6d7dc94317f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "19d5f8d3930e9f99aa2c36258bf95abbe5adf7e889e6181872d1cdba7c9a7dd5", "impliedFormat": 1}, {"version": "9855e02d837744303391e5623a531734443a5f8e6e8755e018c41d63ad797db2", "impliedFormat": 1}, {"version": "a6bf63d17324010ca1fbf0389cab83f93389bb0b9a01dc8a346d092f65b3605f", "impliedFormat": 1}, {"version": "e009777bef4b023a999b2e5b9a136ff2cde37dc3f77c744a02840f05b18be8ff", "impliedFormat": 1}, {"version": "1e0d1f8b0adfa0b0330e028c7941b5a98c08b600efe7f14d2d2a00854fb2f393", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "88bc59b32d0d5b4e5d9632ac38edea23454057e643684c3c0b94511296f2998c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "25d130083f833251b5b4c2794890831b1b8ce2ead24089f724181576cf9d7279", "impliedFormat": 1}, {"version": "ffe66ee5c9c47017aca2136e95d51235c10e6790753215608bff1e712ff54ec6", "impliedFormat": 1}, {"version": "206a70e72af3e24688397b81304358526ce70d020e4c2606c4acfd1fa1e81fb2", "impliedFormat": 1}, {"version": "017caf5d2a8ef581cf94f678af6ce7415e06956317946315560f1487b9a56167", "impliedFormat": 1}, {"version": "528b62e4272e3ddfb50e8eed9e359dedea0a4d171c3eb8f337f4892aac37b24b", "impliedFormat": 1}, {"version": "d71535813e39c23baa113bc4a29a0e187b87d1105ccc8c5a6ebaca38d9a9bff2", "impliedFormat": 1}, {"version": "4c3148420835de895b9218b2cea321a4607008ba5cefa57b2a57e1c1ef85d22f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f72bc8fe16da67e4e3268599295797b202b95e54bd215a03f97e925dd1502a36", "impliedFormat": 1}, {"version": "b1b6ee0d012aeebe11d776a155d8979730440082797695fc8e2a5c326285678f", "impliedFormat": 1}, {"version": "45875bcae57270aeb3ebc73a5e3fb4c7b9d91d6b045f107c1d8513c28ece71c0", "impliedFormat": 1}, {"version": "915e18c559321c0afaa8d34674d3eb77e1ded12c3e85bf2a9891ec48b07a1ca5", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "636302a00dfd1f9fe6e8e91e4e9350c6518dcc8d51a474e4fc3a9ba07135100b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3f16a7e4deafa527ed9995a772bb380eb7d3c2c0fd4ae178c5263ed18394db2c", "impliedFormat": 1}, {"version": "933921f0bb0ec12ef45d1062a1fc0f27635318f4d294e4d99de9a5493e618ca2", "impliedFormat": 1}, {"version": "71a0f3ad612c123b57239a7749770017ecfe6b66411488000aba83e4546fde25", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "e1120271ebbc9952fdc7b2dd3e145560e52e06956345e6fdf91d70ca4886464f", "impliedFormat": 1}, {"version": "814118df420c4e38fe5ae1b9a3bafb6e9c2aa40838e528cde908381867be6466", "impliedFormat": 1}, {"version": "e1ce1d622f1e561f6cdf246372ead3bbc07ce0342024d0e9c7caf3136f712698", "impliedFormat": 1}, {"version": "199c8269497136f3a0f4da1d1d90ab033f899f070e0dd801946f2a241c8abba2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "27e4532aaaa1665d0dd19023321e4dc12a35a741d6b8e1ca3517fcc2544e0efe", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2754d8221d77c7b382096651925eb476f1066b3348da4b73fe71ced7801edada", "impliedFormat": 1}, {"version": "6266d94fb9165d42716e45f3a537ca9f59c07b1dfa8394a659acf139134807db", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f0be1b8078cd549d91f37c30c222c2a187ac1cf981d994fb476a1adc61387b14", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0aaed1d72199b01234152f7a60046bc947f1f37d78d182e9ae09c4289e06a592", "impliedFormat": 1}, {"version": "98ffdf93dfdd206516971d28e3e473f417a5cfd41172e46b4ce45008f640588e", "impliedFormat": 1}, {"version": "66ba1b2c3e3a3644a1011cd530fb444a96b1b2dfe2f5e837a002d41a1a799e60", "impliedFormat": 1}, {"version": "7e514f5b852fdbc166b539fdd1f4e9114f29911592a5eb10a94bb3a13ccac3c4", "impliedFormat": 1}, {"version": "7d6ff413e198d25639f9f01f16673e7df4e4bd2875a42455afd4ecc02ef156da", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a7692a54334fd08960cd0c610ff509c2caa93998e0dcefa54021489bcc67c22d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74736930d108365d7bbe740c7154706ccfb1b2a3855a897963ab3e5c07ecbf19", "impliedFormat": 1}, {"version": "3a051941721a7f905544732b0eb819c8d88333a96576b13af08b82c4f17581e4", "impliedFormat": 1}, {"version": "ac5ed35e649cdd8143131964336ab9076937fa91802ec760b3ea63b59175c10a", "impliedFormat": 1}, {"version": "1e25f8d0a8573cafd5b5a16af22d26ab872068a693b9dbccd3f72846ab373655", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3797dd6f4ea3dc15f356f8cdd3128bfa18122213b38a80d6c1f05d8e13cbdad8", "impliedFormat": 1}, {"version": "ad90122e1cb599b3bc06a11710eb5489101be678f2920f2322b0ac3e195af78d", "impliedFormat": 1}, {"version": "736097ddbb2903bef918bb3b5811ef1c9c5656f2a73bd39b22a91b9cc2525e50", "impliedFormat": 1}, {"version": "4340936f4e937c452ae783514e7c7bbb7fc06d0c97993ff4865370d0962bb9cf", "impliedFormat": 1}, {"version": "b70c7ea83a7d0de17a791d9b5283f664033a96362c42cc4d2b2e0bdaa65ef7d1", "impliedFormat": 1}, {"version": "a0acca63c9e39580f32a10945df231815f0fe554c074da96ba6564010ffbd2d8", "impliedFormat": 1}], "root": [85, [88, 90], [168, 205], [207, 225]], "options": {"allowJs": true, "allowSyntheticDefaultImports": true, "composite": true, "exactOptionalPropertyTypes": false, "jsx": 4, "module": 99, "noPropertyAccessFromIndexSignature": false, "skipLibCheck": true, "strict": true, "target": 99}, "referencedMap": [[228, 1], [226, 2], [231, 3], [227, 1], [229, 4], [230, 1], [235, 5], [236, 2], [234, 2], [237, 2], [238, 2], [239, 2], [340, 2], [341, 6], [285, 7], [286, 7], [287, 8], [245, 9], [288, 10], [289, 11], [290, 12], [240, 2], [243, 13], [241, 2], [242, 2], [291, 14], [292, 15], [293, 16], [294, 17], [295, 18], [296, 19], [297, 19], [299, 2], [298, 20], [300, 21], [301, 22], [302, 23], [284, 24], [244, 2], [303, 25], [304, 26], [305, 27], [338, 28], [306, 29], [307, 30], [308, 31], [309, 32], [310, 33], [311, 34], [312, 35], [313, 36], [314, 37], [315, 38], [316, 38], [317, 39], [318, 2], [319, 2], [320, 40], [322, 41], [321, 42], [323, 43], [324, 44], [325, 45], [326, 46], [327, 47], [328, 48], [329, 49], [330, 50], [331, 51], [332, 52], [333, 53], [334, 54], [335, 55], [336, 56], [337, 57], [342, 58], [52, 2], [54, 59], [55, 58], [232, 2], [233, 60], [246, 2], [74, 61], [64, 62], [83, 63], [82, 2], [80, 64], [65, 62], [73, 65], [66, 66], [78, 67], [84, 68], [67, 69], [68, 70], [70, 71], [77, 72], [81, 73], [75, 74], [72, 75], [69, 69], [79, 62], [71, 76], [76, 77], [57, 2], [60, 2], [62, 78], [61, 78], [63, 79], [59, 80], [58, 81], [56, 2], [53, 2], [339, 82], [92, 2], [98, 83], [91, 2], [95, 2], [97, 84], [94, 85], [167, 86], [161, 86], [122, 87], [118, 88], [133, 89], [123, 90], [130, 91], [117, 92], [131, 2], [129, 93], [126, 94], [127, 95], [124, 96], [132, 97], [99, 85], [162, 98], [113, 99], [110, 100], [111, 101], [112, 102], [101, 103], [120, 104], [139, 105], [135, 106], [134, 107], [138, 108], [136, 109], [137, 109], [114, 110], [116, 111], [115, 112], [119, 113], [163, 114], [121, 115], [103, 116], [164, 117], [102, 118], [165, 119], [104, 120], [142, 121], [140, 100], [141, 122], [105, 109], [146, 123], [144, 124], [145, 125], [106, 126], [149, 127], [148, 128], [151, 129], [150, 130], [154, 131], [152, 130], [153, 132], [147, 133], [143, 134], [155, 133], [107, 109], [166, 135], [108, 130], [109, 109], [125, 136], [128, 137], [100, 2], [156, 109], [157, 138], [159, 139], [158, 140], [160, 141], [93, 142], [96, 143], [262, 144], [272, 145], [261, 144], [282, 146], [253, 147], [252, 148], [281, 149], [275, 150], [280, 151], [255, 152], [269, 153], [254, 154], [278, 155], [250, 156], [249, 149], [279, 157], [251, 158], [256, 159], [257, 2], [260, 159], [247, 2], [283, 160], [273, 161], [264, 162], [265, 163], [267, 164], [263, 165], [266, 166], [276, 149], [258, 167], [259, 168], [268, 169], [248, 170], [271, 161], [270, 159], [274, 2], [277, 171], [206, 172], [86, 173], [87, 174], [207, 175], [208, 176], [209, 177], [218, 178], [214, 179], [210, 180], [213, 181], [217, 182], [211, 179], [215, 183], [212, 184], [216, 185], [171, 186], [88, 187], [173, 188], [176, 189], [174, 190], [177, 191], [178, 183], [179, 191], [180, 192], [181, 191], [182, 191], [185, 193], [186, 191], [187, 193], [188, 194], [189, 176], [175, 191], [190, 195], [168, 195], [191, 176], [183, 191], [192, 196], [169, 191], [193, 194], [194, 193], [184, 197], [195, 187], [196, 198], [200, 199], [201, 200], [198, 186], [197, 186], [199, 186], [202, 201], [203, 191], [222, 202], [220, 203], [221, 204], [219, 205], [225, 206], [224, 207], [223, 183], [85, 208], [89, 190], [204, 209], [205, 210], [172, 209], [170, 191], [90, 183], [50, 2], [51, 2], [9, 2], [11, 2], [10, 2], [2, 2], [12, 2], [13, 2], [14, 2], [15, 2], [16, 2], [17, 2], [18, 2], [19, 2], [3, 2], [20, 2], [21, 2], [4, 2], [22, 2], [26, 2], [23, 2], [24, 2], [25, 2], [27, 2], [28, 2], [29, 2], [5, 2], [30, 2], [31, 2], [32, 2], [33, 2], [6, 2], [37, 2], [34, 2], [35, 2], [36, 2], [38, 2], [7, 2], [39, 2], [44, 2], [45, 2], [40, 2], [41, 2], [42, 2], [43, 2], [8, 2], [49, 2], [46, 2], [47, 2], [48, 2], [1, 2]], "semanticDiagnosticsPerFile": [[168, [{"start": 3373, "length": 7, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'choices' does not exist on type '(Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }) | (ChatCompletion & { _request_id?: string | null | undefined; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'choices' does not exist on type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.", "category": 1, "code": 2339}]}}, {"start": 3614, "length": 5, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'usage' does not exist on type '(Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }) | (ChatCompletion & { _request_id?: string | null | undefined; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'usage' does not exist on type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.", "category": 1, "code": 2339}]}}, {"start": 3661, "length": 5, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'usage' does not exist on type '(Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }) | (ChatCompletion & { _request_id?: string | null | undefined; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'usage' does not exist on type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.", "category": 1, "code": 2339}]}}, {"start": 3723, "length": 5, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'usage' does not exist on type '(Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }) | (ChatCompletion & { _request_id?: string | null | undefined; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'usage' does not exist on type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.", "category": 1, "code": 2339}]}}, {"start": 3784, "length": 5, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'usage' does not exist on type '(Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }) | (ChatCompletion & { _request_id?: string | null | undefined; })'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'usage' does not exist on type 'Stream<ChatCompletionChunk> & { _request_id?: string | null | undefined; }'.", "category": 1, "code": 2339}]}}]], [169, [{"start": 7493, "length": 15, "messageText": "Subsequent property declarations must have the same type.  Property 'semanticContext' must be of type '{ similarContent?: any[] | undefined; userInterests?: string[] | undefined; } | undefined', but here has type '{ similarContent?: any[] | undefined; userInterests?: string[] | undefined; relevanceScore?: number | undefined; } | undefined'.", "category": 1, "code": 2717, "relatedInformation": [{"start": 794, "length": 15, "messageText": "'semanticContext' was also declared here.", "category": 3, "code": 6203}]}]], [171, [{"start": 1335, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'statusCode' does not exist on type 'never'."}]], [174, [{"start": 4046, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'runMutation' does not exist on type 'GenericQueryCtx<{ wallets: { document: { _id: Id<\"wallets\">; _creationTime: number; lastUsedAt?: number | undefined; metadata?: { ensName?: string | undefined; solanaName?: string | undefined; balance?: string | undefined; lastBalanceUpdate?: number | undefined; publicKey?: string | undefined; } | undefined; ... 7 m...'."}]], [180, [{"start": 4319, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'configLoadSuccess' does not exist on type '{}'."}, {"start": 4377, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'detectedEnvironment' does not exist on type '{}'."}]], [181, [{"start": 7185, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '() => void' is not assignable to type '(label: string) => number'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'void' is not assignable to type 'number'.", "category": 1, "code": 2322, "canonicalHead": {"code": 2322, "messageText": "Type '() => void' is not assignable to type '(label: string) => number'."}}]}}]], [189, [{"start": 11066, "length": 20, "messageText": "'batchCacheOperations' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"start": 11386, "length": 7, "messageText": "'handler' implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.", "category": 1, "code": 7023}, {"start": 11566, "length": 9, "messageText": "'setResult' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"start": 11915, "length": 9, "messageText": "'getResult' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}]], [190, [{"start": 2156, "length": 13, "messageText": "'response.data' is possibly 'undefined'.", "category": 1, "code": 18048}]], [191, [{"start": 4019, "length": 21, "messageText": "'autoTuneOptimizations' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"start": 4180, "length": 7, "messageText": "'handler' implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.", "category": 1, "code": 7023}, {"start": 4391, "length": 13, "messageText": "'currentConfig' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"start": 8318, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8513, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8565, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8570, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8707, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8826, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8902, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 13804, "length": 8, "code": 2740, "category": 1, "messageText": "Type 'OptimizationSettings' is missing the following properties from type '{ maxMentionsPerBatch: number; batchProcessingInterval: number; cacheRetentionDays: number; enableSemanticCaching: boolean; enablePriorityScoring: boolean; maxApiRequestsPerMinute: number; ... 4 more ...; adaptiveQualityControl: boolean; }': maxMentionsPerBatch, batchProcessingInterval, cacheRetentionDays, enableSemanticCaching, and 7 more.", "canonicalHead": {"code": 2322, "messageText": "Type 'OptimizationSettings' is not assignable to type '{ maxMentionsPerBatch: number; batchProcessingInterval: number; cacheRetentionDays: number; enableSemanticCaching: boolean; enablePriorityScoring: boolean; maxApiRequestsPerMinute: number; ... 4 more ...; adaptiveQualityControl: boolean; }'."}}, {"start": 14014, "length": 8, "code": 2740, "category": 1, "messageText": "Type 'OptimizationSettings' is missing the following properties from type '{ maxMentionsPerBatch: number; batchProcessingInterval: number; cacheRetentionDays: number; enableSemanticCaching: boolean; enablePriorityScoring: boolean; maxApiRequestsPerMinute: number; ... 4 more ...; adaptiveQualityControl: boolean; }': maxMentionsPerBatch, batchProcessingInterval, cacheRetentionDays, enableSemanticCaching, and 7 more.", "relatedInformation": [{"file": "../../../node_modules/convex/dist/esm-types/type_utils.d.ts", "start": 373, "length": 51, "messageText": "The expected type comes from property 'settings' which is declared here on type '{ userId?: Id<\"users\"> | undefined; isGlobal?: boolean | undefined; createdAt: number; updatedAt: number; settings: { maxMentionsPerBatch: number; batchProcessingInterval: number; ... 8 more ...; adaptiveQualityControl: boolean; }; }'", "category": 3, "code": 6500}], "canonicalHead": {"code": 2322, "messageText": "Type 'OptimizationSettings' is not assignable to type '{ maxMentionsPerBatch: number; batchProcessingInterval: number; cacheRetentionDays: number; enableSemanticCaching: boolean; enablePriorityScoring: boolean; maxApiRequestsPerMinute: number; ... 4 more ...; adaptiveQualityControl: boolean; }'."}}, {"start": 14298, "length": 21, "messageText": "'getOptimizationReport' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"start": 14452, "length": 7, "messageText": "'handler' implicitly has return type 'any' because it does not have a return type annotation and is referenced directly or indirectly in one of its return expressions.", "category": 1, "code": 7023}, {"start": 14969, "length": 10, "messageText": "'cacheStats' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"start": 15057, "length": 6, "messageText": "'report' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}]], [192, [{"start": 969, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type '{ _id: Id<\"mentions\">; _creationTime: number; processedAt?: number | undefined; embeddingId?: string | undefined; url?: string | undefined; mentionAuthorFollowers?: number | undefined; ... 16 more ...; discoveredAt: number; }'."}, {"start": 1042, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'authorHandle' does not exist on type '{ _id: Id<\"mentions\">; _creationTime: number; processedAt?: number | undefined; embeddingId?: string | undefined; url?: string | undefined; mentionAuthorFollowers?: number | undefined; ... 16 more ...; discoveredAt: number; }'."}, {"start": 1080, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'authorName' does not exist on type '{ _id: Id<\"mentions\">; _creationTime: number; processedAt?: number | undefined; embeddingId?: string | undefined; url?: string | undefined; mentionAuthorFollowers?: number | undefined; ... 16 more ...; discoveredAt: number; }'."}, {"start": 1124, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'authorProfileImage' does not exist on type '{ _id: Id<\"mentions\">; _creationTime: number; processedAt?: number | undefined; embeddingId?: string | undefined; url?: string | undefined; mentionAuthorFollowers?: number | undefined; ... 16 more ...; discoveredAt: number; }'."}, {"start": 1386, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'responseGenerated' does not exist on type '{ _id: Id<\"mentions\">; _creationTime: number; processedAt?: number | undefined; embeddingId?: string | undefined; url?: string | undefined; mentionAuthorFollowers?: number | undefined; ... 16 more ...; discoveredAt: number; }'."}, {"start": 1432, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'responseCount' does not exist on type '{ _id: Id<\"mentions\">; _creationTime: number; processedAt?: number | undefined; embeddingId?: string | undefined; url?: string | undefined; mentionAuthorFollowers?: number | undefined; ... 16 more ...; discoveredAt: number; }'."}, {"start": 3369, "length": 3, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./types/optimized.ts", "start": 2396, "length": 3, "messageText": "The expected type comes from property 'url' which is declared here on type 'LightweightTweet'", "category": 3, "code": 6500}]}, {"start": 3389, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"pending\" | \"analyzed\" | \"response_worthy\" | \"skip\"' is not assignable to type '\"pending\" | \"analyzed\" | \"response_worthy\" | \"analyzing\" | \"not_worthy\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"skip\"' is not assignable to type '\"pending\" | \"analyzed\" | \"response_worthy\" | \"analyzing\" | \"not_worthy\"'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./types/optimized.ts", "start": 2411, "length": 14, "messageText": "The expected type comes from property 'analysisStatus' which is declared here on type 'LightweightTweet'", "category": 3, "code": 6500}]}, {"start": 3832, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"pending\" | \"analyzed\" | \"response_worthy\" | \"skip\"' is not assignable to type '\"pending\" | \"analyzed\" | \"response_worthy\" | \"analyzing\" | \"not_worthy\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"skip\"' is not assignable to type '\"pending\" | \"analyzed\" | \"response_worthy\" | \"analyzing\" | \"not_worthy\"'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./types/optimized.ts", "start": 2833, "length": 14, "messageText": "The expected type comes from property 'analysisStatus' which is declared here on type 'TweetSummary'", "category": 3, "code": 6500}]}, {"start": 5072, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"failed\" | \"draft\" | \"approved\" | \"declined\" | \"posted\"' is not assignable to type '\"pending\" | \"draft\" | \"approved\" | \"posted\" | \"rejected\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"failed\"' is not assignable to type '\"pending\" | \"draft\" | \"approved\" | \"posted\" | \"rejected\"'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./types/optimized.ts", "start": 3508, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'LightweightResponse'", "category": 3, "code": 6500}]}, {"start": 5119, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'aiModel' does not exist on type '{ _id: Id<\"responses\">; _creationTime: number; responseStrategy?: string | undefined; generationModel?: string | undefined; contextUsed?: string[] | undefined; estimatedEngagement?: { ...; } | undefined; ... 17 more ...; characterCount: number; }'."}, {"start": 5239, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'scheduledAt' does not exist on type '{ _id: Id<\"responses\">; _creationTime: number; responseStrategy?: string | undefined; generationModel?: string | undefined; contextUsed?: string[] | undefined; estimatedEngagement?: { ...; } | undefined; ... 17 more ...; characterCount: number; }'."}, {"start": 5689, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"failed\" | \"draft\" | \"approved\" | \"declined\" | \"posted\"' is not assignable to type '\"pending\" | \"draft\" | \"approved\" | \"posted\" | \"rejected\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"failed\"' is not assignable to type '\"pending\" | \"draft\" | \"approved\" | \"posted\" | \"rejected\"'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./types/optimized.ts", "start": 3981, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'ResponseSummary'", "category": 3, "code": 6500}]}]], [196, [{"start": 11747, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'entries' does not exist on type 'Headers'."}, {"start": 12898, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ name: string; status: string; message: string; details: any; }[]' is not assignable to type '{ name: string; status: \"warn\" | \"pass\" | \"fail\"; message: string; details?: any; }[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ name: string; status: string; message: string; details: any; }' is not assignable to type '{ name: string; status: \"warn\" | \"pass\" | \"fail\"; message: string; details?: any; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'status' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string' is not assignable to type '\"warn\" | \"pass\" | \"fail\"'.", "category": 1, "code": 2322, "canonicalHead": {"code": 2322, "messageText": "Type '{ name: string; status: string; message: string; details: any; }' is not assignable to type '{ name: string; status: \"warn\" | \"pass\" | \"fail\"; message: string; details?: any; }'."}}]}]}]}, "relatedInformation": [{"start": 8990, "length": 6, "messageText": "The expected type comes from property 'checks' which is declared here on type '{ status: \"critical\" | \"healthy\" | \"warning\"; checks: { name: string; status: \"warn\" | \"pass\" | \"fail\"; message: string; details?: any; }[]; summary: { totalChecks: number; passed: number; warnings: number; failures: number; }; }'", "category": 3, "code": 6500}]}]], [200, [{"start": 14949, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'tweets' does not exist on type 'TwitterApiResponse<any>'."}, {"start": 15095, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'users' does not exist on type 'TwitterApiResponse<any>'."}, {"start": 16643, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ id: any; text: any; author_id: any; created_at: any; conversation_id: any; in_reply_to_user_id: any; referenced_tweets: any; public_metrics: { like_count: any; retweet_count: any; reply_count: any; impression_count: any; }; attachments: any; entities: any; } | null' is not assignable to type 'TwitterTweet | null'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ id: any; text: any; author_id: any; created_at: any; conversation_id: any; in_reply_to_user_id: any; referenced_tweets: any; public_metrics: { like_count: any; retweet_count: any; reply_count: any; impression_count: any; }; attachments: any; entities: any; }' is not assignable to type 'TwitterTweet'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'public_metrics' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Property 'quote_count' is missing in type '{ like_count: any; retweet_count: any; reply_count: any; impression_count: any; }' but required in type '{ retweet_count: number; like_count: number; reply_count: number; quote_count: number; bookmark_count?: number | undefined; impression_count?: number | undefined; }'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ id: any; text: any; author_id: any; created_at: any; conversation_id: any; in_reply_to_user_id: any; referenced_tweets: any; public_metrics: { like_count: any; retweet_count: any; reply_count: any; impression_count: any; }; attachments: any; entities: any; }' is not assignable to type 'TwitterTweet'."}}]}]}]}, "relatedInformation": [{"file": "./types/twitter.ts", "start": 1855, "length": 11, "messageText": "'quote_count' is declared here.", "category": 3, "code": 2728}, {"file": "./types/twitter.ts", "start": 4354, "length": 5, "messageText": "The expected type comes from property 'tweet' which is declared here on type 'TweetResponse'", "category": 3, "code": 6500}]}]], [202, [{"start": 2337, "length": 8, "code": 2739, "category": 1, "messageText": "Type 'Omit<UnifiedImageResponse, \"isFallback\" | \"generatedAt\">' is missing the following properties from type 'UnifiedImageResponse': isFallback, generatedAt", "canonicalHead": {"code": 2322, "messageText": "Type 'Omit<UnifiedImageResponse, \"isFallback\" | \"generatedAt\">' is not assignable to type 'UnifiedImageResponse'."}}, {"start": 2719, "length": 8, "code": 2739, "category": 1, "messageText": "Type 'Omit<UnifiedImageResponse, \"isFallback\" | \"generatedAt\">' is missing the following properties from type 'UnifiedImageResponse': isFallback, generatedAt", "canonicalHead": {"code": 2322, "messageText": "Type 'Omit<UnifiedImageResponse, \"isFallback\" | \"generatedAt\">' is not assignable to type 'UnifiedImageResponse'."}}]], [208, [{"start": 1163, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 1209, "length": 1, "messageText": "Parameter 'q' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1749, "length": 7, "messageText": "Parameter 'mention' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2718, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2775, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2901, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 3515, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3779, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4815, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 4861, "length": 1, "messageText": "Parameter 'q' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5054, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6160, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 6544, "length": 7, "messageText": "Parameter 'mention' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7340, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7397, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7448, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 8062, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8320, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9498, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 9544, "length": 1, "messageText": "Parameter 'q' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9676, "length": 7, "messageText": "Parameter 'mention' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10267, "length": 7, "messageText": "Parameter 'mention' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 11087, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 11228, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 11279, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 11907, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [214, [{"start": 1153, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 1215, "length": 1, "messageText": "Parameter 'q' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6874, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'db' does not exist on type 'ActionCtx'."}, {"start": 6936, "length": 1, "messageText": "Parameter 'q' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [215, [{"start": 12822, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'platform' does not exist on type '{ platform: \"twitter\"; aspectRatio: \"landscape\"; style: \"vibrant\"; provider: \"auto\"; } | { aspectRatio: \"landscape\"; style: \"professional\"; provider: \"fal\"; } | { platform: \"instagram\"; aspectRatio: \"square\"; style: \"vibrant\"; provider: \"auto\"; } | { ...; } | { ...; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'platform' does not exist on type '{ aspectRatio: \"landscape\"; style: \"professional\"; provider: \"fal\"; }'.", "category": 1, "code": 2339}]}}, {"start": 12843, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'platform' does not exist on type '{ platform: \"twitter\"; aspectRatio: \"landscape\"; style: \"vibrant\"; provider: \"auto\"; } | { aspectRatio: \"landscape\"; style: \"professional\"; provider: \"fal\"; } | { platform: \"instagram\"; aspectRatio: \"square\"; style: \"vibrant\"; provider: \"auto\"; } | { ...; } | { ...; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'platform' does not exist on type '{ aspectRatio: \"landscape\"; style: \"professional\"; provider: \"fal\"; }'.", "category": 1, "code": 2339}]}}]], [218, [{"start": 636, "length": 32, "messageText": "Module \"./unifiedImageGeneration\" has already exported a member named 'testUnifiedImageGeneration'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"start": 877, "length": 9, "messageText": "Cannot find module './types' or its corresponding type declarations.", "category": 1, "code": 2307}]], [219, [{"start": 8230, "length": 7, "code": 2719, "category": 1, "messageText": {"messageText": "Type '(ctx: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/server/registration\").GenericQueryCtx<{ wallets: { document: { _id: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/values/value\").Id<\"wallets\">; _creati...' is not assignable to type '(ctx: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/server/registration\").GenericQueryCtx<{ wallets: { document: { _id: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/values/value\").Id<\"wallets\">; _creati...'. Two different types with this name exist, but they are unrelated.", "category": 1, "code": 2719, "next": [{"messageText": "Type 'Promise<{ data: { _id: Id<\"mentions\">; mentionTweetId: string; mentionContent: string; mentionAuthor: string; mentionAuthorHandle: string; mentionAuthorFollowers: number | undefined; mentionAuthorVerified: boolean | undefined; ... 8 more ...; url: string | undefined; }[]; nextCursor: string | null; hasMore: boolean;...' is not assignable to type 'ValidatorTypeToReturnType<{ data: { createdAt: number; engagement: { views?: number | undefined; likes: number; retweets: number; replies: number; }; url: string; mentionTweetId: string; mentionContent: string; mentionAuthor: string; ... 9 more ...; _id: Id<...>; }[]; hasMore: boolean; nextCursor: string | null; }>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'Promise<{ data: { _id: Id<\"mentions\">; mentionTweetId: string; mentionContent: string; mentionAuthor: string; mentionAuthorHandle: string; mentionAuthorFollowers: number | undefined; mentionAuthorVerified: boolean | undefined; ... 8 more ...; url: string | undefined; }[]; nextCursor: string | null; hasMore: boolean;...' is not assignable to type 'Promise<{ data: { createdAt: number; engagement: { views?: number | undefined; likes: number; retweets: number; replies: number; }; url: string; mentionTweetId: string; mentionContent: string; mentionAuthor: string; ... 9 more ...; _id: Id<...>; }[]; hasMore: boolean; nextCursor: string | null; }>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ data: { _id: Id<\"mentions\">; mentionTweetId: string; mentionContent: string; mentionAuthor: string; mentionAuthorHandle: string; mentionAuthorFollowers: number | undefined; mentionAuthorVerified: boolean | undefined; ... 8 more ...; url: string | undefined; }[]; nextCursor: string | null; hasMore: boolean; }' is not assignable to type '{ data: { createdAt: number; engagement: { views?: number | undefined; likes: number; retweets: number; replies: number; }; url: string; mentionTweetId: string; mentionContent: string; mentionAuthor: string; ... 9 more ...; _id: Id<...>; }[]; hasMore: boolean; nextCursor: string | null; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'data' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type '{ _id: Id<\"mentions\">; mentionTweetId: string; mentionContent: string; mentionAuthor: string; mentionAuthorHandle: string; mentionAuthorFollowers: number | undefined; mentionAuthorVerified: boolean | undefined; ... 8 more ...; url: string | undefined; }[]' is not assignable to type '{ createdAt: number; engagement: { views?: number | undefined; likes: number; retweets: number; replies: number; }; url: string; mentionTweetId: string; mentionContent: string; mentionAuthor: string; ... 9 more ...; _id: Id<...>; }[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ _id: Id<\"mentions\">; mentionTweetId: string; mentionContent: string; mentionAuthor: string; mentionAuthorHandle: string; mentionAuthorFollowers: number | undefined; mentionAuthorVerified: boolean | undefined; ... 8 more ...; url: string | undefined; }' is not assignable to type '{ createdAt: number; engagement: { views?: number | undefined; likes: number; retweets: number; replies: number; }; url: string; mentionTweetId: string; mentionContent: string; mentionAuthor: string; ... 9 more ...; _id: Id<...>; }'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'url' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}], "canonicalHead": {"code": 2322, "messageText": "Type '{ _id: Id<\"mentions\">; mentionTweetId: string; mentionContent: string; mentionAuthor: string; mentionAuthorHandle: string; mentionAuthorFollowers: number | undefined; mentionAuthorVerified: boolean | undefined; ... 8 more ...; url: string | undefined; }' is not assignable to type '{ createdAt: number; engagement: { views?: number | undefined; likes: number; retweets: number; replies: number; }; url: string; mentionTweetId: string; mentionContent: string; mentionAuthor: string; ... 9 more ...; _id: Id<...>; }'."}}]}]}], "canonicalHead": {"code": 2322, "messageText": "Type '{ data: { _id: Id<\"mentions\">; mentionTweetId: string; mentionContent: string; mentionAuthor: string; mentionAuthorHandle: string; mentionAuthorFollowers: number | undefined; mentionAuthorVerified: boolean | undefined; ... 8 more ...; url: string | undefined; }[]; nextCursor: string | null; hasMore: boolean; }' is not assignable to type '{ data: { createdAt: number; engagement: { views?: number | undefined; likes: number; retweets: number; replies: number; }; url: string; mentionTweetId: string; mentionContent: string; mentionAuthor: string; ... 9 more ...; _id: Id<...>; }[]; hasMore: boolean; nextCursor: string | null; }'."}}]}]}]}], "canonicalHead": {"code": 2719, "messageText": "Type '(ctx: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/server/registration\").GenericQueryCtx<{ wallets: { document: { _id: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/values/value\").Id<\"wallets\">; _creati...' is not assignable to type '(ctx: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/server/registration\").GenericQueryCtx<{ wallets: { document: { _id: import(\"/Users/<USER>/Downloads/Coding/Personal/BuddyChipPro/node_modules/convex/dist/esm-types/values/value\").Id<\"wallets\">; _creati...'. Two different types with this name exist, but they are unrelated."}}]}}]], [222, [{"start": 325, "length": 12, "messageText": "'\"./queries\"' has no exported member named 'MentionStats'. Did you mean 'getMentionStats'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./mentions/queries.ts", "start": 2173, "length": 15, "messageText": "'getMentionStats' is declared here.", "category": 3, "code": 2728}]}, {"start": 341, "length": 19, "messageText": "Module '\"./queries\"' has no exported member 'MentionWithAnalysis'.", "category": 1, "code": 2305}, {"start": 364, "length": 22, "messageText": "Module '\"./queries\"' has no exported member 'OptimizedMentionResult'.", "category": 1, "code": 2305}, {"start": 390, "length": 18, "messageText": "Module '\"./queries\"' has no exported member 'MentionBatchResult'.", "category": 1, "code": 2305}]], [223, [{"start": 3983, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'createdAt' does not exist on type 'never'."}, {"start": 11818, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'createdAt' does not exist on type 'never'."}]], [225, [{"start": 209, "length": 17, "messageText": "'\"./queries\"' has no exported member named 'ResponseAnalytics'. Did you mean 'getResponseAnalytics'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./responses/queries.ts", "start": 4141, "length": 20, "messageText": "'getResponseAnalytics' is declared here.", "category": 3, "code": 2728}]}, {"start": 230, "length": 13, "messageText": "'\"./queries\"' has no exported member named 'ResponseStats'. Did you mean 'getResponseStats'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./responses/queries.ts", "start": 14812, "length": 16, "messageText": "'getResponseStats' is declared here.", "category": 3, "code": 2728}]}, {"start": 247, "length": 12, "messageText": "'\"./queries\"' has no exported member named 'UserResponse'. Did you mean 'getUserResponses'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./responses/queries.ts", "start": 199, "length": 16, "messageText": "'getUserResponses' is declared here.", "category": 3, "code": 2728}]}, {"start": 263, "length": 20, "messageText": "Module '\"./queries\"' has no exported member 'ResponseWithMetadata'.", "category": 1, "code": 2305}]]], "affectedFilesPendingEmit": [207, 208, 209, 218, 214, 210, 213, 217, 211, 215, 212, 216, 171, 88, 173, 176, 174, 177, 178, 179, 180, 181, 182, 185, 186, 187, 188, 189, 175, 190, 168, 191, 183, 192, 169, 193, 194, 184, 195, 196, 200, 201, 198, 197, 199, 202, 203, 222, 220, 221, 219, 225, 224, 223, 85, 89, 204, 205, 172, 170, 90], "emitSignatures": [85, 88, 89, 90, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225], "version": "5.8.3"}