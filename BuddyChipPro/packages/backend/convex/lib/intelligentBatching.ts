/**
 * 🚀 INTELLIGENT BATCHING SYSTEM
 * 
 * Advanced batching logic to minimize API calls and bandwidth usage
 * while maintaining responsiveness for critical operations
 */

import { GenericDatabaseReader, GenericDatabaseWriter } from "convex/server";

/**
 * Batching configuration for different operation types
 */
export const BATCH_CONFIG = {
  // Mention monitoring - balance between responsiveness and efficiency
  MENTION_MONITORING: {
    maxBatchSize: 20,           // Max accounts per batch
    optimalBatchSize: 8,        // Optimal accounts per batch for performance
    maxWaitTime: 10 * 60 * 1000, // Max 10 minutes between batches
    minBatchSize: 3,            // Minimum accounts to start a batch
    priorityThreshold: 0.8,     // Threshold for high-priority accounts
  },
  
  // Tweet scraping - less frequent but comprehensive
  TWEET_SCRAPING: {
    maxBatchSize: 15,
    optimalBatchSize: 5,
    maxWaitTime: 60 * 60 * 1000, // Max 1 hour between batches
    minBatchSize: 2,
    priorityThreshold: 0.7,
  },
  
  // AI analysis - computationally expensive, batch for efficiency
  AI_ANALYSIS: {
    maxBatchSize: 50,           // Larger batches for AI processing
    optimalBatchSize: 25,
    maxWaitTime: 30 * 60 * 1000, // Max 30 minutes
    minBatchSize: 10,
    priorityThreshold: 0.9,
  },
  
  // Health checks - lightweight, less frequent
  HEALTH_CHECKS: {
    maxBatchSize: 100,          // Can batch many health checks
    optimalBatchSize: 50,
    maxWaitTime: 2 * 60 * 60 * 1000, // Max 2 hours
    minBatchSize: 20,
    priorityThreshold: 0.5,
  },
} as const;

/**
 * Activity-based frequency calculator
 */
export class ActivityBasedScheduler {
  /**
   * Calculate optimal cron frequency based on user activity levels
   */
  static calculateOptimalFrequency(
    operationType: keyof typeof BATCH_CONFIG,
    activityLevel: "low" | "medium" | "high" | "peak",
    timeOfDay: number // Hour 0-23
  ): {
    intervalMinutes: number;
    batchSize: number;
    shouldSkip: boolean;
  } {
    const config = BATCH_CONFIG[operationType];
    const isPeakHours = timeOfDay >= 9 && timeOfDay <= 21; // 9 AM - 9 PM
    const isBusinessHours = timeOfDay >= 9 && timeOfDay <= 17; // 9 AM - 5 PM
    
    // Base frequencies by operation type (in minutes)
    const baseFrequencies = {
      MENTION_MONITORING: {
        low: 60,      // 1 hour during low activity
        medium: 30,   // 30 minutes during medium activity  
        high: 15,     // 15 minutes during high activity
        peak: 10,     // 10 minutes during peak activity
      },
      TWEET_SCRAPING: {
        low: 180,     // 3 hours during low activity
        medium: 120,  // 2 hours during medium activity
        high: 90,     // 1.5 hours during high activity
        peak: 60,     // 1 hour during peak activity
      },
      AI_ANALYSIS: {
        low: 120,     // 2 hours during low activity
        medium: 90,   // 1.5 hours during medium activity
        high: 60,     // 1 hour during high activity
        peak: 45,     // 45 minutes during peak activity
      },
      HEALTH_CHECKS: {
        low: 240,     // 4 hours during low activity
        medium: 180,  // 3 hours during medium activity
        high: 120,    // 2 hours during high activity
        peak: 90,     // 1.5 hours during peak activity
      },
    };
    
    let intervalMinutes = baseFrequencies[operationType][activityLevel];
    
    // Adjust for time of day
    if (!isPeakHours && activityLevel !== "peak") {
      intervalMinutes = Math.min(intervalMinutes * 1.5, 360); // Max 6 hours during off-peak
    }
    
    // Skip non-critical operations during very low activity periods
    const shouldSkip = (
      !isBusinessHours && 
      activityLevel === "low" && 
      operationType !== "MENTION_MONITORING"
    );
    
    // Calculate optimal batch size based on frequency
    const frequencyMultiplier = intervalMinutes / baseFrequencies[operationType].medium;
    const batchSize = Math.min(
      Math.max(
        Math.round(config.optimalBatchSize * frequencyMultiplier),
        config.minBatchSize
      ),
      config.maxBatchSize
    );
    
    return {
      intervalMinutes,
      batchSize,
      shouldSkip,
    };
  }
  
  /**
   * Determine current activity level based on recent user interactions
   */
  static async determineActivityLevel(
    db: GenericDatabaseReader<any>
  ): Promise<"low" | "medium" | "high" | "peak"> {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    const sixHoursAgo = now - 6 * 60 * 60 * 1000;
    
    // Check recent user activity indicators
    const recentMentions = await db
      .query("mentions")
      .withIndex("by_discovered_at", q => q.gte("discoveredAt", oneHourAgo))
      .take(100);
    
    const recentResponses = await db
      .query("responses")
      .withIndex("by_created_at", q => q.gte("createdAt", sixHoursAgo))
      .take(50);
    
    const recentAnalytics = await db
      .query("analyticsEvents")
      .withIndex("by_timestamp", q => q.gte("timestamp", oneHourAgo))
      .take(200);
    
    // Calculate activity score
    const mentionScore = Math.min(recentMentions.length / 10, 4); // 0-4 scale
    const responseScore = Math.min(recentResponses.length / 5, 3); // 0-3 scale  
    const analyticsScore = Math.min(recentAnalytics.length / 20, 3); // 0-3 scale
    
    const totalScore = mentionScore + responseScore + analyticsScore;
    
    // Classify activity level
    if (totalScore >= 8) return "peak";
    if (totalScore >= 5) return "high";
    if (totalScore >= 2) return "medium";
    return "low";
  }
}

/**
 * Smart batch processor with priority queue
 */
export class SmartBatchProcessor {
  constructor(
    private db: GenericDatabaseReader<any> | GenericDatabaseWriter<any>
  ) {}
  
  /**
   * Create optimized batches based on priority and timing
   */
  async createOptimizedBatches<T extends { priority?: string; lastProcessed?: number }>(
    items: T[],
    batchConfig: typeof BATCH_CONFIG[keyof typeof BATCH_CONFIG],
    currentTime: number = Date.now()
  ): Promise<{
    priorityBatch: T[];
    regularBatch: T[];
    deferredItems: T[];
    totalBatches: number;
  }> {
    // Sort by priority and staleness
    const sortedItems = items.sort((a, b) => {
      // Priority score (high = 3, medium = 2, low = 1)
      const aPriority = a.priority === "high" ? 3 : a.priority === "medium" ? 2 : 1;
      const bPriority = b.priority === "high" ? 3 : b.priority === "medium" ? 2 : 1;
      
      // Staleness score (older items get higher priority)
      const aAge = currentTime - (a.lastProcessed || 0);
      const bAge = currentTime - (b.lastProcessed || 0);
      
      // Combined score (priority weighted higher than age)
      const aScore = aPriority * 1000 + aAge / 1000;
      const bScore = bPriority * 1000 + bAge / 1000;
      
      return bScore - aScore; // Higher score first
    });
    
    // Separate into priority and regular batches
    const highPriorityItems = sortedItems.filter(item => 
      item.priority === "high" || 
      (currentTime - (item.lastProcessed || 0)) > batchConfig.maxWaitTime
    );
    
    const regularItems = sortedItems.filter(item => 
      item.priority !== "high" && 
      (currentTime - (item.lastProcessed || 0)) <= batchConfig.maxWaitTime
    );
    
    // Create optimized batches
    const priorityBatch = highPriorityItems.slice(0, batchConfig.maxBatchSize);
    const regularBatch = regularItems.slice(0, batchConfig.optimalBatchSize);
    
    // Items that don't fit in current batches
    const remainingPriority = highPriorityItems.slice(batchConfig.maxBatchSize);
    const remainingRegular = regularItems.slice(batchConfig.optimalBatchSize);
    const deferredItems = [...remainingPriority, ...remainingRegular];
    
    // Calculate total batches needed
    const totalBatches = Math.ceil(
      (priorityBatch.length + regularBatch.length + deferredItems.length) / 
      batchConfig.optimalBatchSize
    );
    
    return {
      priorityBatch,
      regularBatch,
      deferredItems,
      totalBatches,
    };
  }
  
  /**
   * Execute batch with rate limiting and error handling
   */
  async executeBatchWithRateLimit<T, R>(
    batch: T[],
    processor: (item: T) => Promise<R>,
    rateLimit: {
      maxConcurrent: number;
      delayBetweenItems: number;
    } = {
      maxConcurrent: 3,
      delayBetweenItems: 1000, // 1 second between items
    }
  ): Promise<{
    results: R[];
    errors: { item: T; error: string }[];
    executionTime: number;
  }> {
    const startTime = Date.now();
    const results: R[] = [];
    const errors: { item: T; error: string }[] = [];
    
    // Process in chunks to respect rate limits
    const chunks = this.chunkArray(batch, rateLimit.maxConcurrent);
    
    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (item) => {
        try {
          const result = await processor(item);
          results.push(result);
          return result;
        } catch (error) {
          errors.push({
            item,
            error: error instanceof Error ? error.message : "Unknown error",
          });
          return null;
        }
      });
      
      // Wait for chunk to complete
      await Promise.all(chunkPromises);
      
      // Add delay between chunks to respect rate limits
      if (chunks.indexOf(chunk) < chunks.length - 1) {
        await this.delay(rateLimit.delayBetweenItems);
      }
    }
    
    return {
      results,
      errors,
      executionTime: Date.now() - startTime,
    };
  }
  
  /**
   * Utility: Split array into chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
  
  /**
   * Utility: Async delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Bandwidth usage calculator for optimization decisions
 */
export class BandwidthCalculator {
  /**
   * Estimate bandwidth usage for different batching strategies
   */
  static estimateBandwidthUsage(
    operationType: keyof typeof BATCH_CONFIG,
    frequency: number, // executions per day
    batchSize: number,
    averageResponseSize: number // bytes per response
  ): {
    dailyBandwidth: number; // bytes per day
    monthlyBandwidth: number; // bytes per month
    costEstimate: number; // estimated cost per month
  } {
    const dailyExecutions = frequency;
    const dailyBandwidth = dailyExecutions * batchSize * averageResponseSize;
    const monthlyBandwidth = dailyBandwidth * 30;
    
    // Rough cost estimate (varies by provider)
    const costPerGB = 0.10; // $0.10 per GB (conservative estimate)
    const costEstimate = (monthlyBandwidth / (1024 * 1024 * 1024)) * costPerGB;
    
    return {
      dailyBandwidth,
      monthlyBandwidth,
      costEstimate,
    };
  }
  
  /**
   * Compare bandwidth usage between different strategies
   */
  static compareBandwidthStrategies(
    strategies: Array<{
      name: string;
      frequency: number;
      batchSize: number;
      averageResponseSize: number;
    }>
  ): Array<{
    name: string;
    dailyBandwidth: number;
    monthlyBandwidth: number;
    costEstimate: number;
    efficiencyRatio: number; // higher is better
  }> {
    const results = strategies.map(strategy => {
      const usage = this.estimateBandwidthUsage(
        "MENTION_MONITORING", // default for comparison
        strategy.frequency,
        strategy.batchSize,
        strategy.averageResponseSize
      );
      
      return {
        name: strategy.name,
        ...usage,
        efficiencyRatio: strategy.batchSize / strategy.frequency, // batch size per execution
      };
    });
    
    return results.sort((a, b) => b.efficiencyRatio - a.efficiencyRatio);
  }
}