/**
 * 🚀 BANDWIDTH MONITORING & OPTIMIZATION TRACKING
 * 
 * This module tracks bandwidth usage and optimization effectiveness
 * to ensure our optimization efforts are working as expected
 */

import { internalMutation, query } from "../_generated/server";
import { v } from "convex/values";

/**
 * Log bandwidth usage for monitoring and optimization tracking
 */
export const logBandwidthUsage = internalMutation({
  args: {
    operation: v.string(),
    bytesRead: v.number(),
    executionTime: v.number(),
    recordsScanned: v.number(),
    recordsReturned: v.number(),
    cacheHit: v.boolean(),
    optimizationType: v.optional(v.string()), // "projection", "pagination", "caching", etc.
    estimatedSavings: v.optional(v.number()), // Estimated bytes saved
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("bandwidthLogs", {
      ...args,
      timestamp: Date.now(),
    });
  },
});

/**
 * Get bandwidth usage report for monitoring dashboard
 */
export const getBandwidthReport = query({
  args: {
    hours: v.optional(v.number()),
    operation: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const hoursAgo = Date.now() - (args.hours || 24) * 60 * 60 * 1000;
    
    let query = ctx.db
      .query("bandwidthLogs")
      .withIndex("by_timestamp", q => q.gte("timestamp", hoursAgo));
    
    if (args.operation) {
      query = query.filter(q => q.eq(q.field("operation"), args.operation));
    }
    
    const logs = await query.take(10000); // Limit for analysis
    
    if (logs.length === 0) {
      return {
        totalBytesRead: 0,
        averageExecutionTime: 0,
        cacheHitRate: 0,
        totalSavings: 0,
        topOperations: [],
        optimizationBreakdown: {},
      };
    }
    
    const totalBytesRead = logs.reduce((sum, log) => sum + log.bytesRead, 0);
    const averageExecutionTime = logs.reduce((sum, log) => sum + log.executionTime, 0) / logs.length;
    const cacheHits = logs.filter(log => log.cacheHit).length;
    const cacheHitRate = (cacheHits / logs.length) * 100;
    const totalSavings = logs.reduce((sum, log) => sum + (log.estimatedSavings || 0), 0);
    
    // Top operations by bandwidth usage
    const operationStats = logs.reduce((acc, log) => {
      if (!acc[log.operation]) {
        acc[log.operation] = {
          operation: log.operation,
          totalBytes: 0,
          totalCalls: 0,
          avgExecutionTime: 0,
          cacheHitRate: 0,
        };
      }
      
      acc[log.operation].totalBytes += log.bytesRead;
      acc[log.operation].totalCalls += 1;
      acc[log.operation].avgExecutionTime += log.executionTime;
      if (log.cacheHit) acc[log.operation].cacheHitRate += 1;
      
      return acc;
    }, {} as Record<string, any>);
    
    // Calculate averages and sort by bytes
    const topOperations = Object.values(operationStats)
      .map((stat: any) => ({
        ...stat,
        avgExecutionTime: Math.round(stat.avgExecutionTime / stat.totalCalls),
        cacheHitRate: Math.round((stat.cacheHitRate / stat.totalCalls) * 100),
        avgBytesPerCall: Math.round(stat.totalBytes / stat.totalCalls),
      }))
      .sort((a: any, b: any) => b.totalBytes - a.totalBytes)
      .slice(0, 10);
    
    // Optimization breakdown
    const optimizationBreakdown = logs.reduce((acc, log) => {
      const type = log.optimizationType || "unoptimized";
      if (!acc[type]) {
        acc[type] = {
          calls: 0,
          totalSavings: 0,
          avgSavingsPerCall: 0,
        };
      }
      
      acc[type].calls += 1;
      acc[type].totalSavings += log.estimatedSavings || 0;
      
      return acc;
    }, {} as Record<string, any>);
    
    // Calculate averages
    Object.keys(optimizationBreakdown).forEach(type => {
      const data = optimizationBreakdown[type];
      data.avgSavingsPerCall = data.calls > 0 ? Math.round(data.totalSavings / data.calls) : 0;
    });
    
    return {
      timeRange: `${args.hours || 24} hours`,
      totalBytesRead: Math.round(totalBytesRead),
      totalBytesReadMB: Math.round(totalBytesRead / 1024 / 1024 * 100) / 100,
      averageExecutionTime: Math.round(averageExecutionTime),
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      totalSavings: Math.round(totalSavings),
      totalSavingsMB: Math.round(totalSavings / 1024 / 1024 * 100) / 100,
      totalOperations: logs.length,
      topOperations,
      optimizationBreakdown,
      generatedAt: Date.now(),
    };
  },
});

/**
 * Get optimization effectiveness metrics
 */
export const getOptimizationMetrics = query({
  args: {
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const daysAgo = Date.now() - (args.days || 7) * 24 * 60 * 60 * 1000;
    
    const logs = await ctx.db
      .query("bandwidthLogs")
      .withIndex("by_timestamp", q => q.gte("timestamp", daysAgo))
      .take(50000); // Large limit for comprehensive analysis
    
    if (logs.length === 0) {
      return {
        totalOptimizations: 0,
        totalSavings: 0,
        averageSavingsPerOptimization: 0,
        optimizationTypes: {},
        dailyTrends: [],
      };
    }
    
    const optimizedLogs = logs.filter(log => log.optimizationType);
    const totalSavings = optimizedLogs.reduce((sum, log) => sum + (log.estimatedSavings || 0), 0);
    
    // Group by optimization type
    const optimizationTypes = optimizedLogs.reduce((acc, log) => {
      const type = log.optimizationType!;
      if (!acc[type]) {
        acc[type] = {
          count: 0,
          totalSavings: 0,
          avgSavingsPerCall: 0,
          totalBytesRead: 0,
        };
      }
      
      acc[type].count += 1;
      acc[type].totalSavings += log.estimatedSavings || 0;
      acc[type].totalBytesRead += log.bytesRead;
      
      return acc;
    }, {} as Record<string, any>);
    
    // Calculate averages
    Object.keys(optimizationTypes).forEach(type => {
      const data = optimizationTypes[type];
      data.avgSavingsPerCall = data.count > 0 ? Math.round(data.totalSavings / data.count) : 0;
      data.savingsPercent = data.totalBytesRead > 0 
        ? Math.round((data.totalSavings / (data.totalBytesRead + data.totalSavings)) * 100)
        : 0;
    });
    
    // Daily trends (last 7 days)
    const dailyTrends = [];
    for (let i = 6; i >= 0; i--) {
      const dayStart = Date.now() - (i + 1) * 24 * 60 * 60 * 1000;
      const dayEnd = Date.now() - i * 24 * 60 * 60 * 1000;
      
      const dayLogs = logs.filter(log => log.timestamp >= dayStart && log.timestamp < dayEnd);
      const dayOptimized = dayLogs.filter(log => log.optimizationType);
      const daySavings = dayOptimized.reduce((sum, log) => sum + (log.estimatedSavings || 0), 0);
      const dayBytes = dayLogs.reduce((sum, log) => sum + log.bytesRead, 0);
      
      dailyTrends.push({
        date: new Date(dayStart).toISOString().split('T')[0],
        totalOperations: dayLogs.length,
        optimizedOperations: dayOptimized.length,
        totalSavings: Math.round(daySavings),
        totalBytes: Math.round(dayBytes),
        optimizationRate: dayLogs.length > 0 ? Math.round((dayOptimized.length / dayLogs.length) * 100) : 0,
      });
    }
    
    return {
      timeRange: `${args.days || 7} days`,
      totalOptimizations: optimizedLogs.length,
      totalSavings: Math.round(totalSavings),
      totalSavingsMB: Math.round(totalSavings / 1024 / 1024 * 100) / 100,
      averageSavingsPerOptimization: optimizedLogs.length > 0 
        ? Math.round(totalSavings / optimizedLogs.length) 
        : 0,
      optimizationTypes,
      dailyTrends,
      generatedAt: Date.now(),
    };
  },
});

/**
 * Helper function to log a bandwidth optimization
 * Use this in optimized queries to track effectiveness
 */
export async function logOptimization(
  ctx: any,
  operation: string,
  originalSize: number,
  optimizedSize: number,
  executionTime: number,
  recordsScanned: number,
  recordsReturned: number,
  optimizationType: string,
  cacheHit: boolean = false
): Promise<void> {
  const savings = originalSize - optimizedSize;
  
  try {
    await ctx.scheduler.runAfter(0, "lib/bandwidthMonitor:logBandwidthUsage", {
      operation,
      bytesRead: optimizedSize,
      executionTime,
      recordsScanned,
      recordsReturned,
      cacheHit,
      optimizationType,
      estimatedSavings: savings,
    });
  } catch (error) {
    // Don't fail the main operation if logging fails
    console.warn("Failed to log bandwidth optimization:", error);
  }
}

/**
 * Helper to calculate estimated bandwidth savings
 */
export function calculateBandwidthSavings(
  recordCount: number,
  avgFullRecordSize: number,
  avgProjectedRecordSize: number
): {
  originalSize: number;
  optimizedSize: number;
  savings: number;
  savingsPercent: number;
} {
  const originalSize = recordCount * avgFullRecordSize;
  const optimizedSize = recordCount * avgProjectedRecordSize;
  const savings = originalSize - optimizedSize;
  const savingsPercent = originalSize > 0 ? Math.round((savings / originalSize) * 100) : 0;
  
  return {
    originalSize,
    optimizedSize,
    savings,
    savingsPercent,
  };
}