/**
 * Prompt templates for AI response generation
 * Handles different response styles and scenarios
 */

export interface PromptContext {
  originalContent: string;
  authorInfo?: {
    handle: string;
    displayName: string;
    isVerified?: boolean;
    followerCount?: number;
  };
  tweetMetadata?: {
    engagement: {
      likes: number;
      retweets: number;
      replies: number;
    };
    createdAt: number;
    hasMedia?: boolean;
  };
  userContext?: {
    expertise?: string[];
    interests?: string[];
    writingStyle?: string;
    brand?: string;
  };
  responseStrategy?: 'engage' | 'educate' | 'promote' | 'support' | 'humor';
  maxLength?: number;
}

export interface EnhancedPromptContext extends PromptContext {
  semanticScore?: number;
  enhancedScore?: number;
  semanticContext?: {
    similarContent?: any[];
    userInterests?: string[];
  };
}

export interface PromptTemplate {
  systemPrompt: string;
  userPrompt: (context: PromptContext) => string;
  maxTokens?: number;
  temperature?: number;
}

/**
 * Base system prompts for different response styles
 */
export const SYSTEM_PROMPTS = {
  professional: `You are a professional social media assistant helping create thoughtful, engaging responses to tweets. Your responses should be:
- Professional yet approachable
- Informative and valuable
- Respectful and constructive
- Concise and well-structured
- Focused on adding genuine value to the conversation

Avoid being salesy, overly promotional, or generic. Always aim to contribute meaningfully to the discussion.`,

  casual: `You are a friendly social media assistant helping create casual, authentic responses to tweets. Your responses should be:
- Conversational and relatable
- Natural and human-like
- Engaging without being try-hard
- Genuine and authentic
- Using appropriate casual language and tone

Keep responses feeling organic and avoid corporate speak or overly formal language.`,

  humorous: `You are a witty social media assistant helping create clever, humorous responses to tweets. Your responses should be:
- Clever and genuinely funny
- Appropriate and respectful
- Using wordplay, observations, or light humor
- Engaging and shareable
- Never offensive or controversial

Focus on intelligent humor that adds to the conversation rather than cheap laughs.`,

  technical: `You are a technical expert social media assistant helping create informative responses to tweets. Your responses should be:
- Technically accurate and precise
- Educational and informative
- Using appropriate technical terminology
- Providing genuine insights or clarifications
- Citing sources when relevant

Focus on sharing knowledge and expertise while remaining accessible to your audience.`,

  supportive: `You are an empathetic social media assistant helping create supportive responses to tweets. Your responses should be:
- Encouraging and uplifting
- Empathetic and understanding
- Offering practical help or resources when appropriate
- Genuine and caring
- Respectful of different perspectives

Focus on being helpful and supportive while maintaining appropriate boundaries.`,
};

/**
 * Response generation templates
 */
export const RESPONSE_TEMPLATES: Record<string, PromptTemplate> = {
  
  tweetReply: {
    systemPrompt: SYSTEM_PROMPTS.professional,
    userPrompt: (context: PromptContext) => `
Generate a thoughtful reply to this tweet:

Original Tweet: "${context.originalContent}"
Author: @${context.authorInfo?.handle} (${context.authorInfo?.displayName})
${context.authorInfo?.isVerified ? 'Verified account' : ''}
${context.tweetMetadata ? `Engagement: ${context.tweetMetadata.engagement.likes} likes, ${context.tweetMetadata.engagement.retweets} retweets` : ''}

${context.userContext?.expertise ? `Your expertise areas: ${context.userContext.expertise.join(', ')}` : ''}
${context.userContext?.interests ? `Your interests: ${context.userContext.interests.join(', ')}` : ''}
${context.responseStrategy ? `Response strategy: ${context.responseStrategy}` : ''}

Create a reply that:
1. Adds genuine value to the conversation
2. Is relevant to the original tweet's topic
3. Matches the appropriate tone for the context
4. Stays under ${context.maxLength || 280} characters
5. Encourages further engagement

Reply:`,
    maxTokens: 150,
    temperature: 0.7,
  },

  tweetRemake: {
    systemPrompt: SYSTEM_PROMPTS.professional,
    userPrompt: (context: PromptContext) => `
Remake this tweet to be more engaging while preserving the core message:

Original Tweet: "${context.originalContent}"

${context.userContext?.writingStyle ? `Writing style preference: ${context.userContext.writingStyle}` : ''}
${context.userContext?.brand ? `Brand voice: ${context.userContext.brand}` : ''}

Create a remake that:
1. Preserves the original meaning and intent
2. Makes it more engaging and shareable
3. Improves clarity and impact
4. Uses better structure and flow
5. Stays under ${context.maxLength || 280} characters
6. Optimizes for engagement (likes, retweets, replies)

Remade Tweet:`,
    maxTokens: 200,
    temperature: 0.8,
  },

  mentionResponse: {
    systemPrompt: SYSTEM_PROMPTS.casual,
    userPrompt: (context: PromptContext) => `
Someone mentioned you in this tweet. Generate an appropriate response:

Mention Tweet: "${context.originalContent}"
Author: @${context.authorInfo?.handle} (${context.authorInfo?.displayName})
${context.authorInfo?.followerCount ? `Followers: ${context.authorInfo.followerCount}` : ''}

${context.userContext?.brand ? `Your brand: ${context.userContext.brand}` : ''}
${context.responseStrategy ? `Response approach: ${context.responseStrategy}` : ''}

Create a response that:
1. Acknowledges the mention appropriately
2. Continues the conversation naturally
3. Represents your brand/personality well
4. Is neither too formal nor too casual
5. Encourages positive engagement
6. Stays under ${context.maxLength || 280} characters

Response:`,
    maxTokens: 150,
    temperature: 0.6,
  },

  threadContinuation: {
    systemPrompt: SYSTEM_PROMPTS.professional,
    userPrompt: (context: PromptContext) => `
Continue this tweet thread with a valuable addition:

Thread Context: "${context.originalContent}"

${context.userContext?.expertise ? `Your expertise: ${context.userContext.expertise.join(', ')}` : ''}

Create a follow-up tweet that:
1. Builds on the previous point naturally
2. Adds new valuable information or perspective
3. Maintains consistent tone and style
4. Works well as part of the thread flow
5. Encourages readers to engage or share
6. Stays under ${context.maxLength || 280} characters

Follow-up Tweet:`,
    maxTokens: 200,
    temperature: 0.7,
  },

  questionResponse: {
    systemPrompt: SYSTEM_PROMPTS.technical,
    userPrompt: (context: PromptContext) => `
Answer this question from a tweet:

Question Tweet: "${context.originalContent}"
Asked by: @${context.authorInfo?.handle}

${context.userContext?.expertise ? `Your expertise areas: ${context.userContext.expertise.join(', ')}` : ''}

Provide an answer that:
1. Directly addresses the question
2. Is accurate and helpful
3. Demonstrates your expertise appropriately
4. Is concise but comprehensive
5. Invites follow-up if needed
6. Stays under ${context.maxLength || 280} characters

Answer:`,
    maxTokens: 180,
    temperature: 0.5,
  },
};

/**
 * Enhanced analysis context interface
 */
export interface EnhancedPromptContext extends PromptContext {
  semanticScore?: number;
  enhancedScore?: number;
  semanticContext?: {
    similarContent?: any[];
    userInterests?: string[];
    relevanceScore?: number;
  };
  similarContent?: any[];
}

/**
 * Tweet analysis templates
 */
export const ANALYSIS_TEMPLATES = {
  
  worthinessAnalysis: {
    systemPrompt: `You are an expert social media analyst. Analyze tweets to determine if they're worth responding to based on engagement potential, relevance, and strategic value.`,
    userPrompt: (context: PromptContext) => `
Analyze this tweet for response worthiness:

Tweet: "${context.originalContent}"
Author: @${context.authorInfo?.handle} (${context.authorInfo?.displayName})
${context.authorInfo?.isVerified ? 'Verified: Yes' : 'Verified: No'}
${context.authorInfo?.followerCount ? `Followers: ${context.authorInfo.followerCount}` : ''}
${context.tweetMetadata ? `Current engagement: ${context.tweetMetadata.engagement.likes} likes, ${context.tweetMetadata.engagement.retweets} retweets, ${context.tweetMetadata.engagement.replies} replies` : ''}

${context.userContext?.expertise ? `Your expertise: ${context.userContext.expertise.join(', ')}` : ''}
${context.userContext?.interests ? `Your interests: ${context.userContext.interests.join(', ')}` : ''}

Provide analysis in this exact JSON format:
{
  "shouldRespond": boolean,
  "confidence": number (0-1),
  "priority": "high" | "medium" | "low",
  "reasons": ["reason1", "reason2"],
  "suggestedStrategy": "engage" | "educate" | "promote" | "support" | "humor",
  "estimatedEngagement": {
    "likes": number,
    "retweets": number,
    "replies": number
  }
}`,
    maxTokens: 300,
    temperature: 0.3,
  },

  sentimentAnalysis: {
    systemPrompt: `You are a sentiment analysis expert. Analyze the emotional tone and sentiment of social media content accurately.`,
    userPrompt: (context: PromptContext) => `
Analyze the sentiment of this tweet:

Tweet: "${context.originalContent}"
Author: @${context.authorInfo?.handle}

Provide analysis in this exact JSON format:
{
  "sentiment": "positive" | "negative" | "neutral",
  "confidence": number (0-1),
  "emotions": ["emotion1", "emotion2"],
  "topics": ["topic1", "topic2"],
  "urgency": "high" | "medium" | "low",
  "responseTone": "supportive" | "informative" | "celebratory" | "cautious"
}`,
    maxTokens: 200,
    temperature: 0.2,
  },

  topicExtraction: {
    systemPrompt: `You are a topic modeling expert. Extract the main topics and themes from social media content.`,
    userPrompt: (context: PromptContext) => `
Extract topics and themes from this tweet:

Tweet: "${context.originalContent}"

Identify:
1. Main topics (2-5 words each)
2. Industry/domain relevance
3. Hashtag potential
4. Content category

Provide analysis in this exact JSON format:
{
  "mainTopics": ["topic1", "topic2"],
  "industries": ["industry1", "industry2"],
  "contentType": "question" | "announcement" | "opinion" | "news" | "personal" | "educational",
  "suggestedHashtags": ["#hashtag1", "#hashtag2"],
  "complexity": "simple" | "moderate" | "complex"
}`,
    maxTokens: 250,
    temperature: 0.3,
  },

  enhancedWorthiness: {
    systemPrompt: `You are an advanced AI analyst specializing in social media engagement optimization and strategic response planning. You use sophisticated multi-factor analysis considering content quality, author influence, semantic relevance, engagement potential, and strategic value to determine response worthiness.`,
    userPrompt: (context: EnhancedPromptContext) => `
Analyze this tweet for advanced response worthiness using enhanced scoring metrics:

Tweet: "${context.originalContent}"
Author: @${context.authorInfo?.handle} (${context.authorInfo?.displayName})
${context.authorInfo?.isVerified ? 'Verified: Yes' : 'Verified: No'}
${context.authorInfo?.followerCount ? `Followers: ${context.authorInfo.followerCount}` : ''}
${context.tweetMetadata?.engagement ? `Current engagement: ${context.tweetMetadata.engagement.likes} likes, ${context.tweetMetadata.engagement.retweets} retweets, ${context.tweetMetadata.engagement.replies} replies` : ''}

Enhanced Analysis Data:
${context.semanticScore !== undefined ? `Semantic relevance to user interests: ${(context.semanticScore * 100).toFixed(1)}%` : ''}
${context.enhancedScore !== undefined ? `Pre-calculated worthiness score: ${(context.enhancedScore * 100).toFixed(1)}/100` : ''}
${context.semanticContext?.similarContent?.length ? `Similar content patterns found: ${context.semanticContext.similarContent.length} related tweets` : ''}
${context.semanticContext?.userInterests?.length ? `User interest overlap: ${context.semanticContext.userInterests.slice(0, 3).join(', ')}` : ''}

${context.userContext?.expertise ? `Your expertise areas: ${context.userContext.expertise.join(', ')}` : ''}
${context.userContext?.interests ? `Your interests: ${context.userContext.interests.join(', ')}` : ''}
${context.userContext?.brand ? `Your brand: ${context.userContext.brand}` : ''}

Provide comprehensive analysis in this exact JSON format:
{
  "shouldRespond": boolean,
  "confidence": number (0-1),
  "priority": "urgent" | "high" | "medium" | "low",
  "reasons": ["specific_reason_1", "specific_reason_2", "specific_reason_3"],
  "suggestedStrategy": "engage" | "educate" | "promote" | "support" | "humor" | "question" | "challenge",
  "estimatedEngagement": {
    "likes": number,
    "retweets": number,
    "replies": number
  },
  "riskFactors": ["risk_1", "risk_2"],
  "responseUrgency": "immediate" | "within_hour" | "within_day" | "low",
  "contentCategory": "question" | "opinion" | "news" | "personal" | "educational" | "promotional" | "technical",
  "strategicValue": number (0-1),
  "semanticAlignment": number (0-1),
  "engagementOpportunities": ["opportunity_1", "opportunity_2"],
  "recommendedApproach": {
    "tone": "professional" | "casual" | "supportive" | "authoritative" | "friendly",
    "length": "short" | "medium" | "detailed",
    "timing": "immediate" | "considered" | "delayed"
  },
  "competitiveAdvantage": "high" | "medium" | "low",
  "viralPotential": "high" | "medium" | "low"
}`,
    maxTokens: 600,
    temperature: 0.2,
  },
};

/**
 * Enhanced analysis templates for comprehensive tweet evaluation
 */
export const ENHANCED_ANALYSIS_TEMPLATES = {
  comprehensiveWorthiness: {
    systemPrompt: `You are an advanced AI analyst specializing in social media engagement optimization. Analyze tweets for response worthiness using multi-factor analysis including content quality, author influence, semantic relevance, and strategic value.`,
    userPrompt: (context: EnhancedPromptContext) => `
Analyze this tweet for response worthiness:

Tweet: "${context.originalContent}"
Author: @${context.authorInfo?.handle} (${context.authorInfo?.displayName})
${context.authorInfo?.isVerified ? 'Verified: Yes' : 'Verified: No'}
${context.authorInfo?.followerCount ? `Followers: ${context.authorInfo.followerCount}` : ''}
${context.tweetMetadata?.engagement ? `Engagement: ${context.tweetMetadata.engagement.likes} likes, ${context.tweetMetadata.engagement.retweets} retweets, ${context.tweetMetadata.engagement.replies} replies` : ''}

Enhanced Data:
${context.semanticScore !== undefined ? `Semantic relevance: ${(context.semanticScore * 100).toFixed(1)}%` : ''}
${context.enhancedScore !== undefined ? `Pre-calculated score: ${(context.enhancedScore * 100).toFixed(1)}/100` : ''}

${context.userContext?.expertise ? `Your expertise: ${context.userContext.expertise.join(', ')}` : ''}
${context.userContext?.interests ? `Your interests: ${context.userContext.interests.join(', ')}` : ''}

Return JSON:
{
  "worthinessScore": number (0-1),
  "shouldRespond": boolean,
  "responseStrategy": "engage" | "educate" | "promote" | "support" | "humor",
  "reasoning": "brief explanation",
  "topics": ["topic1", "topic2"],
  "sentiment": "positive" | "negative" | "neutral",
  "urgency": "low" | "medium" | "high"
}`,
    maxTokens: 400,
    temperature: 0.3,
  },
};

/**
 * Helper function to get appropriate template based on context
 */
export function getResponseTemplate(
  type: 'reply' | 'remake' | 'mention' | 'thread' | 'question',
  style: 'professional' | 'casual' | 'humorous' | 'technical' | 'supportive' = 'professional'
): PromptTemplate {
  const baseTemplate = (() => {
    switch (type) {
      case 'reply': return RESPONSE_TEMPLATES.tweetReply;
      case 'remake': return RESPONSE_TEMPLATES.tweetRemake;
      case 'mention': return RESPONSE_TEMPLATES.mentionResponse;
      case 'thread': return RESPONSE_TEMPLATES.threadContinuation;
      case 'question': return RESPONSE_TEMPLATES.questionResponse;
      default: return RESPONSE_TEMPLATES.tweetReply;
    }
  })();

  return {
    ...baseTemplate,
    systemPrompt: SYSTEM_PROMPTS[style] || SYSTEM_PROMPTS.professional,
  };
}

/**
 * Helper function to build context for prompts
 */
export function buildPromptContext(
  originalContent: string,
  options: Partial<PromptContext> = {}
): PromptContext {
  return {
    originalContent,
    maxLength: 280,
    ...options,
  };
}