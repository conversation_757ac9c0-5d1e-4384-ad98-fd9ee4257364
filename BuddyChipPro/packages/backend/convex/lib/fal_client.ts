/**
 * Fal.ai Client for Advanced Image Generation
 * Provides access to Flux Pro and other advanced models
 */

export interface FalConfig {
  apiKey: string;
  baseURL?: string;
  timeout?: number;
  maxRetries?: number;
}

export interface FalImageGenerationRequest {
  prompt: string;
  image_size?: 'square_hd' | 'square' | 'portrait_4_3' | 'portrait_16_9' | 'landscape_4_3' | 'landscape_16_9';
  num_inference_steps?: number;
  guidance_scale?: number;
  num_images?: number;
  enable_safety_checker?: boolean;
  seed?: number;
  expand_prompt?: boolean;
  format?: 'jpeg' | 'png';
  aspect_ratio?: string;
  raw?: boolean;
}

export interface FalImageGenerationResponse {
  images: Array<{
    url: string;
    width: number;
    height: number;
    content_type: string;
  }>;
  timings: {
    inference: number;
  };
  seed: number;
  has_nsfw_concepts: boolean[];
  prompt: string;
}

export interface FalImageResponse {
  url: string;
  width: number;
  height: number;
  contentType: string;
  seed: number;
  hasNsfwConcepts: boolean;
  inferenceTime: number;
  model: string;
  revisedPrompt?: string;
}

/**
 * Fal.ai client for advanced image generation
 */
export class FalClient {
  private config: Required<FalConfig>;

  constructor(config: FalConfig) {
    this.config = {
      baseURL: 'https://fal.run/fal-ai',
      timeout: 60000,
      maxRetries: 3,
      ...config,
    };
  }

  /**
   * Generate image using Flux Pro model
   */
  async generateImage(
    prompt: string,
    options: Partial<FalImageGenerationRequest> = {}
  ): Promise<FalImageResponse> {
    const requestPayload: FalImageGenerationRequest = {
      prompt,
      image_size: options.image_size || 'landscape_16_9',
      num_inference_steps: options.num_inference_steps || 28,
      guidance_scale: options.guidance_scale || 3.5,
      num_images: 1, // Generate one image at a time
      enable_safety_checker: options.enable_safety_checker ?? true,
      expand_prompt: options.expand_prompt ?? true,
      format: options.format || 'jpeg',
      ...options,
    };

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        console.log(`🎨 Fal.ai Flux Pro attempt ${attempt}/${this.config.maxRetries}`);
        
        const response = await this.makeRequest('/flux-pro/kontext', requestPayload);
        
        if (!response.images || response.images.length === 0) {
          throw new Error('No images returned from Fal.ai');
        }

        const image = response.images[0];
        
        return {
          url: image.url,
          width: image.width,
          height: image.height,
          contentType: image.content_type,
          seed: response.seed,
          hasNsfwConcepts: response.has_nsfw_concepts?.[0] || false,
          inferenceTime: response.timings?.inference || 0,
          model: 'fal-ai/flux-pro',
          revisedPrompt: response.prompt !== prompt ? response.prompt : undefined,
        };
      } catch (error) {
        lastError = error as Error;
        console.warn(`Fal.ai attempt ${attempt} failed:`, error);
        
        if (attempt < this.config.maxRetries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(`Fal.ai image generation failed after ${this.config.maxRetries} attempts: ${lastError?.message}`);
  }

  /**
   * Generate multiple image variations
   */
  async generateImageVariations(
    prompt: string,
    count: number = 3,
    options: Partial<FalImageGenerationRequest> = {}
  ): Promise<FalImageResponse[]> {
    const results: FalImageResponse[] = [];
    const maxConcurrent = 2; // Limit concurrent requests to avoid rate limits
    
    for (let i = 0; i < count; i += maxConcurrent) {
      const batch = Math.min(maxConcurrent, count - i);
      const promises: Promise<FalImageResponse>[] = [];
      
      for (let j = 0; j < batch; j++) {
        const variationOptions = {
          ...options,
          seed: options.seed ? options.seed + i + j : undefined,
        };
        promises.push(this.generateImage(prompt, variationOptions));
      }
      
      try {
        const batchResults = await Promise.all(promises);
        results.push(...batchResults);
      } catch (error) {
        console.error(`Batch ${i} failed:`, error);
        // Continue with individual fallbacks
        for (let j = 0; j < batch; j++) {
          try {
            const result = await this.generateImage(prompt, {
              ...options,
              seed: options.seed ? options.seed + i + j : undefined,
            });
            results.push(result);
          } catch (individualError) {
            console.error(`Individual variation ${i + j} failed:`, individualError);
          }
        }
      }
    }

    return results;
  }

  /**
   * Generate social media optimized image
   */
  async generateSocialMediaImage(
    prompt: string,
    options: {
      platform?: 'twitter' | 'instagram' | 'linkedin';
      style?: 'minimal' | 'vibrant' | 'professional' | 'artistic';
      aspectRatio?: 'square' | 'landscape' | 'portrait';
    } = {}
  ): Promise<FalImageResponse> {
    // Optimize prompt for social media
    let optimizedPrompt = prompt;
    
    // Add style modifiers
    if (options.style) {
      const styleModifiers = {
        minimal: 'clean, minimal design, simple composition, white space',
        vibrant: 'vibrant colors, energetic, dynamic composition, high contrast',
        professional: 'professional, clean, business-appropriate, sophisticated',
        artistic: 'artistic, creative, unique perspective, visually striking',
      };
      optimizedPrompt += `, ${styleModifiers[options.style]}`;
    }

    // Add platform-specific optimizations
    if (options.platform) {
      const platformOptimizations = {
        twitter: 'social media friendly, eye-catching, clear focal point, Twitter-optimized',
        instagram: 'Instagram-style, aesthetic, visually appealing, shareable, trendy',
        linkedin: 'professional networking, business context, informative, LinkedIn-appropriate',
      };
      optimizedPrompt += `, ${platformOptimizations[options.platform]}`;
    }

    // Determine image size based on platform and aspect ratio
    let imageSize: FalImageGenerationRequest['image_size'] = 'square_hd';
    
    if (options.aspectRatio === 'landscape' || options.platform === 'twitter') {
      imageSize = 'landscape_16_9';
    } else if (options.aspectRatio === 'portrait') {
      imageSize = 'portrait_4_3';
    } else if (options.platform === 'instagram') {
      imageSize = 'square_hd';
    }

    return this.generateImage(optimizedPrompt, {
      image_size: imageSize,
      num_inference_steps: 35, // Higher quality for social media
      guidance_scale: 4.0,
      expand_prompt: true,
      format: 'jpeg',
    });
  }

  /**
   * Make HTTP request to Fal.ai API
   */
  private async makeRequest(endpoint: string, payload: any): Promise<FalImageGenerationResponse> {
    const url = `${this.config.baseURL}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Key ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json() as FalImageGenerationResponse;
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`Request timeout after ${this.config.timeout}ms`);
      }
      
      throw error;
    }
  }

  /**
   * Test connection to Fal.ai
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.generateImage('A simple test image of a colorful abstract pattern', {
        image_size: 'square',
        num_inference_steps: 20,
        guidance_scale: 3.0,
      });
      return true;
    } catch (error) {
      console.error('Fal.ai connection test failed:', error);
      return false;
    }
  }

  /**
   * Get model capabilities and pricing info
   */
  getModelInfo() {
    return {
      model: 'fal-ai/flux-pro',
      provider: 'Fal.ai',
      capabilities: {
        maxResolution: '2048x2048',
        supportedFormats: ['jpeg', 'png'],
        supportedAspectRatios: [
          'square', 'square_hd', 'portrait_4_3', 'portrait_16_9', 
          'landscape_4_3', 'landscape_16_9'
        ],
        maxInferenceSteps: 50,
        guidanceScaleRange: [1.0, 20.0],
        expandPrompt: true,
        safetyChecker: true,
      },
      pricing: {
        estimatedCostPerImage: 0.055, // USD, approximate
        currency: 'USD',
      },
    };
  }
}

/**
 * Initialize Fal.ai client with environment variables
 */
export function createFalClient(): FalClient {
  const apiKey = process.env.FAL_API_KEY;
  
  if (!apiKey) {
    throw new Error(
      'FAL_API_KEY environment variable is required. ' +
      'Get your API key from https://fal.ai/dashboard'
    );
  }

  return new FalClient({
    apiKey,
  });
}

/**
 * Singleton instance for use across the application
 */
let falClient: FalClient | null = null;

export function getFalClient(): FalClient {
  if (!falClient) {
    falClient = createFalClient();
  }
  return falClient;
}