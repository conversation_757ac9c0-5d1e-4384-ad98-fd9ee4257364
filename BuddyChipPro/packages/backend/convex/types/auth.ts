/**
 * Authentication and user-related type definitions
 */

import { Id } from "../_generated/dataModel";

export interface UserIdentity {
  subject: string;
  name?: string;
  email?: string;
}

export interface AuthContext {
  auth: {
    getUserIdentity(): Promise<UserIdentity | null>;
  };
  db: {
    query(table: string): any;
    insert(table: string, document: any): Promise<Id<any>>;
    patch(id: Id<any>, fields: any): Promise<void>;
  };
}

export interface UserDoc {
  _id: Id<"users">;
  name: string;
  email: string;
  clerkId: string;
  image?: string;
  primaryWalletId?: Id<"wallets">;
  createdAt: number;
  updatedAt?: number;
}

export async function getUserId(ctx: AuthContext): Promise<Id<"users"> | null> {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    return null;
  }
  
  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", identity.subject))
    .first();
    
  return user?._id || null;
}