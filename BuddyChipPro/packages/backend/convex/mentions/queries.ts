import { query, internalQuery } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import type { Doc, Id } from "../_generated/dataModel";
import type { QueryCtx } from "../_generated/server";
import { 
  projectLightweightMention, 
  projectMentionSummaries, 
  projectResponseList 
} from "../lib/projections";
import type { 
  LightweightMention, 
  PaginatedResult 
} from "../types/optimized";

// Type aliases for better readability and type safety
type Mention = Doc<"mentions">;
type TwitterAccount = Doc<"twitterAccounts">;
type User = Doc<"users">;
type Response = Doc<"responses">;

// Helper type for priority levels
type Priority = "high" | "medium" | "low";

// Helper type for mention types
type MentionType = "mention" | "reply" | "quote" | "retweet_with_comment";

// Security helper function to get authenticated user and their Twitter accounts
async function getAuthenticatedUserAccounts(ctx: QueryCtx): Promise<{ user: User; accounts: TwitterAccount[] }> {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Authentication required");
  }
  
  const user: User | null = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
    .first();
    
  if (!user) {
    throw new Error("User not found");
  }
  
  const accounts: TwitterAccount[] = await ctx.db
    .query("twitterAccounts")
    .withIndex("by_user", (q) => q.eq("userId", user._id))
    .take(50); // 🚀 BANDWIDTH FIX: Reasonable limit for user accounts
    
  return { user, accounts };
}

// Security helper to verify account ownership
async function verifyAccountOwnership(accountId: string, userAccounts: TwitterAccount[]): Promise<boolean> {
  return userAccounts.some(account => account._id === accountId);
}

// Helper function to filter mentions by user's accounts
function filterMentionsByUserAccounts(mentions: Mention[], userAccounts: TwitterAccount[]): Mention[] {
  const accountIds = new Set(userAccounts.map(account => account._id));
  return mentions.filter(mention => accountIds.has(mention.monitoredAccountId));
}

export const getMentionStats = query({
  args: {
    monitoredAccountId: v.optional(v.id("twitterAccounts")),
  },
  returns: v.object({
    total: v.number(),
    unread: v.number(),
    unprocessed: v.number(),
    todayCount: v.number(),
    weekCount: v.number(),
    responseOpportunities: v.number(),
    priorityBreakdown: v.object({
      high: v.number(),
      medium: v.number(),
      low: v.number(),
    }),
    mentionTypes: v.object({
      mention: v.number(),
      reply: v.number(),
      quote: v.number(),
      retweet_with_comment: v.number(),
    }),
  }),
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user and get their accounts
      const { user, accounts } = await getAuthenticatedUserAccounts(ctx);
      
      // 🔐 SECURITY: If specific account requested, verify ownership
      if (args.monitoredAccountId) {
        const hasAccess = await verifyAccountOwnership(args.monitoredAccountId, accounts);
        if (!hasAccess) {
          throw new Error("Access denied: Account not owned by user");
        }
      }
      
      // 🚀 PERFORMANCE FIX: Eliminate N+1 query pattern with batch query
      const MENTION_STATS_LIMIT = 1000; // Limit for statistics calculation
      const accountIds = accounts.map(account => account._id);

      // Single optimized query instead of N separate queries
      let allUserMentions: Mention[] = [];
      if (accountIds.length > 0) {
        // Use a single query with filter to get mentions for all accounts
        const allMentions = await ctx.db
          .query("mentions")
          .withIndex("by_monitored_account")
          .order("desc")
          .take(MENTION_STATS_LIMIT * accountIds.length); // Scale limit by account count

        // Filter to only include mentions for user's accounts
        allUserMentions = allMentions.filter(mention =>
          accountIds.includes(mention.monitoredAccountId)
        );
      }
      
      // Filter to specific account if requested
      const mentions = args.monitoredAccountId 
        ? allUserMentions.filter(m => m.monitoredAccountId === args.monitoredAccountId)
        : allUserMentions;
    
      const now = Date.now();
      const oneDayAgo = now - 24 * 60 * 60 * 1000;
      const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;

      const todayMentions = mentions.filter((m: Mention) => m.discoveredAt >= oneDayAgo);
      const weekMentions = mentions.filter((m: Mention) => m.discoveredAt >= oneWeekAgo);
      const unreadMentions = mentions.filter((m: Mention) => !m.isNotificationSent);
      const unprocessedMentions = mentions.filter((m: Mention) => !m.isProcessed);

      // Priority breakdown
      const highPriority = mentions.filter((m: Mention) => m.priority === "high").length;
      const mediumPriority = mentions.filter((m: Mention) => m.priority === "medium").length;
      const lowPriority = mentions.filter((m: Mention) => m.priority === "low").length;

      // Response opportunity count
      const responseOpportunities = mentions.filter((m: Mention) => 
        m.aiAnalysisResult?.shouldRespond && !m.isProcessed
      ).length;

      const stats = {
        total: mentions.length,
        unread: unreadMentions.length,
        unprocessed: unprocessedMentions.length,
        todayCount: todayMentions.length,
        weekCount: weekMentions.length,
        responseOpportunities,
        priorityBreakdown: {
          high: highPriority,
          medium: mediumPriority,
          low: lowPriority,
        },
        mentionTypes: {
          mention: mentions.filter((m: Mention) => m.mentionType === "mention").length,
          reply: mentions.filter((m: Mention) => m.mentionType === "reply").length,
          quote: mentions.filter((m: Mention) => m.mentionType === "quote").length,
          retweet_with_comment: mentions.filter((m: Mention) => m.mentionType === "retweet_with_comment").length,
        },
      };
      
      return stats;
      
    } catch (error) {
      console.error('Error in getMentionStats:', error);
      // 🔐 SECURITY: Don't leak sensitive error information
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return {
        total: 0,
        unread: 0,
        unprocessed: 0,
        todayCount: 0,
        weekCount: 0,
        responseOpportunities: 0,
        priorityBreakdown: {
          high: 0,
          medium: 0,
          low: 0,
        },
        mentionTypes: {
          mention: 0,
          reply: 0,
          quote: 0,
          retweet_with_comment: 0,
        },
      };
    }
  },
});

export const getRecentMentions = query({
  args: {
    limit: v.optional(v.number()),
    priority: v.optional(v.union(v.literal("high"), v.literal("medium"), v.literal("low"))),
    monitoredAccountId: v.optional(v.id("twitterAccounts")),
    processed: v.optional(v.boolean()),
    timeRange: v.optional(v.union(v.literal("24h"), v.literal("7d"), v.literal("30d"))),
    cursor: v.optional(v.string()),
  },
  returns: v.object({
    data: v.array(v.object({
      _id: v.id("mentions"),
      mentionTweetId: v.string(),
      mentionContent: v.string(),
      mentionAuthor: v.string(),
      mentionAuthorHandle: v.string(),
      mentionAuthorFollowers: v.number(),
      mentionAuthorVerified: v.boolean(),
      monitoredAccountId: v.id("twitterAccounts"),
      mentionType: v.union(v.literal("mention"), v.literal("reply"), v.literal("quote"), v.literal("retweet_with_comment")),
      engagement: v.object({
        likes: v.number(),
        retweets: v.number(),
        replies: v.number(),
        views: v.optional(v.number()),
      }),
      priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")),
      isProcessed: v.boolean(),
      isNotificationSent: v.boolean(),
      createdAt: v.number(),
      discoveredAt: v.number(),
      url: v.string(),
    })),
    nextCursor: v.union(v.string(), v.null()),
    hasMore: v.boolean(),
  }),
  handler: async (ctx, args) => {
    try {
      const { user, accounts } = await getAuthenticatedUserAccounts(ctx);
      const realLimit = Math.min(Math.max(args.limit || 50, 1), 100);

      let startDate: number | undefined;
      if (args.timeRange) {
        const now = Date.now();
        switch (args.timeRange) {
          case "24h": startDate = now - 24 * 60 * 60 * 1000; break;
          case "7d": startDate = now - 7 * 24 * 60 * 60 * 1000; break;
          case "30d": startDate = now - 30 * 24 * 60 * 60 * 1000; break;
        }
      }

      // Scenario 1: Specific monitoredAccountId provided (ideal for pagination)
      if (args.monitoredAccountId) {
        const hasAccess = await verifyAccountOwnership(args.monitoredAccountId, accounts);
        if (!hasAccess) throw new Error("Access denied: Account not owned by user");

        let query = ctx.db
          .query("mentions")
          .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", args.monitoredAccountId!));

        if (args.priority) {
          query = query.filter((q) => q.eq(q.field("priority"), args.priority));
        }
        if (args.processed !== undefined) {
          query = query.filter((q) => q.eq(q.field("isProcessed"), args.processed));
        }
        if (startDate !== undefined) {
          query = query.filter((q) => q.gte(q.field("createdAt"), startDate!));
        }

        const orderedQuery = query.order("desc");

        let paginatedQuery = orderedQuery;
        if (args.cursor) {
          paginatedQuery = orderedQuery.filter((q) => q.lt(q.field("_creationTime"), parseFloat(args.cursor!)));
        }

        const items = await paginatedQuery.take(realLimit + 1);
        const hasMore = items.length > realLimit;
        const nextCursor = hasMore ? items[realLimit]._creationTime.toString() : null;

        const mappedData = items.slice(0, realLimit).map(mention => ({
          _id: mention._id,
          mentionTweetId: mention.mentionTweetId,
          mentionContent: mention.mentionContent.slice(0, 200),
          mentionAuthor: mention.mentionAuthor,
          mentionAuthorHandle: mention.mentionAuthorHandle,
          mentionAuthorFollowers: mention.mentionAuthorFollowers,
          mentionAuthorVerified: mention.mentionAuthorVerified,
          monitoredAccountId: mention.monitoredAccountId,
          mentionType: mention.mentionType,
          engagement: mention.engagement,
          priority: mention.priority,
          isProcessed: mention.isProcessed,
          isNotificationSent: mention.isNotificationSent,
          createdAt: mention.createdAt,
          discoveredAt: mention.discoveredAt,
          url: mention.url,
        }));

        return {
          data: mappedData,
          nextCursor,
          hasMore,
        };

      } else {
        // Scenario 2: No specific monitoredAccountId (fetch for all user accounts)
        let collectedMentions: Mention[] = [];
        for (const account of accounts) {
          let accountQuery = ctx.db
            .query("mentions")
            .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", account._id));

          if (args.priority) {
            accountQuery = accountQuery.filter((q) => q.eq(q.field("priority"), args.priority));
          }
          if (args.processed !== undefined) {
            accountQuery = accountQuery.filter((q) => q.eq(q.field("isProcessed"), args.processed));
          }
          if (startDate !== undefined) {
            accountQuery = accountQuery.filter((q) => q.gte(q.field("createdAt"), startDate!));
          }
          const orderedAccountQuery = accountQuery.order("desc");
          const accountMentions = await orderedAccountQuery.take(realLimit);
          collectedMentions.push(...accountMentions);
        }

        collectedMentions.sort((a, b) => b.createdAt - a.createdAt);

        const finalSlicedMentions = collectedMentions.slice(0, realLimit);
        const hasMoreOverall = collectedMentions.length > realLimit;

        const mappedData = finalSlicedMentions.map(mention => ({
          _id: mention._id,
          mentionTweetId: mention.mentionTweetId,
          mentionContent: mention.mentionContent.slice(0, 200),
          mentionAuthor: mention.mentionAuthor,
          mentionAuthorHandle: mention.mentionAuthorHandle,
          mentionAuthorFollowers: mention.mentionAuthorFollowers,
          mentionAuthorVerified: mention.mentionAuthorVerified,
          monitoredAccountId: mention.monitoredAccountId,
          mentionType: mention.mentionType,
          engagement: mention.engagement,
          priority: mention.priority,
          isProcessed: mention.isProcessed,
          isNotificationSent: mention.isNotificationSent,
          createdAt: mention.createdAt,
          discoveredAt: mention.discoveredAt,
          url: mention.url,
        }));

        return {
          data: mappedData,
          nextCursor: null,
          hasMore: hasMoreOverall,
        };
      }
    } catch (error) {
      console.error('Error in getRecentMentions:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return { data: [], nextCursor: null, hasMore: false };
    }
  },
});

export const getMentionResponses = query({
  args: {
    mentionId: v.optional(v.id("mentions")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user and get their accounts
      const { user, accounts } = await getAuthenticatedUserAccounts(ctx);
      
      // 🔐 SECURITY: Input validation
      const limit = Math.min(Math.max(args.limit || 20, 1), 100);
      
      // If specific mention requested, verify it belongs to user's accounts
      if (args.mentionId) {
        const mention = await ctx.db.get(args.mentionId);
        if (!mention) {
          throw new Error("Mention not found");
        }
        
        const hasAccess = await verifyAccountOwnership(mention.monitoredAccountId, accounts);
        if (!hasAccess) {
          throw new Error("Access denied: Mention not owned by user");
        }
      }
      
      // Get responses only for user's mentions
      const accountIds = accounts.map(account => account._id);
      let allResponses: Response[] = [];
      
      // 🚀 BANDWIDTH OPTIMIZED: Get mentions and responses with strict limits
      const MENTION_RESPONSES_LIMIT = 200; // Limit mentions per account
      const RESPONSES_PER_MENTION_LIMIT = 10; // Limit responses per mention
      
      for (const accountId of accountIds) {
        const accountMentions: Mention[] = await ctx.db
          .query("mentions")
          .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", accountId))
          .order("desc")
          .take(MENTION_RESPONSES_LIMIT); // 🚀 BANDWIDTH FIX: Limit mentions per account
          
        // Get responses for each mention with limits
        for (const mention of accountMentions) {
          if (!args.mentionId || mention._id === args.mentionId) {
            const mentionResponses: Response[] = await ctx.db
              .query("responses")
              .withIndex("by_target", (q) => 
                q.eq("targetType", "mention").eq("targetId", mention._id)
              )
              .order("desc")
              .take(RESPONSES_PER_MENTION_LIMIT); // 🚀 BANDWIDTH FIX: Limit responses per mention
            allResponses.push(...mentionResponses);
          }
        }
      }
      
      const sortedResponses = allResponses
        .sort((a, b) => b._creationTime - a._creationTime)
        .slice(0, limit);

      const lightweightResponses = sortedResponses.map(response => ({
        _id: response._id,
        targetType: response.targetType,
        targetId: response.targetId,
        userId: response.userId,
        content: response.content ? response.content.slice(0, 280) : "", // Truncate content
        style: response.style,
        status: response.status,
        createdAt: response._creationTime, // Using _creationTime as createdAt for response
        characterCount: response.characterCount,
      }));

      return lightweightResponses;
        
    } catch (error) {
      console.error('Error in getMentionResponses:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return [];
    }
  },
});

/**
 * Get mention by ID with all details
 * Used by AI processing functions
 */
export const getMentionById = query({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    const mention: Mention | null = await ctx.db.get(args.mentionId);
    return mention;
  },
});

/**
 * Get the latest mention for a specific account
 * Used to determine the last time we checked for mentions
 */
export const getLatestMentionForAccount = query({
  args: {
    accountId: v.id("twitterAccounts"),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user and get their accounts
      const { user, accounts } = await getAuthenticatedUserAccounts(ctx);
      
      // 🔐 SECURITY: Verify account ownership
      const hasAccess = await verifyAccountOwnership(args.accountId, accounts);
      if (!hasAccess) {
        throw new Error("Access denied: Account not owned by user");
      }
      
      const mention: Mention | null = await ctx.db
        .query("mentions")
        .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", args.accountId))
        .order("desc")
        .first();

      return mention;
      
    } catch (error) {
      console.error('Error in getLatestMentionForAccount:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get a mention by its Twitter tweet ID
 * Used to check for duplicates before storing new mentions
 */
export const getMentionByTweetId = query({
  args: {
    tweetId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // 🔐 SECURITY: Authenticate user and get their accounts
      const { user, accounts } = await getAuthenticatedUserAccounts(ctx);
      
      // 🔐 SECURITY: Input validation
      if (!args.tweetId || args.tweetId.trim().length === 0) {
        throw new Error("Tweet ID is required");
      }
      
      const tweetId = args.tweetId.trim();
      
      // Find mention and verify it belongs to user's accounts
      const mention: Mention | null = await ctx.db
        .query("mentions")
        .filter(q => q.eq(q.field("mentionTweetId"), tweetId))
        .first();
        
      if (!mention) {
        return null;
      }
      
      // 🔐 SECURITY: Verify mention belongs to user's account
      const hasAccess = await verifyAccountOwnership(mention.monitoredAccountId, accounts);
      if (!hasAccess) {
        // Don't reveal existence of mention for non-owned accounts
        return null;
      }

      return mention;
      
    } catch (error) {
      console.error('Error in getMentionByTweetId:', error);
      if (error instanceof Error && (error.message.includes("Authentication required") || error.message.includes("Access denied") || error.message.includes("Tweet ID"))) {
        throw error;
      }
      return null;
    }
  },
});

/**
 * Get mentions for sentiment analysis (no authentication required)
 * Used for batch processing sentiment analysis
 */
export const getMentionsForSentimentAnalysis = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;
    
    // Get mentions that haven't been sentiment analyzed yet
    const mentions = await ctx.db
      .query("mentions")
      .withIndex("by_discovered_at")
      .order("desc")
      .filter(q => q.eq(q.field("sentimentAnalysis"), undefined))
      .take(limit);
    
    return mentions;
  },
});

/**
 * Get a Twitter account by ID (no authentication required)
 * Used for sentiment analysis processing
 */
export const getTwitterAccountById = query({
  args: {
    accountId: v.id("twitterAccounts"),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db.get(args.accountId);
    return account;
  },
});

/**
 * Get unprocessed mentions for sentiment analysis (internal function)
 */
export const getUnprocessedMentionsForSentiment = internalQuery({
  args: {
    limit: v.optional(v.number()),
    accountId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    let query = ctx.db.query("mentions");
    
    // Filter by account if specified
    if (args.accountId) {
      // Get the account first to use in filter
      const account = await ctx.db.get(args.accountId as any);
      if (account) {
        query = query.filter((q) => q.eq(q.field("monitoredAccountId"), account._id));
      }
    }
    
    // Get mentions without sentiment analysis
    const mentions = await query
      .filter((q) => q.eq(q.field("sentimentAnalysis"), undefined))
      .order("desc")
      .take(limit);

    return mentions;
  },
});