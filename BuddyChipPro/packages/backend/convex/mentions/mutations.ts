import { mutation, action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { getAIFallbackClient } from "../lib/ai_fallback_client";
import { getResponseTemplate, buildPromptContext } from "../lib/prompt_templates";
import { createTweetIOClient } from "../lib/twitter_client";
import { Id } from "../_generated/dataModel";
import { ActionCtx, MutationCtx } from "../_generated/server";
import type { TwitterUser as TwitterUserAPI } from "../types/twitter";
import { 
  resolveUsername, 
  sanitizeMentionData, 
  convertTwitterIOUserWithFallback,
  resolveAuthorWithApiLookup,
  batchResolveAuthors
} from "../lib/twitter_utils";

// Type Definitions for clarity and safety
type TwitterUser = TwitterUserAPI;

interface Tweet {
  id: string;
  text: string;
  author_id: string;
  in_reply_to_user_id?: string;
  referenced_tweets?: { type: string; id: string }[];
  conversation_id?: string;
  public_metrics?: {
    like_count: number;
    retweet_count: number;
    reply_count: number;
    impression_count?: number;
  };
  created_at: string;
}

interface TwitterAccountFromDB {
  _id: Id<"twitterAccounts">;
  handle: string;
  displayName?: string;
  followersCount?: number;
  verified?: boolean;
  isActive?: boolean;
  isMonitoringEnabled?: boolean;
  lastChecked?: number;
  priority?: number;
}

interface MentionMetrics {
  totalProcessingTime: number;
  accountsProcessed: number;
  mentionsFound: number;
  mentionsStored: number;
  duplicatesSkipped: number;
  errors: number;
  cacheHits: number;
  apiCalls: number;
}

type ProcessAccountMentionsContext = Pick<ActionCtx, "runQuery" | "runMutation" | "auth">;

export const updateMentionProcessingStatus = mutation({
  args: {
    mentionId: v.id("mentions"),
    isProcessed: v.boolean(),
    aiAnalysisResult: v.optional(v.object({
      shouldRespond: v.boolean(),
      responseStrategy: v.optional(v.string()),
      sentiment: v.optional(v.string()),
      topics: v.optional(v.array(v.string())),
      confidence: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    const { mentionId, aiAnalysisResult, ...updates } = args;

    const updateData: Record<string, unknown> = {
      ...updates,
      processedAt: Date.now(),
    };

    if (aiAnalysisResult) {
      updateData.aiAnalysisResult = aiAnalysisResult;
    }

    await ctx.db.patch(mentionId, updateData);

    return { success: true };
  },
});

export const markNotificationSent = mutation({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.mentionId, {
      isNotificationSent: true,
      notificationSentAt: Date.now(),
    });

    return { success: true };
  },
});

export const updateMentionPriority = mutation({
  args: {
    mentionId: v.id("mentions"),
    priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.mentionId, {
      priority: args.priority,
    });

    return { success: true };
  },
});

export const updateMonitoringSettings = mutation({
  args: {
    twitterAccountId: v.id("twitterAccounts"),
    isMonitoringEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { twitterAccountId, ...updates } = args;

    await ctx.db.patch(twitterAccountId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

export const bulkUpdateMentionNotifications = mutation({
  args: {
    mentionIds: v.array(v.id("mentions")),
    isNotificationSent: v.boolean(),
  },
  handler: async (ctx, args) => {
    const timestamp = Date.now();

    for (const mentionId of args.mentionIds) {
      await ctx.db.patch(mentionId, {
        isNotificationSent: args.isNotificationSent,
        notificationSentAt: args.isNotificationSent ? timestamp : undefined,
      });
    }

    return { 
      success: true, 
      updated: args.mentionIds.length 
    };
  },
});

export const markAllMentionsAsRead = mutation({
  args: {
    monitoredAccountId: v.optional(v.id("twitterAccounts")),
  },
  handler: async (ctx, args) => {
    const timestamp = Date.now();

    // Get all unread mentions
    let query = ctx.db.query("mentions")
      .withIndex("by_notification", (q) => q.eq("isNotificationSent", false));

    let unreadMentions = await query.collect();

    // Filter by monitored account if specified
    if (args.monitoredAccountId) {
      unreadMentions = unreadMentions.filter(m => m.monitoredAccountId === args.monitoredAccountId);
    }

    // Mark all as read
    const updatePromises = unreadMentions.map(mention =>
      ctx.db.patch(mention._id, {
        isNotificationSent: true,
        notificationSentAt: timestamp,
      })
    );

    await Promise.all(updatePromises);

    return { 
      success: true, 
      updated: unreadMentions.length 
    };
  },
});

export const deleteMention = mutation({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.mentionId);
    return { success: true };
  },
});

export const storeMention = mutation({
  args: {
    mentionTweetId: v.string(),
    mentionContent: v.string(),
    mentionAuthor: v.string(),
    mentionAuthorHandle: v.string(),
    mentionAuthorFollowers: v.optional(v.number()),
    mentionAuthorVerified: v.optional(v.boolean()),
    monitoredAccountId: v.id("twitterAccounts"),
    mentionType: v.union(
      v.literal("mention"),
      v.literal("reply"),
      v.literal("quote"),
      v.literal("retweet_with_comment")
    ),
    originalTweetId: v.optional(v.string()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")),
    createdAt: v.number(),
    url: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if mention already exists
    const existing = await ctx.db
      .query("mentions")
      .filter(q => q.eq(q.field("mentionTweetId"), args.mentionTweetId))
      .first();

    if (existing) {
      // Update engagement metrics
      await ctx.db.patch(existing._id, {
        engagement: args.engagement,
        discoveredAt: Date.now(), // Update discovery time
      });
      return existing._id;
    }

    // Create new mention
    const mentionId = await ctx.db.insert("mentions", {
      ...args,
      isProcessed: false,
      isNotificationSent: false,
      discoveredAt: Date.now(),
    });

    return mentionId;
  },
});

export const storeMentionWithCheck = mutation({
  args: {
    mentionTweetId: v.string(),
    mentionContent: v.string(),
    mentionAuthor: v.string(),
    mentionAuthorHandle: v.string(),
    mentionAuthorFollowers: v.optional(v.number()),
    mentionAuthorVerified: v.optional(v.boolean()),
    monitoredAccountId: v.id("twitterAccounts"),
    mentionType: v.union(
      v.literal("mention"),
      v.literal("reply"),
      v.literal("quote"),
      v.literal("retweet_with_comment")
    ),
    originalTweetId: v.optional(v.string()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    priority: v.union(v.literal("high"), v.literal("medium"), v.literal("low")),
    createdAt: v.number(),
    url: v.optional(v.string()),
    forceUpdate: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Check if mention already exists
    const existing = await ctx.db
      .query("mentions")
      .filter(q => q.eq(q.field("mentionTweetId"), args.mentionTweetId))
      .first();

    if (existing) {
      // If force update is enabled or engagement has changed significantly
      const engagementChanged = args.forceUpdate || 
        existing.engagement.likes !== args.engagement.likes ||
        existing.engagement.retweets !== args.engagement.retweets ||
        existing.engagement.replies !== args.engagement.replies;

      if (engagementChanged) {
        // Update engagement metrics and refresh discovery time
        await ctx.db.patch(existing._id, {
          engagement: args.engagement,
          discoveredAt: Date.now(),
          priority: args.priority, // Update priority as it may have changed
        });
        
        return { 
          mentionId: existing._id, 
          action: 'updated',
          changes: {
            engagement: true,
            priority: existing.priority !== args.priority,
          }
        };
      } else {
        // No significant changes, return existing
        return { 
          mentionId: existing._id, 
          action: 'exists',
          changes: {}
        };
      }
    }

    // Create new mention with comprehensive duplicate check
    const mentionId = await ctx.db.insert("mentions", {
      mentionTweetId: args.mentionTweetId,
      mentionContent: args.mentionContent,
      mentionAuthor: args.mentionAuthor,
      mentionAuthorHandle: args.mentionAuthorHandle,
      mentionAuthorFollowers: args.mentionAuthorFollowers,
      mentionAuthorVerified: args.mentionAuthorVerified,
      monitoredAccountId: args.monitoredAccountId,
      mentionType: args.mentionType,
      originalTweetId: args.originalTweetId,
      engagement: args.engagement,
      priority: args.priority,
      createdAt: args.createdAt,
      url: args.url,
      isProcessed: false,
      isNotificationSent: false,
      discoveredAt: Date.now(),
    });

    // Schedule sentiment analysis for the new mention (if ai queries exist)
    try {
      ctx.scheduler.runAfter(0, api.mentions.mutations.processMentionSentiment, {
        mentionId,
      });
    } catch (error) {
      console.log("Sentiment analysis scheduling skipped:", error);
    }

    return { 
      mentionId, 
      action: 'created',
      changes: {
        created: true,
      }
    };
  },
});

export const updateMentionEngagement = mutation({
  args: {
    mentionId: v.id("mentions"),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.mentionId, {
      engagement: args.engagement,
      discoveredAt: Date.now(), // Update to mark as refreshed
    });

    return { success: true };
  },
});

/**
 * Simple action for users to fetch their latest mentions from Twitter
 * This function fetches real mentions using TwitterAPI.io
 */
export const fetchUserMentions = action({
  args: {
    accountHandle: v.optional(v.string()),
    hoursBack: v.optional(v.number()),
    maxMentions: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    try {
      // Get authenticated user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("Authentication required");
      }

      const user = await ctx.runQuery(api.users.getCurrentUser);
      if (!user) {
        throw new Error("User not found");
      }

      // Get user's Twitter accounts
      const twitterAccounts = await ctx.runQuery(api.users.getUserTwitterAccounts);
      let activeAccounts: TwitterAccountFromDB[] = twitterAccounts?.filter((account: TwitterAccountFromDB) => 
        account.isActive && account.isMonitoringEnabled !== false
      ) || [];

      // Filter by specific account if requested
      if (args.accountHandle) {
        activeAccounts = activeAccounts.filter((account) => 
          account.handle.toLowerCase() === (args.accountHandle as string).toLowerCase()
        );
      }

      if (activeAccounts.length === 0) {
        return {
          success: true,
          newMentions: 0,
          message: args.accountHandle 
            ? `No active Twitter account found for handle @${args.accountHandle}`
            : "No active Twitter accounts found. Please add Twitter accounts first.",
          accountsChecked: 0,
        };
      }

      // Initialize TwitterAPI.io client
      const { createTweetIOClient } = await import("../lib/twitter_client");
      const { getTweetIOConfig } = await import("../lib/config");
      
      const config = getTweetIOConfig();
      const twitterClient = createTweetIOClient(config.apiKey, ctx as any);

      const lookbackHours = args.hoursBack || 24;
      let totalNewMentions = 0;
      const accountResults = [];

      // Process each account
      for (const account of activeAccounts) {
        try {
          // Get recent mentions for this account
          const lastMention = await ctx.runQuery(api.mentions.queries.getLatestMentionForAccount, {
            accountId: account._id
          });

          // Calculate search window
          const baseTime = Date.now() - (lookbackHours * 60 * 60 * 1000);
          const searchStartTime = lastMention 
            ? new Date(Math.max(lastMention.discoveredAt - (30 * 60 * 1000), baseTime)).toISOString()
            : new Date(baseTime).toISOString();

          // Fetch mentions from TwitterAPI.io
          const mentionResults = await twitterClient.searchMentions(account.handle, {
            maxResults: args.maxMentions || 30,
            startTime: searchStartTime,
          });

          const { tweets: mentionTweets, users: mentionUsers }: { tweets: Tweet[], users: TwitterUserAPI[] } = mentionResults;
          let newMentionsForAccount = 0;

          // Process each mention
          for (const tweet of mentionTweets) {
            // Check if we already have this mention
            const existingMention = await ctx.runQuery(api.mentions.queries.getMentionByTweetId, {
              tweetId: tweet.id
            });

            if (existingMention) {
              continue;
            }

            // PHASE 1 FIX: Never skip mentions, always use fallback data
            let author = mentionUsers.find((u) => u.id === tweet.author_id);
            let authorUsername: string;
            let authorName: string;
            
            if (!author || !author.username) {
              console.warn(`⚠️ Missing author data for mention ${tweet.id}, using fallback resolution`, { 
                authorId: tweet.author_id, 
                author: author ? 'found but no username' : 'not found',
                authorData: author 
              });
              
              // Generate fallback author data instead of skipping
              authorUsername = resolveUsername({
                user: author,
                tweet: tweet as any, // Cast to TwitterTweet for compatibility
                authorId: tweet.author_id,
                displayName: author?.name
              });
              
              authorName = author?.name || `Unknown User (${tweet.author_id.substring(0, 8)})`;
              
              // Create fallback author object if none exists
              if (!author) {
                author = {
                  id: tweet.author_id,
                  name: authorName,
                  username: authorUsername,
                  followers_count: 0,
                  verified: false,
                } as TwitterUserAPI;
              } else {
                // Update existing author with resolved username
                author.username = authorUsername;
              }
            } else {
              authorUsername = author.username;
              authorName = author.name;
            }

            // Determine mention type
            let mentionType: "mention" | "reply" | "quote" | "retweet_with_comment" = "mention";
            if (tweet.in_reply_to_user_id) {
              mentionType = "reply";
            } else if (tweet.referenced_tweets?.some((ref) => ref.type === "quoted")) {
              mentionType = "quote";
            } else if (tweet.referenced_tweets?.some((ref) => ref.type === "retweeted")) {
              mentionType = "retweet_with_comment";
            }

            // Calculate priority
            const { calculateMentionPriority } = await import("../lib/twitter_client");
            const priority = calculateMentionPriority(author);

            // Store the mention with sanitized data
            const rawMentionData = {
              mentionTweetId: tweet.id,
              mentionContent: tweet.text,
              mentionAuthor: authorName,
              mentionAuthorHandle: authorUsername,
              mentionAuthorFollowers: author.followers_count || 0,
              mentionAuthorVerified: author.verified || false,
              monitoredAccountId: account._id,
              mentionType,
              originalTweetId: tweet.conversation_id && tweet.conversation_id !== tweet.id ? tweet.conversation_id : undefined,
              engagement: {
                likes: tweet.public_metrics?.like_count || 0,
                retweets: tweet.public_metrics?.retweet_count || 0,
                replies: tweet.public_metrics?.reply_count || 0,
                views: tweet.public_metrics?.impression_count || 0,
              },
              priority,
              createdAt: new Date(tweet.created_at).getTime(),
              url: `https://twitter.com/${authorUsername}/status/${tweet.id}`,
            };

            // PHASE 1 FIX: Sanitize and validate mention data before storage
            const { isValid, sanitized: mentionData, warnings } = sanitizeMentionData(rawMentionData);
            
            if (warnings.length > 0) {
              console.log(`📋 Mention ${tweet.id} data sanitized:`, warnings);
            }
            
            if (!isValid) {
              console.error(`❌ Failed to sanitize mention ${tweet.id}, skipping`, { rawMentionData });
              continue; // Only skip if data is fundamentally invalid
            }

            const result = await ctx.runMutation(api.mentions.mutations.storeMentionWithCheck, mentionData);
            
            if (result.action === 'created') {
              newMentionsForAccount++;
              totalNewMentions++;
            }
          }

          accountResults.push({
            handle: account.handle,
            displayName: account.displayName,
            newMentions: newMentionsForAccount,
            totalChecked: mentionTweets.length,
            searchStartTime,
          });

        } catch (accountError) {
          console.error(`Error processing @${account.handle}:`, accountError);
          accountResults.push({
            handle: account.handle,
            displayName: account.displayName,
            newMentions: 0,
            totalChecked: 0,
            error: accountError instanceof Error ? accountError.message : String(accountError),
          });
        }
      }

      return {
        success: true,
        newMentions: totalNewMentions,
        message: totalNewMentions === 0 
          ? "No new mentions found!" 
          : `Found ${totalNewMentions} new mentions!`,
        accountsChecked: activeAccounts.length,
        accountResults,
        timestamp: Date.now(),
        lookbackHours,
      };

    } catch (error) {
      console.error('Error fetching user mentions:', error);
      return {
        success: false,
        newMentions: 0,
        message: `Failed to fetch mentions: ${error instanceof Error ? error.message : String(error)}`,
        accountsChecked: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

/**
 * Process sentiment analysis for a mention
 * Automatically called when new mentions are created
 */
export const processMentionSentiment = action({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    try {
      // Get the mention data
      const mention = await ctx.runQuery(api.mentions.queries.getMentionById, {
        mentionId: args.mentionId,
      });

      if (!mention) {
        console.error("Mention not found for sentiment analysis:", args.mentionId);
        return { success: false, error: "Mention not found" };
      }

      // Skip if already analyzed
      if (mention.sentimentAnalysis) {
        console.log("Sentiment already analyzed for mention:", args.mentionId);
        return { success: true, skipped: true };
      }

      // Get the monitored account info to get the handle
      const account = await ctx.runQuery(api.mentions.queries.getTwitterAccountById, {
        accountId: mention.monitoredAccountId,
      });
      const accountHandle = account?.handle || "unknown";

      console.log("🎯 Starting sentiment analysis for mention:", args.mentionId);

      // Run sentiment analysis if available
      try {
        const sentimentResult = await ctx.runAction(api.ai.sentiment.analyzeMentionSentiment, {
          mentionContent: mention.mentionContent,
          mentionAuthor: mention.mentionAuthor,
          mentionAuthorHandle: mention.mentionAuthorHandle,
          mentionType: mention.mentionType,
          accountHandle: accountHandle,
          engagement: {
            likes: mention.engagement.likes,
            retweets: mention.engagement.retweets,
            replies: mention.engagement.replies,
          },
          priority: mention.priority,
        });

        // Store the sentiment analysis results
        await ctx.runMutation(api.ai.sentiment.updateMentionSentiment, {
          mentionId: args.mentionId,
          sentimentAnalysis: sentimentResult,
        });

        return { 
          success: true, 
          sentimentResult,
          sentiment: sentimentResult.sentiment,
          score: sentimentResult.sentimentScore,
        };
      } catch (sentimentError) {
        console.log("Sentiment analysis not available, skipping:", sentimentError);
        return { success: true, skipped: true, reason: "sentiment_analysis_not_available" };
      }

    } catch (error) {
      console.error("❌ Error processing sentiment for mention:", args.mentionId, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

/**
 * Simple one-click mention refresh for users
 * Optimized for quick frontend calls with immediate feedback
 */
export const quickMentionRefresh = action({
  args: {
    accountHandle: v.optional(v.string()), // Optional: refresh specific account
  },
  handler: async (ctx, args) => {
    try {
      // Get authenticated user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        return {
          success: false,
          message: "Please log in to refresh mentions",
          newMentions: 0
        };
      }

      // 🚀 SMART RATE LIMITING: Check if we refreshed recently
      const user = await ctx.runQuery(api.users.getCurrentUser);
      if (!user) {
        return {
          success: false,
          message: "User not found",
          newMentions: 0
        };
      }

      // Get user's last refresh time from database
      const lastRefresh = user.lastMentionRefresh || 0;
      const timeSinceLastRefresh = Date.now() - lastRefresh;
      const minRefreshInterval = 2 * 60 * 1000; // 2 minutes minimum between refreshes

      if (timeSinceLastRefresh < minRefreshInterval) {
        const waitTime = Math.ceil((minRefreshInterval - timeSinceLastRefresh) / 1000);
        return {
          success: false,
          message: `Please wait ${waitTime} seconds before refreshing again`,
          newMentions: 0,
          rateLimited: true,
          waitTime
        };
      }

      // 🎯 OPTIMIZED FOR LARGE BACKLOGS: Limit processing scope
      const result = await ctx.runAction(api.mentions.mutations.fetchUserMentions, {
        accountHandle: args.accountHandle,
        hoursBack: 2, // Only check last 2 hours for quick refresh
        maxMentions: 10, // Much smaller limit to prevent overload
      });

      // Update user's last refresh timestamp
      await ctx.runMutation(api.users.updateUser, {
        userId: user._id,
        updates: { lastMentionRefresh: Date.now() }
      });

      return {
        success: result.success,
        message: result.success 
          ? `Quick refresh complete: ${result.newMentions} new mentions found`
          : result.message,
        newMentions: result.newMentions,
        accountsChecked: result.accountsChecked,
        timestamp: Date.now(),
        quickRefresh: true,
        optimizedForLargeBacklog: true,
      };

    } catch (error) {
      console.error('Error in quick mention refresh:', error);
      return {
        success: false,
        message: `Refresh failed: ${error instanceof Error ? error.message : String(error)}`,
        newMentions: 0,
        quickRefresh: true,
      };
    }
  },
});