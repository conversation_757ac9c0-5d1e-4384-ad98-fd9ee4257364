import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import type { ActionCtx } from "../_generated/server";
import { getOpenRouterClient } from "../lib/openrouter_client";

/**
 * Tweet Analysis Engine
 * Provides comprehensive analysis of tweets for response strategy and prioritization
 */

/**
 * Analyze if a tweet is worth responding to
 */
export const analyzeTweetWorthiness = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
    authorDisplayName: v.string(),
    authorIsVerified: v.optional(v.boolean()),
    authorFollowerCount: v.optional(v.number()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    userContext: v.optional(v.object({
      expertise: v.optional(v.array(v.string())),
      interests: v.optional(v.array(v.string())),
      brand: v.optional(v.string()),
    })),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const client = getOpenRouterClient();
      
      const worthinessPrompt = `
Analyze if this tweet is worth responding to from a business/personal brand perspective:

Tweet: "${args.content}"
Author: @${args.authorHandle} (${args.authorDisplayName}) ${args.authorIsVerified ? '(Verified)' : ''}
Followers: ${args.authorFollowerCount || 'Unknown'}
Engagement: ${args.engagement.likes} likes, ${args.engagement.retweets} retweets, ${args.engagement.replies} replies
${args.engagement.views ? `Views: ${args.engagement.views}` : ''}

${args.userContext?.expertise?.length ? `Your expertise: ${args.userContext.expertise.join(', ')}` : ''}
${args.userContext?.interests?.length ? `Your interests: ${args.userContext.interests.join(', ')}` : ''}
${args.userContext?.brand ? `Your brand: ${args.userContext.brand}` : ''}

Consider these factors:
1. Relevance to your expertise/interests
2. Engagement potential (author influence, current engagement)
3. Content quality and discussion value
4. Brand alignment and opportunity
5. Response timing and context

Provide analysis in this exact JSON format:
{
  "shouldRespond": boolean,
  "confidence": number (0-1),
  "priority": "urgent" | "high" | "medium" | "low",
  "reasons": [string],
  "suggestedStrategy": "engage" | "educate" | "support" | "promote" | "skip",
  "estimatedEngagement": {
    "likes": number,
    "retweets": number,
    "replies": number
  },
  "riskFactors": [string],
  "opportunities": [string]
}`;

      const response = await client.generateCompletion(worthinessPrompt, {
        model: 'google/gemini-2.0-flash-exp:free',
        systemPrompt: 'You are an expert social media strategist who analyzes tweets for response worthiness and brand engagement opportunities.',
        maxTokens: 600,
        temperature: 0.3,
      });

      // Parse JSON response
      let analysis;
      try {
        analysis = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse analysis JSON:', response.content);
        // Fallback analysis
        analysis = {
          shouldRespond: false,
          confidence: 0.1,
          priority: 'low',
          reasons: ['Failed to analyze tweet'],
          suggestedStrategy: 'skip',
          estimatedEngagement: { likes: 0, retweets: 0, replies: 0 },
          riskFactors: ['Analysis error'],
          opportunities: [],
        };
      }

      return {
        tweetId: args.tweetId,
        analysis,
        model: 'google/gemini-2.0-flash-exp:free',
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet worthiness analysis failed:', error);
      throw new Error(`Tweet analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Analyze tweet sentiment and emotional context
 */
export const analyzeTweetSentiment = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const client = getOpenRouterClient();
      
      const sentimentPrompt = `
Analyze the sentiment and emotional context of this tweet:

Tweet: "${args.content}"
Author: @${args.authorHandle}

Provide detailed sentiment analysis including:
1. Overall sentiment (positive, negative, neutral, mixed)
2. Emotional intensity and specific emotions
3. Tone and communication style
4. Topics and themes
5. Urgency level
6. Recommended response tone

Provide analysis in this exact JSON format:
{
  "sentiment": "positive" | "negative" | "neutral" | "mixed",
  "confidence": number (0-1),
  "emotions": [string],
  "intensity": number (0-100),
  "topics": [string],
  "tone": "formal" | "casual" | "professional" | "humorous" | "urgent" | "frustrated",
  "urgency": "immediate" | "high" | "medium" | "low",
  "responseTone": "supportive" | "informative" | "engaging" | "professional" | "empathetic",
  "triggers": [string],
  "context": "question" | "complaint" | "praise" | "discussion" | "announcement" | "other"
}`;

      const response = await client.generateCompletion(sentimentPrompt, {
        model: 'google/gemini-2.0-flash-exp:free',
        systemPrompt: 'You are an expert in emotional psychology and sentiment analysis with deep understanding of social media communication patterns.',
        maxTokens: 400,
        temperature: 0.2,
      });

      // Parse JSON response
      let sentiment;
      try {
        sentiment = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse sentiment JSON:', response.content);
        // Fallback sentiment
        sentiment = {
          sentiment: 'neutral',
          confidence: 0.1,
          emotions: ['unknown'],
          intensity: 50,
          topics: ['general'],
          tone: 'casual',
          urgency: 'low',
          responseTone: 'informative',
          triggers: [],
          context: 'other',
        };
      }

      return {
        tweetId: args.tweetId,
        sentiment,
        model: 'google/gemini-2.0-flash-exp:free',
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet sentiment analysis failed:', error);
      throw new Error(`Sentiment analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Extract topics and themes from a tweet
 */
export const extractTweetTopics = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const client = getOpenRouterClient();
      
      const topicPrompt = `
Extract and categorize the main topics and themes from this tweet:

Tweet: "${args.content}"

Identify:
1. Main topics and themes
2. Industry categories
3. Content type classification
4. Complexity level
5. Target audience
6. Suggested hashtags for responses

Provide analysis in this exact JSON format:
{
  "mainTopics": [string],
  "industries": [string],
  "contentType": "news" | "opinion" | "question" | "announcement" | "educational" | "entertainment" | "personal",
  "complexity": "simple" | "moderate" | "complex" | "technical",
  "targetAudience": "general" | "professionals" | "technical" | "business" | "consumers",
  "suggestedHashtags": [string],
  "categories": [string],
  "keywords": [string]
}`;

      const response = await client.generateCompletion(topicPrompt, {
        model: 'google/gemini-2.0-flash-exp:free',
        systemPrompt: 'You are an expert content categorization specialist who identifies topics, themes, and content patterns in social media posts.',
        maxTokens: 300,
        temperature: 0.2,
      });

      // Parse JSON response
      let topics;
      try {
        topics = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse topics JSON:', response.content);
        // Fallback topics
        topics = {
          mainTopics: ['general'],
          industries: ['social media'],
          contentType: 'general',
          complexity: 'simple',
          targetAudience: 'general',
          suggestedHashtags: [],
          categories: ['general'],
          keywords: [],
        };
      }

      return {
        tweetId: args.tweetId,
        topics,
        model: 'google/gemini-2.0-flash-exp:free',
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet topic extraction failed:', error);
      throw new Error(`Topic extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Classify tweet priority for response queue
 */
export const classifyTweetPriority = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
    authorIsVerified: v.optional(v.boolean()),
    authorFollowerCount: v.optional(v.number()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
    }),
    recency: v.number(), // Hours since posted
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const client = getOpenRouterClient();

      // Build priority scoring prompt
      const priorityPrompt = `
Analyze this tweet and assign a priority score for response queue:

Tweet: "${args.content}"
Author: @${args.authorHandle} ${args.authorIsVerified ? '(Verified)' : ''}
Followers: ${args.authorFollowerCount || 0}
Engagement: ${args.engagement.likes} likes, ${args.engagement.retweets} retweets, ${args.engagement.replies} replies
Posted: ${args.recency} hours ago

Priority factors to consider:
1. Author influence (verification, follower count)
2. Current engagement (viral potential)
3. Content relevance and quality
4. Recency (time-sensitive vs evergreen)
5. Response opportunity (questions, mentions, discussions)

Provide analysis in this exact JSON format:
{
  "priority": "urgent" | "high" | "medium" | "low",
  "score": number (0-100),
  "factors": {
    "authorInfluence": number (0-25),
    "engagement": number (0-25),
    "contentQuality": number (0-25),
    "timeliness": number (0-25)
  },
  "reasoning": "explanation",
  "recommendedAction": "respond_immediately" | "respond_soon" | "respond_later" | "skip",
  "timeWindow": "immediate" | "1hour" | "4hours" | "24hours" | "flexible"
}`;

      const response = await client.generateCompletion(priorityPrompt, {
        model: 'google/gemini-2.0-flash-exp:free',
        systemPrompt: 'You are an expert social media strategist who prioritizes content for maximum engagement and brand impact.',
        maxTokens: 400,
        temperature: 0.3,
      });

      // Parse JSON response
      let priority;
      try {
        priority = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse priority JSON:', response.content);
        // Calculate fallback priority based on basic metrics
        const authorScore = (args.authorFollowerCount || 0) > 10000 ? 20 : 10;
        const verifiedBonus = args.authorIsVerified ? 15 : 0;
        const engagementScore = Math.min(25, (args.engagement.likes + args.engagement.retweets) / 10);
        const timelinessScore = args.recency < 2 ? 25 : args.recency < 6 ? 15 : 5;
        
        const totalScore = authorScore + verifiedBonus + engagementScore + timelinessScore;
        
        priority = {
          priority: totalScore > 70 ? 'high' : totalScore > 40 ? 'medium' : 'low',
          score: totalScore,
          factors: {
            authorInfluence: authorScore + verifiedBonus,
            engagement: engagementScore,
            contentQuality: 15, // Default
            timeliness: timelinessScore,
          },
          reasoning: 'Fallback scoring due to analysis failure',
          recommendedAction: totalScore > 70 ? 'respond_soon' : 'respond_later',
          timeWindow: totalScore > 70 ? '1hour' : '24hours',
        };
      }

      return {
        tweetId: args.tweetId,
        priority,
        model: 'google/gemini-2.0-flash-exp:free',
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Tweet priority classification failed:', error);
      throw new Error(`Priority classification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Comprehensive tweet analysis combining all methods
 */
export const analyzeTweetComprehensive = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
    authorDisplayName: v.string(),
    authorIsVerified: v.optional(v.boolean()),
    authorFollowerCount: v.optional(v.number()),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
      views: v.optional(v.number()),
    }),
    createdAt: v.number(),
    userContext: v.optional(v.object({
      expertise: v.optional(v.array(v.string())),
      interests: v.optional(v.array(v.string())),
      brand: v.optional(v.string()),
    })),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const recency = (Date.now() - args.createdAt) / (1000 * 60 * 60); // Hours

      // Run all analyses in parallel for efficiency
      const [worthinessResult, sentimentResult, topicsResult, priorityResult] = await Promise.allSettled([
        ctx.runAction(api.ai.tweetAnalysis.analyzeTweetWorthiness, {
          tweetId: args.tweetId,
          content: args.content,
          authorHandle: args.authorHandle,
          authorDisplayName: args.authorDisplayName,
          authorIsVerified: args.authorIsVerified,
          authorFollowerCount: args.authorFollowerCount,
          engagement: args.engagement,
          userContext: args.userContext,
        }),
        ctx.runAction(api.ai.tweetAnalysis.analyzeTweetSentiment, {
          tweetId: args.tweetId,
          content: args.content,
          authorHandle: args.authorHandle,
        }),
        ctx.runAction(api.ai.tweetAnalysis.extractTweetTopics, {
          tweetId: args.tweetId,
          content: args.content,
        }),
        ctx.runAction(api.ai.tweetAnalysis.classifyTweetPriority, {
          tweetId: args.tweetId,
          content: args.content,
          authorHandle: args.authorHandle,
          authorIsVerified: args.authorIsVerified,
          authorFollowerCount: args.authorFollowerCount,
          engagement: args.engagement,
          recency,
        }),
      ]);

      return {
        tweetId: args.tweetId,
        analysis: {
          worthiness: worthinessResult.status === 'fulfilled' ? worthinessResult.value.analysis : null,
          sentiment: sentimentResult.status === 'fulfilled' ? sentimentResult.value.sentiment : null,
          topics: topicsResult.status === 'fulfilled' ? topicsResult.value.topics : null,
          priority: priorityResult.status === 'fulfilled' ? priorityResult.value.priority : null,
        },
        errors: [
          ...(worthinessResult.status === 'rejected' ? [{ type: 'worthiness', error: worthinessResult.reason }] : []),
          ...(sentimentResult.status === 'rejected' ? [{ type: 'sentiment', error: sentimentResult.reason }] : []),
          ...(topicsResult.status === 'rejected' ? [{ type: 'topics', error: topicsResult.reason }] : []),
          ...(priorityResult.status === 'rejected' ? [{ type: 'priority', error: priorityResult.reason }] : []),
        ],
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Comprehensive tweet analysis failed:', error);
      throw new Error(`Comprehensive analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Quick tweet analysis for rapid screening
 */
export const analyzeTweetQuick = action({
  args: {
    tweetId: v.string(),
    content: v.string(),
    authorHandle: v.string(),
    engagement: v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
    }),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const client = getOpenRouterClient();
      
      const quickAnalysisPrompt = `
Perform a quick analysis of this tweet for response decision:

Tweet: "${args.content}"
Author: @${args.authorHandle}
Engagement: ${args.engagement.likes}L ${args.engagement.retweets}RT ${args.engagement.replies}R

Provide rapid assessment in this exact JSON format:
{
  "shouldRespond": boolean,
  "confidence": number (0-1),
  "priority": "high" | "medium" | "low",
  "sentiment": "positive" | "negative" | "neutral",
  "responseType": "support" | "engage" | "educate" | "skip",
  "urgency": "immediate" | "soon" | "later",
  "reason": "brief explanation"
}`;

      const response = await client.generateCompletion(quickAnalysisPrompt, {
        model: 'google/gemini-2.0-flash-exp:free',
        systemPrompt: 'You are a rapid content screening specialist. Provide quick but accurate tweet assessments.',
        maxTokens: 200,
        temperature: 0.1,
      });

      let analysis;
      try {
        analysis = JSON.parse(response.content);
      } catch (parseError) {
        console.error('Failed to parse quick analysis JSON:', response.content);
        analysis = {
          shouldRespond: false,
          confidence: 0.5,
          priority: 'low',
          sentiment: 'neutral',
          responseType: 'skip',
          urgency: 'later',
          reason: 'Analysis failed',
        };
      }

      return {
        tweetId: args.tweetId,
        analysis,
        model: 'google/gemini-2.0-flash-exp:free',
        analyzedAt: Date.now(),
      };
    } catch (error) {
      console.error('Quick tweet analysis failed:', error);
      throw new Error(`Quick analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});