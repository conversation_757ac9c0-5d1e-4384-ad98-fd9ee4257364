import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import type { ActionCtx } from "../_generated/server";
import { getOpenRouterClient } from "../lib/openrouter_client";

/**
 * AI Response Generation Engine
 * Generates contextual, engaging responses for mentions and tweets
 */

/**
 * Generate contextual responses for mentions
 * Reads the mention content and crafts appropriate, concise replies
 */
export const generateResponsesForMention = action({
  args: {
    mentionId: v.id("mentions"),
    mentionContent: v.string(),
    authorHandle: v.optional(v.string()),
    authorName: v.optional(v.string()),
    context: v.optional(v.string()),
    responseCount: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      console.log("🤖 Generating contextual responses for mention");
      
      const client = getOpenRouterClient();
      
      // Get current user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("User not authenticated");
      }
      
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
        .first();
      
      if (!user) {
        throw new Error("User not found");
      }

      // Define response styles with enhanced prompting
      const styles = [
        { 
          name: "professional", 
          prompt: "Create a professional, informative response that adds value to the conversation",
          systemPrompt: "You are a professional social media manager. Your responses are polished, informative, and add genuine value to conversations."
        },
        { 
          name: "casual", 
          prompt: "Create a friendly, conversational response that builds rapport",
          systemPrompt: "You are a friendly social media personality. Your responses are warm, approachable, and build genuine connections."
        },
        { 
          name: "supportive", 
          prompt: "Create a helpful, supportive response that offers assistance or encouragement",
          systemPrompt: "You are an empathetic helper. Your responses provide genuine support, encouragement, and actionable assistance."
        },
        { 
          name: "engaging", 
          prompt: "Create an engaging response that encourages further discussion and interaction",
          systemPrompt: "You are a conversation catalyst. Your responses spark meaningful discussions and encourage engagement."
        }
      ];

      const responseCount = Math.min(args.responseCount || 3, styles.length);
      const selectedStyles = styles.slice(0, responseCount);
      const responses = [];
      
      for (const style of selectedStyles) {
        const prompt = `You are responding to this mention on social media:

"${args.mentionContent}"

Author: ${args.authorName || "Unknown"} (@${args.authorHandle || "unknown"})
${args.context ? `Additional context: ${args.context}` : ''}

${style.prompt}

Requirements:
- Keep under 280 characters (Twitter limit)
- Be authentic and conversational
- Add value to the conversation
- Match the ${style.name} tone
- Respond directly to what they said
- Be helpful and engaging
- Avoid generic responses
- Make it personal and specific

Generate ONLY the reply text, no quotes or explanations:`;

        const response = await client.generateCompletion(prompt, {
          model: "google/gemini-2.0-flash-exp:free",
          maxTokens: 120,
          temperature: 0.7,
          systemPrompt: style.systemPrompt
        });

        const cleanContent = response.content
          .replace(/^(Reply:|Response:|Tweet:|Answer:)\s*/i, '')
          .replace(/^[\"']|[\"']$/g, '')
          .trim();

        // Validate content length
        if (cleanContent.length > 280) {
          console.warn(`Response too long (${cleanContent.length} chars), truncating...`);
          const truncated = cleanContent.substring(0, 277) + "...";
          
          // Save the response to database
          const responseId = await ctx.runMutation(api.responses.mutations.storeResponse, {
            targetType: "mention",
            targetId: args.mentionId,
            content: truncated,
            style: style.name,
            confidence: 0.75, // Lower confidence for truncated content
            generationModel: "google/gemini-2.0-flash-exp:free",
            userId: user._id,
          });

          responses.push({
            id: responseId,
            content: truncated,
            style: style.name,
            characterCount: truncated.length,
            wasTruncated: true,
          });
        } else {
          // Save the response to database
          const responseId = await ctx.runMutation(api.responses.mutations.storeResponse, {
            targetType: "mention",
            targetId: args.mentionId,
            content: cleanContent,
            style: style.name,
            confidence: 0.85,
            generationModel: "google/gemini-2.0-flash-exp:free",
            userId: user._id,
          });

          responses.push({
            id: responseId,
            content: cleanContent,
            style: style.name,
            characterCount: cleanContent.length,
            wasTruncated: false,
          });
        }
      }

      console.log(`✅ Generated ${responses.length} contextual responses`);
      
      return {
        success: true,
        totalGenerated: responses.length,
        responses: responses,
        generatedAt: Date.now(),
      };

    } catch (error) {
      console.error("❌ Response generation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        totalGenerated: 0,
        responses: [],
      };
    }
  },
});

/**
 * Generate responses for tweets (not mentions)
 */
export const generateResponsesForTweet = action({
  args: {
    tweetId: v.string(),
    tweetContent: v.string(),
    authorHandle: v.string(),
    authorName: v.optional(v.string()),
    engagement: v.optional(v.object({
      likes: v.number(),
      retweets: v.number(),
      replies: v.number(),
    })),
    responseStyle: v.optional(v.union(
      v.literal("professional"),
      v.literal("casual"),
      v.literal("supportive"),
      v.literal("engaging"),
      v.literal("informative")
    )),
    responseCount: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      console.log("🤖 Generating responses for tweet");
      
      const client = getOpenRouterClient();
      
      // Get current user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("User not authenticated");
      }
      
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
        .first();
      
      if (!user) {
        throw new Error("User not found");
      }

      const allStyles = [
        { 
          name: "professional", 
          prompt: "Create a professional, well-informed comment that adds business value",
          systemPrompt: "You are a business professional commenting thoughtfully on industry topics."
        },
        { 
          name: "casual", 
          prompt: "Create a friendly, relatable comment that shows personality",
          systemPrompt: "You are a friendly person sharing genuine thoughts and experiences."
        },
        { 
          name: "supportive", 
          prompt: "Create an encouraging, uplifting comment that offers support",
          systemPrompt: "You are supportive and empathetic, always looking to help and encourage others."
        },
        { 
          name: "engaging", 
          prompt: "Create a comment that sparks discussion and encourages responses",
          systemPrompt: "You are great at starting conversations and bringing people together."
        },
        { 
          name: "informative", 
          prompt: "Create an educational comment that shares valuable insights or information",
          systemPrompt: "You are knowledgeable and love sharing useful information and insights."
        }
      ];

      // Filter styles based on request
      const selectedStyles = args.responseStyle 
        ? allStyles.filter(s => s.name === args.responseStyle)
        : allStyles.slice(0, args.responseCount || 3);

      const responses = [];
      
      for (const style of selectedStyles) {
        const prompt = `You are commenting on this tweet:

"${args.tweetContent}"

Author: ${args.authorName || "Unknown"} (@${args.authorHandle})
${args.engagement ? `Engagement: ${args.engagement.likes} likes, ${args.engagement.retweets} retweets, ${args.engagement.replies} replies` : ''}

${style.prompt}

Requirements:
- Keep under 280 characters
- Be authentic and add value
- Match the ${style.name} tone
- Comment thoughtfully on their content
- Encourage meaningful dialogue
- Avoid being promotional or salesy

Generate ONLY the comment text, no quotes or explanations:`;

        const response = await client.generateCompletion(prompt, {
          model: "google/gemini-2.0-flash-exp:free",
          maxTokens: 120,
          temperature: 0.8,
          systemPrompt: style.systemPrompt
        });

        const cleanContent = response.content
          .replace(/^(Comment:|Reply:|Response:)\s*/i, '')
          .replace(/^[\"']|[\"']$/g, '')
          .trim();

        // Store as tweet response (different from mention response)
        responses.push({
          content: cleanContent,
          style: style.name,
          characterCount: cleanContent.length,
          confidence: 0.8,
        });
      }

      console.log(`✅ Generated ${responses.length} tweet responses`);
      
      return {
        success: true,
        totalGenerated: responses.length,
        responses: responses,
        tweetId: args.tweetId,
        generatedAt: Date.now(),
      };

    } catch (error) {
      console.error("❌ Tweet response generation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        totalGenerated: 0,
        responses: [],
      };
    }
  },
});

/**
 * Generate multiple response variations for A/B testing
 */
export const generateResponseVariations = action({
  args: {
    baseContent: v.string(),
    style: v.string(),
    variationCount: v.optional(v.number()),
    targetLength: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const client = getOpenRouterClient();
      const variationCount = Math.min(args.variationCount || 5, 10);
      const targetLength = args.targetLength || 280;
      
      const prompt = `Create ${variationCount} different variations of this social media response:

Original: "${args.baseContent}"
Style: ${args.style}
Target length: Under ${targetLength} characters

Generate variations that:
- Maintain the same core message and tone
- Use different wording and structure
- Vary in approach (direct, storytelling, question-based, etc.)
- Keep the ${args.style} style consistent
- Are all under ${targetLength} characters

Format as JSON array:
["variation 1", "variation 2", "variation 3", ...]`;

      const response = await client.generateCompletion(prompt, {
        model: "google/gemini-2.0-flash-exp:free",
        maxTokens: 500,
        temperature: 0.9,
        systemPrompt: `You are a creative copywriter specializing in social media content variations. Create diverse but cohesive alternatives.`
      });

      let variations;
      try {
        variations = JSON.parse(response.content);
        if (!Array.isArray(variations)) {
          throw new Error("Response not an array");
        }
      } catch (parseError) {
        console.error('Failed to parse variations JSON:', response.content);
        // Fallback to simple variations
        variations = [
          args.baseContent,
          args.baseContent + " 👍",
          "Absolutely! " + args.baseContent,
        ];
      }

      return {
        success: true,
        variations: variations.slice(0, variationCount).map((content, index) => ({
          id: index + 1,
          content: content.trim(),
          characterCount: content.trim().length,
          style: args.style,
        })),
        generatedAt: Date.now(),
      };

    } catch (error) {
      console.error("❌ Response variation generation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        variations: [],
      };
    }
  },
});

/**
 * Enhanced response generation with sentiment matching
 */
export const generateSentimentMatchedResponse = action({
  args: {
    targetContent: v.string(),
    targetSentiment: v.union(v.literal("positive"), v.literal("negative"), v.literal("neutral")),
    authorHandle: v.string(),
    responseGoal: v.union(
      v.literal("support"),
      v.literal("educate"),
      v.literal("engage"),
      v.literal("deflect"),
      v.literal("promote")
    ),
    userBrand: v.optional(v.string()),
    maxLength: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const client = getOpenRouterClient();
      const maxLength = args.maxLength || 280;
      
      const sentimentGuidelines = {
        positive: "Match their positive energy, celebrate with them, amplify the good vibes",
        negative: "Be empathetic and supportive, offer help if appropriate, maintain professionalism",
        neutral: "Be informative and engaging, add value to the conversation"
      };

      const goalGuidelines = {
        support: "Provide encouragement, assistance, or validation",
        educate: "Share helpful information or insights", 
        engage: "Start meaningful conversation and build relationships",
        deflect: "Acknowledge politely but redirect conversation away from sensitive topics",
        promote: "Subtly showcase value while being genuinely helpful"
      };

      const prompt = `Generate a thoughtful response to this content:

"${args.targetContent}"
Author: @${args.authorHandle}

Response parameters:
- Sentiment: ${args.targetSentiment} (${sentimentGuidelines[args.targetSentiment]})
- Goal: ${args.responseGoal} (${goalGuidelines[args.responseGoal]})
- Brand context: ${args.userBrand || "Personal brand"}
- Max length: ${maxLength} characters

Create a response that:
1. Acknowledges their content appropriately 
2. Matches the emotional tone
3. Achieves the response goal
4. Maintains authentic voice
5. Encourages positive interaction

Generate ONLY the response text:`;

      const response = await client.generateCompletion(prompt, {
        model: "google/gemini-2.0-flash-exp:free",
        maxTokens: 150,
        temperature: 0.6,
        systemPrompt: `You are an expert social media manager who crafts perfectly-tuned responses that match sentiment and achieve specific goals while maintaining authenticity.`
      });

      const cleanContent = response.content
        .replace(/^(Response:|Reply:)\s*/i, '')
        .replace(/^[\"']|[\"']$/g, '')
        .trim();

      return {
        success: true,
        response: {
          content: cleanContent,
          characterCount: cleanContent.length,
          targetSentiment: args.targetSentiment,
          responseGoal: args.responseGoal,
          confidence: 0.88,
        },
        generatedAt: Date.now(),
      };

    } catch (error) {
      console.error("❌ Sentiment-matched response generation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        response: null,
      };
    }
  },
});