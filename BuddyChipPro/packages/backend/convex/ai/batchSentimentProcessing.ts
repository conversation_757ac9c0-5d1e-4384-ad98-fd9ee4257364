import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal } from "../_generated/api";
import type { ActionCtx } from "../_generated/server";

/**
 * Batch Sentiment Processing Engine
 * Efficiently processes sentiment analysis for multiple mentions in batches
 */

/**
 * Start sentiment analysis for unprocessed mentions
 */
export const startSentimentAnalysisForUnprocessed = action({
  args: {
    maxBatchSize: v.optional(v.number()),
    accountId: v.optional(v.string()), // Optional: analyze only specific account's mentions
  },
  handler: async (ctx: ActionCtx, args) => {
    const maxBatchSize = args.maxBatchSize || 50;
    
    try {
      console.log('🔍 Starting sentiment analysis for unprocessed mentions...');
      
      // Get current user for authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("User not authenticated");
      }

      // Get unprocessed mentions (those without sentiment analysis) 
      // Using direct database query since the specific query function might not exist
      const unprocessedMentions = await ctx.db
        .query("mentions")
        .filter(q => q.eq(q.field("sentimentAnalyzed"), false))
        .order("desc")
        .take(maxBatchSize);

      if (!unprocessedMentions || unprocessedMentions.length === 0) {
        return {
          success: true,
          message: "No unprocessed mentions found",
          processed: 0,
          total: 0,
          results: [],
        };
      }

      console.log(`📊 Found ${unprocessedMentions.length} unprocessed mentions`);

      // Transform mentions for batch analysis
      const mentionsForAnalysis = unprocessedMentions.map(mention => ({
        id: mention._id,
        content: mention.mentionContent || "",
        author: mention.mentionAuthor || "Unknown",
        authorHandle: mention.mentionAuthorHandle || "unknown",
        accountHandle: "unknown", // Will need to be updated based on your schema
        engagement: {
          likes: mention.engagement?.likes || 0,
          retweets: mention.engagement?.retweets || 0,
          replies: mention.engagement?.replies || 0,
        },
      }));

      // Batch analyze sentiment
      const analysisResults = await ctx.runAction(
        internal.ai.sentimentAnalysis.batchAnalyzeSentiment,
        {
          mentions: mentionsForAnalysis,
          maxConcurrent: 5,
        }
      );

      console.log(`✅ Sentiment analysis completed: ${analysisResults.successful}/${analysisResults.total} successful`);

      // Update mentions with sentiment analysis
      const updatePromises = analysisResults.results
        .filter(result => result.sentiment !== null)
        .map(async (result) => {
          try {
            // Update mention with sentiment analysis directly in database
            await ctx.db.patch(result.id as any, {
              sentimentAnalysis: result.sentiment!,
              sentimentAnalyzed: true,
              sentimentAnalyzedAt: Date.now(),
            });
            return { id: result.id, success: true };
          } catch (error) {
            console.error(`Failed to update sentiment for mention ${result.id}:`, error);
            return { id: result.id, success: false, error: error instanceof Error ? error.message : "Unknown error" };
          }
        });

      const updateResults = await Promise.all(updatePromises);
      const successfulUpdates = updateResults.filter(r => r.success).length;

      return {
        success: true,
        message: `Sentiment analysis completed for ${successfulUpdates} mentions`,
        processed: successfulUpdates,
        total: unprocessedMentions.length,
        failed: updateResults.filter(r => !r.success).length,
        results: updateResults,
        analyticsResults: {
          totalAnalyzed: analysisResults.successful,
          totalFailed: analysisResults.failed,
          processingTime: Date.now(),
        },
      };

    } catch (error) {
      console.error('Failed to start sentiment analysis:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
        processed: 0,
        total: 0,
        results: [],
      };
    }
  },
});

/**
 * Get statistics about unprocessed mentions for sentiment analysis
 */
export const getUnprocessedSentimentStats = action({
  args: {
    accountId: v.optional(v.string()),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      // Get current user for authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("User not authenticated");
      }

      // Count unprocessed mentions
      const unprocessedMentions = await ctx.db
        .query("mentions")
        .filter(q => q.eq(q.field("sentimentAnalyzed"), false))
        .collect();

      // Filter by account if specified
      const filteredMentions = args.accountId 
        ? unprocessedMentions.filter(m => m.accountId === args.accountId)
        : unprocessedMentions;

      return {
        unprocessedCount: filteredMentions.length,
        canProcess: filteredMentions.length > 0,
        totalUnprocessed: unprocessedMentions.length,
      };
    } catch (error) {
      console.error('Failed to get unprocessed sentiment stats:', error);
      return {
        unprocessedCount: 0,
        canProcess: false,
        totalUnprocessed: 0,
      };
    }
  },
});

/**
 * Process sentiment analysis for specific mentions by IDs
 */
export const processSentimentForSpecificMentions = action({
  args: {
    mentionIds: v.array(v.id("mentions")),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      console.log(`🔍 Processing sentiment for ${args.mentionIds.length} specific mentions...`);
      
      // Get current user for authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("User not authenticated");
      }

      // Get mentions by IDs
      const mentions = await Promise.all(
        args.mentionIds.map(id => ctx.db.get(id))
      );

      const validMentions = mentions.filter(Boolean);

      if (validMentions.length === 0) {
        return {
          success: false,
          message: "No valid mentions found",
          processed: 0,
          total: args.mentionIds.length,
        };
      }

      // Transform mentions for analysis
      const mentionsForAnalysis = validMentions.map(mention => ({
        id: mention!._id,
        content: mention!.mentionContent || "",
        author: mention!.mentionAuthor || "Unknown",
        authorHandle: mention!.mentionAuthorHandle || "unknown",
        accountHandle: "unknown",
        engagement: {
          likes: mention!.engagement?.likes || 0,
          retweets: mention!.engagement?.retweets || 0,
          replies: mention!.engagement?.replies || 0,
        },
      }));

      // Batch analyze sentiment
      const analysisResults = await ctx.runAction(
        internal.ai.sentimentAnalysis.batchAnalyzeSentiment,
        {
          mentions: mentionsForAnalysis,
          maxConcurrent: 3,
        }
      );

      // Update mentions with results
      const updatePromises = analysisResults.results
        .filter(result => result.sentiment !== null)
        .map(async (result) => {
          try {
            await ctx.db.patch(result.id as any, {
              sentimentAnalysis: result.sentiment!,
              sentimentAnalyzed: true,
              sentimentAnalyzedAt: Date.now(),
            });
            return { id: result.id, success: true };
          } catch (error) {
            console.error(`Failed to update sentiment for mention ${result.id}:`, error);
            return { id: result.id, success: false, error: error instanceof Error ? error.message : "Unknown error" };
          }
        });

      const updateResults = await Promise.all(updatePromises);
      const successfulUpdates = updateResults.filter(r => r.success).length;

      return {
        success: true,
        message: `Processed sentiment analysis for ${successfulUpdates} mentions`,
        processed: successfulUpdates,
        total: validMentions.length,
        failed: updateResults.filter(r => !r.success).length,
        results: updateResults,
      };

    } catch (error) {
      console.error('Failed to process sentiment for specific mentions:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
        processed: 0,
        total: args.mentionIds.length,
      };
    }
  },
});

/**
 * Reprocess sentiment analysis for mentions with low confidence scores
 */
export const reprocessLowConfidenceSentiment = action({
  args: {
    confidenceThreshold: v.optional(v.number()),
    maxBatchSize: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args) => {
    const confidenceThreshold = args.confidenceThreshold || 0.6;
    const maxBatchSize = args.maxBatchSize || 30;

    try {
      console.log(`🔍 Reprocessing mentions with sentiment confidence below ${confidenceThreshold}...`);
      
      // Get current user for authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("User not authenticated");
      }

      // Get mentions with low confidence sentiment analysis
      const allMentions = await ctx.db
        .query("mentions")
        .filter(q => q.eq(q.field("sentimentAnalyzed"), true))
        .collect();

      const lowConfidenceMentions = allMentions
        .filter(mention => 
          mention.sentimentAnalysis && 
          mention.sentimentAnalysis.confidence < confidenceThreshold
        )
        .slice(0, maxBatchSize);

      if (lowConfidenceMentions.length === 0) {
        return {
          success: true,
          message: `No mentions found with confidence below ${confidenceThreshold}`,
          processed: 0,
          total: 0,
        };
      }

      console.log(`📊 Found ${lowConfidenceMentions.length} mentions to reprocess`);

      // Transform mentions for analysis
      const mentionsForAnalysis = lowConfidenceMentions.map(mention => ({
        id: mention._id,
        content: mention.mentionContent || "",
        author: mention.mentionAuthor || "Unknown",
        authorHandle: mention.mentionAuthorHandle || "unknown",
        accountHandle: "unknown",
        engagement: {
          likes: mention.engagement?.likes || 0,
          retweets: mention.engagement?.retweets || 0,
          replies: mention.engagement?.replies || 0,
        },
      }));

      // Batch analyze sentiment with improved prompting
      const analysisResults = await ctx.runAction(
        internal.ai.sentimentAnalysis.batchAnalyzeSentiment,
        {
          mentions: mentionsForAnalysis,
          maxConcurrent: 3,
        }
      );

      // Update mentions with better results
      const updatePromises = analysisResults.results
        .filter(result => 
          result.sentiment !== null && 
          result.sentiment.confidence > confidenceThreshold
        )
        .map(async (result) => {
          try {
            await ctx.db.patch(result.id as any, {
              sentimentAnalysis: result.sentiment!,
              sentimentAnalyzedAt: Date.now(),
              reprocessedAt: Date.now(),
            });
            return { id: result.id, success: true };
          } catch (error) {
            console.error(`Failed to update reprocessed sentiment for mention ${result.id}:`, error);
            return { id: result.id, success: false, error: error instanceof Error ? error.message : "Unknown error" };
          }
        });

      const updateResults = await Promise.all(updatePromises);
      const successfulUpdates = updateResults.filter(r => r.success).length;

      return {
        success: true,
        message: `Reprocessed sentiment analysis for ${successfulUpdates} mentions`,
        processed: successfulUpdates,
        total: lowConfidenceMentions.length,
        improved: successfulUpdates,
        confidenceThreshold,
      };

    } catch (error) {
      console.error('Failed to reprocess low confidence sentiment:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error",
        processed: 0,
        total: 0,
      };
    }
  },
});