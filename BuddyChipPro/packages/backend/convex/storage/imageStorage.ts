import { action, mutation, query } from "../_generated/server";
import { api } from "../_generated/api";
import { v } from "convex/values";
import type { ActionCtx, MutationCtx, QueryCtx } from "../_generated/server";

/**
 * Image storage and management for generated images
 */

/**
 * Store image metadata in the database
 */
export const storeImageMetadata = mutation({
  args: {
    imageUrl: v.string(),
    originalPrompt: v.string(),
    revisedPrompt: v.optional(v.string()),
    model: v.string(),
    style: v.optional(v.string()),
    platform: v.optional(v.string()),
    size: v.optional(v.string()),
    quality: v.optional(v.string()),
    userId: v.optional(v.id("users")),
    responseId: v.optional(v.id("responses")),
    tags: v.optional(v.array(v.string())),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx: MutationCtx, args) => {
    try {
      const imageId = await ctx.db.insert("generatedImages", {
        imageUrl: args.imageUrl,
        originalPrompt: args.originalPrompt,
        revisedPrompt: args.revisedPrompt,
        model: args.model,
        style: args.style,
        platform: args.platform,
        size: args.size,
        quality: args.quality,
        userId: args.userId,
        responseId: args.responseId,
        tags: args.tags || [],
        isPublic: args.isPublic || false,
        downloadCount: 0,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      return { imageId, success: true };
    } catch (error) {
      console.error('Failed to store image metadata:', error);
      throw new Error(`Failed to store image metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Internal mutation to update the imageBase64 field.
 * This is called by the downloadImageAsBase64 action.
 */
export const internalUpdateImageBase64 = mutation({
  args: {
    imageId: v.id("generatedImages"),
    imageBase64: v.string(),
  },
  handler: async (ctx: MutationCtx, args) => {
    await ctx.db.patch(args.imageId, {
      imageBase64: args.imageBase64,
      updatedAt: Date.now(),
    });
    return { success: true };
  },
});

/**
 * Get image metadata by ID
 */
export const getImageMetadata = query({
  args: { imageId: v.id("generatedImages") },
  handler: async (ctx: QueryCtx, args) => {
    return await ctx.db.get(args.imageId);
  },
});

/**
 * Get images by user
 */
export const getUserImages = query({
  args: { 
    userId: v.optional(v.id("users")),
    limit: v.optional(v.number()),
    platform: v.optional(v.string()),
    style: v.optional(v.string()),
  },
  handler: async (ctx: QueryCtx, args) => {
    const limit = args.limit || 50;
    
    let query = ctx.db.query("generatedImages");
    
    if (args.userId) {
      query = query.filter((q) => q.eq(q.field("userId"), args.userId));
    }

    if (args.platform) {
      query = query.filter((q) => q.eq(q.field("platform"), args.platform));
    }
    
    if (args.style) {
      query = query.filter((q) => q.eq(q.field("style"), args.style));
    }

    return await query
      .order("desc")
      .take(limit);
  },
});

/**
 * Get images by response ID
 */
export const getResponseImages = query({
  args: { responseId: v.id("responses") },
  handler: async (ctx: QueryCtx, args) => {
    return await ctx.db
      .query("generatedImages")
      .filter((q) => q.eq(q.field("responseId"), args.responseId))
      .order("desc")
      .collect();
  },
});

/**
 * Update image metadata
 */
export const updateImageMetadata = mutation({
  args: {
    imageId: v.id("generatedImages"),
    tags: v.optional(v.array(v.string())),
    isPublic: v.optional(v.boolean()),
    customName: v.optional(v.string()),
  },
  handler: async (ctx: MutationCtx, args) => {
    try {
      const existingImage = await ctx.db.get(args.imageId);
      if (!existingImage) {
        throw new Error('Image not found');
      }

      await ctx.db.patch(args.imageId, {
        tags: args.tags !== undefined ? args.tags : existingImage.tags,
        isPublic: args.isPublic !== undefined ? args.isPublic : existingImage.isPublic,
        customName: args.customName !== undefined ? args.customName : existingImage.customName,
        updatedAt: Date.now(),
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to update image metadata:', error);
      throw new Error(`Failed to update image metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Delete image metadata
 */
export const deleteImageMetadata = mutation({
  args: { imageId: v.id("generatedImages") },
  handler: async (ctx: MutationCtx, args) => {
    try {
      const existingImage = await ctx.db.get(args.imageId);
      if (!existingImage) {
        throw new Error('Image not found');
      }

      await ctx.db.delete(args.imageId);
      return { success: true };
    } catch (error) {
      console.error('Failed to delete image metadata:', error);
      throw new Error(`Failed to delete image metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Track image download/usage
 */
export const trackImageUsage = mutation({
  args: { imageId: v.id("generatedImages") },
  handler: async (ctx: MutationCtx, args) => {
    try {
      const existingImage = await ctx.db.get(args.imageId);
      if (!existingImage) {
        throw new Error('Image not found');
      }

      await ctx.db.patch(args.imageId, {
        downloadCount: existingImage.downloadCount + 1,
        lastUsedAt: Date.now(),
        updatedAt: Date.now(),
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to track image usage:', error);
      throw new Error(`Failed to track image usage: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Search images by tags or content
 */
export const searchImages = query({
  args: {
    searchTerm: v.string(),
    userId: v.optional(v.id("users")),
    platform: v.optional(v.string()),
    style: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx: QueryCtx, args) => {
    const limit = args.limit || 20;
    
    // For now, implement simple text search on prompts
    // In the future, this could be enhanced with vector search
    let query = ctx.db.query("generatedImages");

    if (args.userId) {
      query = query.filter((q) => q.eq(q.field("userId"), args.userId));
    }
    
    if (args.platform) {
      query = query.filter((q) => q.eq(q.field("platform"), args.platform));
    }
    
    if (args.style) {
      query = query.filter((q) => q.eq(q.field("style"), args.style));
    }

    const allImages = await query.collect();
    
    // Filter by search term in prompts or tags
    const searchLower = args.searchTerm.toLowerCase();
    const filteredImages = allImages.filter(image => 
      image.originalPrompt.toLowerCase().includes(searchLower) ||
      (image.revisedPrompt && image.revisedPrompt.toLowerCase().includes(searchLower)) ||
      image.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
      (image.customName && image.customName.toLowerCase().includes(searchLower))
    );

    // Sort by creation date and limit
    return filteredImages
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(0, limit);
  },
});

/**
 * Get popular/trending images
 */
export const getPopularImages = query({
  args: {
    timeframe: v.optional(v.union(v.literal("day"), v.literal("week"), v.literal("month"))),
    platform: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx: QueryCtx, args) => {
    const limit = args.limit || 20;
    const now = Date.now();
    
    // Calculate timeframe in milliseconds
    const timeframes = {
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000,
    };
    
    const timeframe = timeframes[args.timeframe || 'week'];
    const cutoffTime = now - timeframe;

    let query = ctx.db
      .query("generatedImages")
      .filter((q) => q.gte(q.field("createdAt"), cutoffTime));

    if (args.platform) {
      query = query.filter((q) => q.eq(q.field("platform"), args.platform));
    }

    const images = await query.collect();
    
    // Sort by download count (popularity) and then by creation date
    return images
      .sort((a, b) => {
        if (a.downloadCount !== b.downloadCount) {
          return b.downloadCount - a.downloadCount;
        }
        return b.createdAt - a.createdAt;
      })
      .slice(0, limit);
  },
});

/**
 * Download image as base64 (for caching/optimization)
 */
export const downloadImageAsBase64 = action({
  args: { 
    imageUrl: v.string(),
    imageId: v.optional(v.id("generatedImages")),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      // Track usage if imageId provided
      if (args.imageId) {
        await ctx.runMutation(api.storage.imageStorage.trackImageUsage, {
          imageId: args.imageId,
        });
      }

      // Download image and convert to base64
      const response = await fetch(args.imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString('base64');
      const mimeType = response.headers.get('content-type') || 'image/png';
      const base64DataUri = `data:${mimeType};base64,${base64}`;

      // Cache the base64 data if imageId is provided and not already cached
      if (args.imageId) {
        try {
          const existingImage = await ctx.runQuery(api.storage.imageStorage.getImageMetadata, { imageId: args.imageId });
          if (existingImage && !existingImage.imageBase64) {
            await ctx.runMutation(api.storage.imageStorage.internalUpdateImageBase64, {
              imageId: args.imageId,
              imageBase64: base64DataUri,
            });
          }
        } catch (e) {
          console.warn(`Non-critical error: Failed to cache Base64 for image ${args.imageId}:`, e);
          // Do not fail the main action if caching fails
        }
      }

      return {
        base64: base64DataUri, // Return the full data URI
        mimeType,
        size: arrayBuffer.byteLength,
        downloadedAt: Date.now(),
      };
    } catch (error) {
      console.error('Failed to download image as base64:', error);
      throw new Error(`Failed to download image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Cleanup old images (for maintenance)
 */
export const cleanupOldImages = action({
  args: {
    olderThanDays: v.optional(v.number()),
    onlyUnused: v.optional(v.boolean()),
  },
  handler: async (ctx: ActionCtx, args) => {
    try {
      const daysToKeep = args.olderThanDays || 90; // Default 90 days
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
      
      const oldImages = await ctx.runQuery(api.storage.imageStorage.getAllImages, {
        olderThan: cutoffTime,
        onlyUnused: args.onlyUnused || false,
      });

      let deletedCount = 0;
      for (const image of oldImages) {
        try {
          await ctx.runMutation(api.storage.imageStorage.deleteImageMetadata, {
            imageId: image._id,
          });
          deletedCount++;
        } catch (deleteError) {
          console.error(`Failed to delete image ${image._id}:`, deleteError);
        }
      }

      return {
        success: true,
        deletedCount,
        totalFound: oldImages.length,
        cutoffDate: new Date(cutoffTime).toISOString(),
      };
    } catch (error) {
      console.error('Image cleanup failed:', error);
      throw new Error(`Image cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

/**
 * Helper query for cleanup
 */
export const getAllImages = query({
  args: {
    olderThan: v.optional(v.number()),
    onlyUnused: v.optional(v.boolean()),
  },
  handler: async (ctx: QueryCtx, args) => {
    let query = ctx.db.query("generatedImages");

    if (args.olderThan !== undefined) {
      query = query.filter((q) => q.lt(q.field("createdAt"), args.olderThan!));
    }

    if (args.onlyUnused) {
      query = query.filter((q) => q.eq(q.field("downloadCount"), 0));
    }

    return await query.collect();
  },
});