{"functions": "./convex/", "node": {"18": true}, "environment": {"OPENROUTER_API_KEY": {"description": "API key for OpenRouter AI service", "required": true}, "TWITTER_API_KEY": {"description": "API key for TwitterAPI.io service", "required": true}, "TWITTER_BEARER_TOKEN": {"description": "Official Twitter API Bearer <PERSON> (optional)", "required": false}, "OPENAI_API_KEY": {"description": "OpenAI API key for DALL-E and GPT-4", "required": false}, "XAI_API_KEY": {"description": "xAI API key for Grok integration", "required": false}, "PERPLEXITY_API_KEY": {"description": "Perplexity API key for enhanced research", "required": false}, "ANTHROPIC_API_KEY": {"description": "Anthropic API key for Claude models", "required": false}, "ENABLE_AI_ANALYSIS": {"description": "Enable AI-powered tweet analysis", "required": false, "default": "true"}, "ENABLE_MENTION_MONITORING": {"description": "Enable automated mention monitoring", "required": false, "default": "true"}, "ENABLE_TWEET_SCRAPING": {"description": "Enable automatic tweet scraping", "required": false, "default": "true"}, "ENABLE_RESPONSE_GENERATION": {"description": "Enable AI response generation", "required": false, "default": "true"}, "ENABLE_CRON_JOBS": {"description": "Enable automated cron job workflows", "required": false, "default": "true"}, "TWITTER_API_DELAY": {"description": "Delay between Twitter API calls (ms)", "required": false, "default": "1000"}, "TWITTER_API_RETRY_ATTEMPTS": {"description": "Number of retry attempts for failed Twitter API calls", "required": false, "default": "3"}, "TWITTER_API_RETRY_DELAY": {"description": "Delay between retry attempts (ms)", "required": false, "default": "5000"}, "TWITTER_DEFAULT_MAX_RESULTS": {"description": "Default maximum number of tweets to fetch per request", "required": false, "default": "20"}, "TWITTER_MAX_ACCOUNTS_PER_BATCH": {"description": "Maximum number of accounts to process in a single batch", "required": false, "default": "10"}, "TWITTER_MENTION_LOOKBACK_HOURS": {"description": "How many hours back to look for mentions", "required": false, "default": "24"}, "DEFAULT_SCRAPE_INTERVAL_MINUTES": {"description": "Default interval between scraping operations (minutes)", "required": false, "default": "60"}, "MENTION_CHECK_INTERVAL_MINUTES": {"description": "Interval between mention checks (minutes)", "required": false, "default": "15"}, "MAX_MENTIONS_PER_ACCOUNT": {"description": "Maximum number of mentions to store per account", "required": false, "default": "100"}, "VERIFIED_ACCOUNT_WEIGHT": {"description": "Priority weight for verified accounts", "required": false, "default": "2.0"}, "HIGH_PRIORITY_FOLLOWER_THRESHOLD": {"description": "Follower count threshold for high priority classification", "required": false, "default": "10000"}, "MEDIUM_PRIORITY_FOLLOWER_THRESHOLD": {"description": "Follower count threshold for medium priority classification", "required": false, "default": "1000"}, "TOKEN_USAGE_ENABLED": {"description": "Enable token usage tracking and cost calculation", "required": false, "default": "true"}, "DEBUG_MODE": {"description": "Enable debug mode for additional logging", "required": false, "default": "false"}, "LOG_LEVEL": {"description": "Logging level (debug, info, warn, error)", "required": false, "default": "info"}, "VERBOSE_LOGGING": {"description": "Enable verbose logging for troubleshooting", "required": false, "default": "false"}}, "modules": {"@convex-dev/auth": "^0.0.87"}}