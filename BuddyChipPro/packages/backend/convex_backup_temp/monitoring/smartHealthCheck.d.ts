/**
 * 🚀 SMART HEALTH CHECK SYSTEM
 *
 * Intelligent health monitoring with predictive failure detection
 * and bandwidth-optimized checking strategies
 */
interface BaseHealthResult {
    status: "healthy" | "warning" | "critical";
    issues: string[];
}
export interface DatabaseHealthResult extends BaseHealthResult {
    responseTime: number;
    connections: number;
    queryPerformance: number;
}
export interface CacheHealthResult extends BaseHealthResult {
    hitRate: number;
    size: number;
    expiredEntries: number;
}
export interface ApiHealthResult extends BaseHealthResult {
    dailyUsage: number;
    rateLimitStatus: string;
    errorRate: number;
}
export interface QueueHealthResult extends BaseHealthResult {
    pendingJobs: number;
    failedJobs: number;
    averageProcessingTime: number;
}
export interface PredictiveAnalysisResult {
    bandwidthTrend: "increasing" | "stable" | "decreasing";
    capacityRisk: "low" | "medium" | "high";
    costProjection: number;
    recommendations: string[];
}
export interface BandwidthAnalysisResult {
    currentUsage: number;
    optimizationSavings: number;
    cacheEfficiency: number;
    recommendations: string[];
}
/**
 * Smart health check with intelligent failure prediction
 * Replaces frequent health checks with intelligent monitoring
 */
export declare const smartHealthCheck: any;
/**
 * Quick health check for emergency situations
 */
export declare const quickHealthCheck: any;
export {};
