import type { LightweightTweet } from "./types/optimized";
export declare const getPendingTweets: import("convex/server").RegisteredQuery<"public", {
    twitterAccountId?: import("convex/values").GenericId<"twitterAccounts"> | undefined;
    limit?: number | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"tweets">;
    _creationTime: number;
    metadata?: {
        isThread?: boolean | undefined;
        threadPosition?: number | undefined;
        hasMedia?: boolean | undefined;
        mediaType?: string | undefined;
        language?: string | undefined;
    } | undefined;
    authorProfileImage?: string | undefined;
    isRetweet?: boolean | undefined;
    retweetedFrom?: string | undefined;
    analysisScore?: number | undefined;
    analysisReason?: string | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    createdAt: number;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
    tweetId: string;
    content: string;
    author: string;
    authorHandle: string;
    scrapedAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
}[]>>;
export declare const getTweetsByAccount: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
    cursor?: string | undefined;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
}, Promise<{
    data: LightweightTweet[];
    nextCursor: string | null;
    hasMore: boolean;
}>>;
export declare const getRecentTweets: import("convex/server").RegisteredQuery<"public", {
    hours?: number | undefined;
    limit?: number | undefined;
    cursor?: string | undefined;
}, Promise<{
    data: LightweightTweet[];
    nextCursor: string | null;
    hasMore: boolean;
}>>;
export declare const searchTweets: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
    cursor?: string | undefined;
    query: string;
}, Promise<{
    data: {
        id: import("convex/values").GenericId<"tweets">;
        content: string;
        author: string;
        authorHandle: string;
        authorProfileImage: string | undefined;
        createdAt: number;
    }[];
    nextCursor: null;
    hasMore: boolean;
}>>;
export declare const getTweetById: import("convex/server").RegisteredQuery<"public", {
    tweetId: import("convex/values").GenericId<"tweets">;
}, Promise<{
    _id: import("convex/values").GenericId<"tweets">;
    _creationTime: number;
    metadata?: {
        isThread?: boolean | undefined;
        threadPosition?: number | undefined;
        hasMedia?: boolean | undefined;
        mediaType?: string | undefined;
        language?: string | undefined;
    } | undefined;
    authorProfileImage?: string | undefined;
    isRetweet?: boolean | undefined;
    retweetedFrom?: string | undefined;
    analysisScore?: number | undefined;
    analysisReason?: string | undefined;
    embeddingId?: string | undefined;
    url?: string | undefined;
    createdAt: number;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
    tweetId: string;
    content: string;
    author: string;
    authorHandle: string;
    scrapedAt: number;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
    analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
} | null>>;
export declare const getTweetStats: import("convex/server").RegisteredQuery<"public", {
    twitterAccountId?: import("convex/values").GenericId<"twitterAccounts"> | undefined;
}, Promise<{
    _meta: {
        cacheHit: boolean;
        isStale: boolean;
        executionTime: number;
    };
    total: any;
    todayCount: any;
    weekCount: any;
    pending: any;
    analyzed: any;
    responseWorthy: any;
    averageEngagement: number;
    generatedAt: number;
}>>;
export declare const storeTweet: import("convex/server").RegisteredMutation<"public", {
    metadata?: {
        isThread?: boolean | undefined;
        threadPosition?: number | undefined;
        hasMedia?: boolean | undefined;
        mediaType?: string | undefined;
        language?: string | undefined;
    } | undefined;
    authorProfileImage?: string | undefined;
    isRetweet?: boolean | undefined;
    retweetedFrom?: string | undefined;
    url?: string | undefined;
    createdAt: number;
    twitterAccountId: import("convex/values").GenericId<"twitterAccounts">;
    tweetId: string;
    content: string;
    author: string;
    authorHandle: string;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
}, Promise<import("convex/values").GenericId<"tweets">>>;
export declare const updateTweetAnalysis: import("convex/server").RegisteredMutation<"public", {
    analysisScore?: number | undefined;
    analysisReason?: string | undefined;
    embeddingId?: string | undefined;
    tweetId: import("convex/values").GenericId<"tweets">;
    analysisStatus: "pending" | "analyzed" | "response_worthy" | "skip";
}, Promise<{
    success: boolean;
}>>;
export declare const fetchTweetsFromAccount: import("convex/server").RegisteredAction<"public", {
    maxResults?: number | undefined;
    sinceId?: string | undefined;
    handle: string;
}, Promise<{
    success: boolean;
    scraped: number;
    tweets: string[];
    latestTweetId?: string;
    error?: string;
}>>;
export declare const getTweetEngagement: import("convex/server").RegisteredAction<"public", {
    tweetId: string;
}, Promise<{
    success: boolean;
    error: string;
    engagement?: undefined;
} | {
    success: boolean;
    engagement: {
        likes: number;
        retweets: number;
        replies: number;
        views: number;
    };
    error?: undefined;
}>>;
export declare const updateTweetEngagement: import("convex/server").RegisteredMutation<"public", {
    tweetId: import("convex/values").GenericId<"tweets">;
    engagement: {
        views?: number | undefined;
        likes: number;
        retweets: number;
        replies: number;
    };
}, Promise<{
    success: boolean;
}>>;
