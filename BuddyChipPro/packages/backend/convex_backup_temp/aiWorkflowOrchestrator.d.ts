/**
 * Enhanced AI Analysis Workflow Orchestrator
 * Coordinates the complete AI analysis pipeline with vector embeddings and advanced scoring
 */
export declare const runEnhancedAnalysisWorkflow: import("convex/server").RegisteredAction<"public", {
    options?: {
        batchSize?: number | undefined;
        includeEmbeddings?: boolean | undefined;
        enhancedScoring?: boolean | undefined;
        useSemanticContext?: boolean | undefined;
        prioritizeRecent?: boolean | undefined;
    } | undefined;
    userId: import("convex/values").GenericId<"users">;
    tweetIds: import("convex/values").GenericId<"tweets">[];
}, Promise<{
    workflowId: string;
    success: boolean;
    summary: {
        totalTweets: number;
        processed: number;
        responseWorthy: number;
        errors: number;
        averageScore: number;
    };
    errors: {
        step: string;
        error: string;
    }[];
    completedAt: number;
}>>;
/**
 * Semantic content discovery workflow
 */
export declare const runSemanticDiscoveryWorkflow: import("convex/server").RegisteredAction<"public", {
    options?: {
        timeRange?: {
            start: number;
            end: number;
        } | undefined;
        maxResults?: number | undefined;
        includeUserContext?: boolean | undefined;
        minSimilarity?: number | undefined;
    } | undefined;
    userId: import("convex/values").GenericId<"users">;
    query: string;
}, Promise<{
    query: string;
    results: any | null;
    insights: any | null;
    userContext: any | null;
    opportunities: any | null;
    summary: any | null;
    discoveredAt: number | null;
}>>;
/**
 * Workflow status management
 */
export declare const createWorkflowStatus: import("convex/server").RegisteredMutation<"public", {
    userId: import("convex/values").GenericId<"users">;
    workflowId: string;
    totalTweets: number;
    options: any;
}, Promise<void>>;
export declare const updateWorkflowStatus: import("convex/server").RegisteredMutation<"public", {
    metadata?: any;
    workflowId: string;
    step: string;
    progress: number;
}, Promise<void>>;
export declare const completeWorkflow: import("convex/server").RegisteredMutation<"public", {
    workflowId: string;
    summary: any;
    results: any[];
    errors: any[];
}, Promise<void>>;
export declare const getWorkflowStatus: import("convex/server").RegisteredQuery<"public", {
    workflowId: string;
}, Promise<{
    _id: import("convex/values").GenericId<"workflowStatus">;
    _creationTime: number;
    metadata?: {
        currentBatch?: number | undefined;
        totalBatches?: number | undefined;
        lastProcessedId?: string | undefined;
        estimatedTimeRemaining?: number | undefined;
    } | undefined;
    options?: {
        priority?: string | undefined;
        batchSize?: number | undefined;
        enableParallel?: boolean | undefined;
        filters?: string[] | undefined;
    } | undefined;
    summary?: {
        analyzedTweets?: number | undefined;
        responseWorthy?: number | undefined;
        averageScore?: number | undefined;
        topTopics?: string[] | undefined;
    } | undefined;
    results?: {
        metadata?: string | undefined;
        score?: number | undefined;
        id: string;
        type: string;
    }[] | undefined;
    errors?: {
        step?: string | undefined;
        severity?: string | undefined;
        message: string;
        timestamp: number;
    }[] | undefined;
    completedAt?: number | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    workflowId: string;
    totalTweets: number;
    step: string;
    progress: number;
} | null>>;
export declare const getUserWorkflows: import("convex/server").RegisteredQuery<"public", {
    limit?: number | undefined;
    userId: import("convex/values").GenericId<"users">;
}, Promise<{
    _id: import("convex/values").GenericId<"workflowStatus">;
    _creationTime: number;
    metadata?: {
        currentBatch?: number | undefined;
        totalBatches?: number | undefined;
        lastProcessedId?: string | undefined;
        estimatedTimeRemaining?: number | undefined;
    } | undefined;
    options?: {
        priority?: string | undefined;
        batchSize?: number | undefined;
        enableParallel?: boolean | undefined;
        filters?: string[] | undefined;
    } | undefined;
    summary?: {
        analyzedTweets?: number | undefined;
        responseWorthy?: number | undefined;
        averageScore?: number | undefined;
        topTopics?: string[] | undefined;
    } | undefined;
    results?: {
        metadata?: string | undefined;
        score?: number | undefined;
        id: string;
        type: string;
    }[] | undefined;
    errors?: {
        step?: string | undefined;
        severity?: string | undefined;
        message: string;
        timestamp: number;
    }[] | undefined;
    completedAt?: number | undefined;
    createdAt: number;
    updatedAt: number;
    userId: import("convex/values").GenericId<"users">;
    workflowId: string;
    totalTweets: number;
    step: string;
    progress: number;
}[]>>;
