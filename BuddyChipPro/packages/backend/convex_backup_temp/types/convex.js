/**
 * Common Convex type definitions and utilities
 */
import { v } from "convex/values";
// Common validation schemas
export const jobStatusValidator = v.union(v.literal("pending"), v.literal("running"), v.literal("completed"), v.literal("failed"), v.literal("cancelled"));
export const priorityValidator = v.union(v.literal("high"), v.literal("medium"), v.literal("low"));
export const mentionTypeValidator = v.union(v.literal("mention"), v.literal("reply"), v.literal("quote"));
export const searchTypeValidator = v.union(v.literal("xai"), v.literal("tweetio"), v.literal("hybrid"));
// Common object schemas
export const engagementValidator = v.object({
    likes: v.number(),
    retweets: v.number(),
    replies: v.number(),
    views: v.optional(v.number()),
});
export const jobMetadataValidator = v.object({
    description: v.optional(v.string()),
    startedAt: v.optional(v.number()),
    estimatedCompletion: v.optional(v.number()),
    errorCount: v.optional(v.number()),
    lastErrorMessage: v.optional(v.string()),
});
export const jobResultsValidator = v.object({
    successCount: v.number(),
    errorCount: v.number(),
    outputData: v.optional(v.array(v.string())),
});
export const searchMetadataValidator = v.object({
    responseTime: v.optional(v.number()),
    success: v.optional(v.boolean()),
    insights: v.optional(v.array(v.string())),
    tokensUsed: v.optional(v.number()),
    model: v.optional(v.string()),
});
