{
  "extends": "../../../tsconfig.base.json",
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,
    "composite": true,
    
    // TEMP – until Convex types are updated
    "skipLibCheck": true,
    "noPropertyAccessFromIndexSignature": false,
    "exactOptionalPropertyTypes": false,

    /* These compiler options are required by Convex */
    "target": "ESNext",
    "lib": ["ES2021", "dom"],
    "module": "ESNext",
    "isolatedModules": true,
    "allowJs": true
  },
  "include": ["./**/*"],
  "exclude": ["./_generated"]
}
