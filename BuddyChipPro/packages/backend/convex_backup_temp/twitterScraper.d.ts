export declare const scrapeTweetsForAccount: import("convex/server").RegisteredAction<"public", {
    maxResults?: number | undefined;
    sinceId?: string | undefined;
    handle: string;
}, Promise<{
    success: boolean;
    scraped: number;
    tweets: string[];
    latestTweetId?: string;
    error?: string;
}>>;
export declare const liveSearch: import("convex/server").RegisteredAction<"public", {
    maxResults?: number | undefined;
    startTime?: string | undefined;
    endTime?: string | undefined;
    useXAI?: boolean | undefined;
    sources?: string[] | undefined;
    query: string;
}, Promise<{
    success: boolean;
    searchId?: string;
    content?: string;
    citations?: string[];
    tokensUsed?: number;
    query?: string;
    error?: string;
}>>;
export declare const searchMentionsForAccount: import("convex/server").RegisteredAction<"public", {
    maxResults?: number | undefined;
    startTime?: string | undefined;
    endTime?: string | undefined;
    handle: string;
}, Promise<{
    success: boolean;
    found: number;
    stored: number;
    mentions: string[];
    account: string;
    error?: string;
}>>;
export declare const bulkScrapeMentions: import("convex/server").RegisteredAction<"public", {
    accounts?: string[] | undefined;
    hoursBack?: number | undefined;
}, Promise<{
    success: boolean;
    processedAccounts: number;
    totalFound: number;
    totalStored: number;
    results: Array<{
        account: string;
        success: boolean;
        found: number;
        stored: number;
        error?: string;
    }>;
    error?: string;
}>>;
