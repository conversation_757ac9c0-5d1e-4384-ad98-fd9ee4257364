/**
 * Get comprehensive dashboard statistics for the authenticated user only
 * 🔐 SECURITY: Requires authentication and only returns user's own data
 */
export declare const getDashboardStats: any;
/**
 * Get trending topics from recent tweets and mentions - SECURED
 * 🔐 SECURITY: Requires authentication and only analyzes user's own data
 */
export declare const getTrendingTopics: any;
/**
 * Get performance analytics over time - SECURED
 * 🔐 SECURITY: Requires authentication and only analyzes user's own data
 */
export declare const getPerformanceAnalytics: any;
