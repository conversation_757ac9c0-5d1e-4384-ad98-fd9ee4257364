import { query } from "./_generated/server";

export const testConnection = query({
  handler: async (ctx) => {
    return { 
      success: true, 
      message: "Connection successful",
      timestamp: Date.now() 
    };
  },
});

/**
 * Debug query to test authentication without complex dependencies
 */
export const testAuth = query({
  args: {},
  handler: async (ctx) => {
    try {
      // Try to get auth identity
      const identity = await ctx.auth.getUserIdentity();
      
      if (!identity) {
        return {
          success: false,
          message: "No authentication identity found",
          identity: null,
          debug: {
            hasAuth: Boolean(ctx.auth),
            authType: typeof ctx.auth,
          }
        };
      }

      // Try to find user
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
        .first();

      return {
        success: true,
        message: "Authentication working!",
        identity: {
          subject: identity.subject,
          email: identity.email,
          name: identity.name,
        },
        user: user ? {
          _id: user._id,
          name: user.name,
          email: user.email,
          clerkId: user.clerkId,
        } : null,
        debug: {
          hasAuth: <PERSON><PERSON>an(ctx.auth),
          authType: typeof ctx.auth,
          identitySubject: identity.subject,
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Authentication error: ${String(error)}`,
        identity: null,
        user: null,
        debug: {
          hasAuth: Boolean(ctx.auth),
          authType: typeof ctx.auth,
          error: String(error),
        }
      };
    }
  },
});

/**
 * 🔐 SECURITY: Debug query with authentication required
 * Only authenticated users can access database statistics
 */
export const testBasicQuery = query({
  args: {},
  handler: async (ctx) => {
    try {
      // 🔐 SECURITY: Require authentication for debug endpoints
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("Authentication required for debug endpoints");
      }

      // Verify user exists in database
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
        .first();

      if (!user) {
        throw new Error("User not found in database");
      }

      // 🚀 PERFORMANCE FIX: Use efficient counting instead of .collect()
      // Get sample records to verify table existence and estimate counts
      const userSample = await ctx.db.query("users").take(1000);
      const mentionSample = await ctx.db.query("mentions").take(1000);
      const twitterAccountSample = await ctx.db.query("twitterAccounts").take(1000);

      // Estimate counts based on sample size (more efficient than loading all records)
      const userCount = userSample.length;
      const mentionCount = mentionSample.length;
      const twitterAccountCount = twitterAccountSample.length;

      console.log(`🔍 DEBUG: Efficient counting - Users: ${userCount}, Mentions: ${mentionCount}, Accounts: ${twitterAccountCount}`);

      return {
        success: true,
        message: "Basic database access working",
        authenticatedUser: {
          id: user._id,
          name: user.name,
          email: user.email,
        },
        data: {
          userCount,
          mentionCount,
          twitterAccountCount,
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Database error: ${String(error)}`,
        error: String(error),
      };
    }
  },
});

/**
 * 🔐 SECURITY: Debug query for recent mentions with authentication
 * Tests bandwidth optimizations for authenticated users only
 */
export const getRecentMentionsDebug = query({
  args: {},
  handler: async (ctx) => {
    try {
      // 🔐 SECURITY: Require authentication for debug endpoints
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("Authentication required for debug endpoints");
      }

      // Get authenticated user from database
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
        .first();

      if (!user) {
        return { error: "Authenticated user not found in database" };
      }

      // Get user's Twitter accounts
      const accounts = await ctx.db
        .query("twitterAccounts")
        .withIndex("by_user", (q) => q.eq("userId", user._id))
        .collect();

      if (!accounts || accounts.length === 0) {
        return { error: "No Twitter accounts found" };
      }

      // Get recent mentions for the first account
      const firstAccount = accounts[0];
      const mentions = await ctx.db
        .query("mentions")
        .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", firstAccount._id))
        .order("desc")
        .take(10);

      // Project to lightweight format (test our optimizations)
      const lightweightMentions = mentions.map(mention => ({
        _id: mention._id,
        content: mention.mentionContent.slice(0, 280), // Truncated
        authorName: mention.mentionAuthor,
        authorHandle: mention.mentionAuthorHandle,
        createdAt: mention.createdAt,
        discoveredAt: mention.discoveredAt,
        engagement: mention.engagement,
        mentionType: mention.mentionType,
        isProcessed: mention.isProcessed,
        hasResponse: false, // Simplified
        responseCount: 0,
        priority: mention.priority,
      }));

      return {
        success: true,
        user: {
          _id: user._id,
          name: user.name,
          email: user.email,
        },
        accounts: accounts.map(acc => ({
          _id: acc._id,
          handle: acc.handle,
          isActive: acc.isActive,
          isMonitoringEnabled: acc.isMonitoringEnabled,
        })),
        mentions: {
          data: lightweightMentions,
          nextCursor: null,
          hasMore: mentions.length === 10,
          totalEstimate: lightweightMentions.length,
        },
        debug: {
          totalMentions: mentions.length,
          accountUsed: firstAccount.handle,
          originalDataSize: JSON.stringify(mentions).length,
          optimizedDataSize: JSON.stringify(lightweightMentions).length,
          bandwidthSavings: `${Math.round((1 - JSON.stringify(lightweightMentions).length / JSON.stringify(mentions).length) * 100)}%`,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: String(error),
      };
    }
  },
});