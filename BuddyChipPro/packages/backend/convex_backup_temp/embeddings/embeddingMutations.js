import { action, mutation } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { getOpenRouterClient } from "../lib/openrouter_client";
/**
 * Generate and store embedding for a tweet
 */
export const generateTweetEmbedding = action({
    args: {
        tweetId: v.id("tweets"),
        content: v.string(),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const model = args.model || "text-embedding-3-small";
            // Generate embedding
            const embeddingResult = await client.generateEmbedding(args.content, model);
            // Store embedding
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingMutations.storeTweetEmbedding, {
                tweetId: args.tweetId,
                embedding: embeddingResult.embedding,
                model: embeddingResult.model,
            });
            // Update tweet with embedding reference
            await ctx.runMutation(api.tweets.updateTweetAnalysis, {
                tweetId: args.tweetId,
                embeddingId: embeddingId.toString(),
            });
            return {
                embeddingId,
                model: embeddingResult.model,
                dimensions: embeddingResult.embedding.length,
            };
        }
        catch (error) {
            console.error("Tweet embedding generation failed:", error);
            throw new Error(`Tweet embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Store tweet embedding in database
 */
export const storeTweetEmbedding = mutation({
    args: {
        tweetId: v.id("tweets"),
        embedding: v.array(v.number()),
        model: v.string(),
    },
    handler: async (ctx, args) => {
        // Check if embedding already exists
        const existing = await ctx.db
            .query("tweetEmbeddings")
            .withIndex("by_tweet", (q) => q.eq("tweetId", args.tweetId))
            .first();
        if (existing) {
            // Update existing embedding
            await ctx.db.patch(existing._id, {
                embedding: args.embedding,
                model: args.model,
                createdAt: Date.now(),
            });
            return existing._id;
        }
        // Create new embedding
        const embeddingId = await ctx.db.insert("tweetEmbeddings", {
            tweetId: args.tweetId,
            embedding: args.embedding,
            model: args.model,
            createdAt: Date.now(),
        });
        return embeddingId;
    },
});
/**
 * Generate and store embedding for a mention
 */
export const generateMentionEmbedding = action({
    args: {
        mentionId: v.id("mentions"),
        content: v.string(),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const model = args.model || "text-embedding-3-small";
            // Generate embedding
            const embeddingResult = await client.generateEmbedding(args.content, model);
            // Store embedding
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingMutations.storeMentionEmbedding, {
                mentionId: args.mentionId,
                embedding: embeddingResult.embedding,
                model: embeddingResult.model,
            });
            return {
                embeddingId,
                model: embeddingResult.model,
                dimensions: embeddingResult.embedding.length,
            };
        }
        catch (error) {
            console.error("Mention embedding generation failed:", error);
            throw new Error(`Mention embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Store mention embedding in database
 */
export const storeMentionEmbedding = mutation({
    args: {
        mentionId: v.id("mentions"),
        embedding: v.array(v.number()),
        model: v.string(),
    },
    handler: async (ctx, args) => {
        // Check if embedding already exists
        const existing = await ctx.db
            .query("mentionEmbeddings")
            .withIndex("by_mention", (q) => q.eq("mentionId", args.mentionId))
            .first();
        if (existing) {
            // Update existing embedding
            await ctx.db.patch(existing._id, {
                embedding: args.embedding,
                model: args.model,
                createdAt: Date.now(),
            });
            return existing._id;
        }
        // Create new embedding
        const embeddingId = await ctx.db.insert("mentionEmbeddings", {
            mentionId: args.mentionId,
            embedding: args.embedding,
            model: args.model,
            createdAt: Date.now(),
        });
        return embeddingId;
    },
});
/**
 * Generate and store embedding for a response
 */
export const generateResponseEmbedding = action({
    args: {
        responseId: v.id("responses"),
        content: v.string(),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const client = getOpenRouterClient();
            const model = args.model || "text-embedding-3-small";
            // Generate embedding
            const embeddingResult = await client.generateEmbedding(args.content, model);
            // Store embedding
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingMutations.storeResponseEmbedding, {
                responseId: args.responseId,
                embedding: embeddingResult.embedding,
                model: embeddingResult.model,
            });
            return {
                embeddingId,
                model: embeddingResult.model,
                dimensions: embeddingResult.embedding.length,
            };
        }
        catch (error) {
            console.error("Response embedding generation failed:", error);
            throw new Error(`Response embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Store response embedding in database
 */
export const storeResponseEmbedding = mutation({
    args: {
        responseId: v.id("responses"),
        embedding: v.array(v.number()),
        model: v.string(),
    },
    handler: async (ctx, args) => {
        // Check if embedding already exists
        const existing = await ctx.db
            .query("responseEmbeddings")
            .withIndex("by_response", (q) => q.eq("responseId", args.responseId))
            .first();
        if (existing) {
            // Update existing embedding
            await ctx.db.patch(existing._id, {
                embedding: args.embedding,
                model: args.model,
                createdAt: Date.now(),
            });
            return existing._id;
        }
        // Create new embedding
        const embeddingId = await ctx.db.insert("responseEmbeddings", {
            responseId: args.responseId,
            embedding: args.embedding,
            model: args.model,
            createdAt: Date.now(),
        });
        return embeddingId;
    },
});
/**
 * Generate user context embedding
 */
export const generateUserContextEmbedding = action({
    args: {
        contextType: v.string(),
        content: v.string(),
        weight: v.optional(v.number()),
        model: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
            clerkId: identity.subject,
        });
        if (!user) {
            throw new Error("User not found");
        }
        try {
            const client = getOpenRouterClient();
            const model = args.model || "text-embedding-3-small";
            // Generate embedding
            const embeddingResult = await client.generateEmbedding(args.content, model);
            // Store user context embedding
            const embeddingId = await ctx.runMutation(api.embeddings.embeddingMutations.storeUserContextEmbedding, {
                userId: user._id,
                contextType: args.contextType,
                content: args.content,
                embedding: embeddingResult.embedding,
                model: embeddingResult.model,
                weight: args.weight || 1.0,
            });
            return {
                embeddingId,
                model: embeddingResult.model,
                dimensions: embeddingResult.embedding.length,
            };
        }
        catch (error) {
            console.error("User context embedding generation failed:", error);
            throw new Error(`User context embedding failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
/**
 * Store user context embedding
 */
export const storeUserContextEmbedding = mutation({
    args: {
        userId: v.id("users"),
        contextType: v.string(),
        content: v.string(),
        embedding: v.array(v.number()),
        model: v.string(),
        weight: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        // Check if embedding of this type already exists for user
        const existing = await ctx.db
            .query("userContextEmbeddings")
            .withIndex("by_context_type", (q) => q.eq("userId", args.userId).eq("contextType", args.contextType))
            .first();
        if (existing) {
            // Update existing embedding
            await ctx.db.patch(existing._id, {
                content: args.content,
                embedding: args.embedding,
                model: args.model,
                weight: args.weight || existing.weight,
                updatedAt: Date.now(),
            });
            return existing._id;
        }
        // Create new embedding
        const embeddingId = await ctx.db.insert("userContextEmbeddings", {
            userId: args.userId,
            contextType: args.contextType,
            content: args.content,
            embedding: args.embedding,
            model: args.model,
            weight: args.weight || 1.0,
            createdAt: Date.now(),
            updatedAt: Date.now(),
        });
        return embeddingId;
    },
});
/**
 * Bulk generate embeddings for existing content
 */
export const bulkGenerateEmbeddings = action({
    args: {
        contentType: v.union(v.literal("tweets"), v.literal("mentions"), v.literal("responses")),
        limit: v.optional(v.number()),
        skipExisting: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const identity = await ctx.auth.getUserIdentity();
        if (!identity) {
            throw new Error("Not authenticated");
        }
        const user = await ctx.runQuery(api.userQueries.getUserByClerkId, {
            clerkId: identity.subject,
        });
        if (!user) {
            throw new Error("User not found");
        }
        try {
            let processed = 0;
            let errors = 0;
            const limit = args.limit || 50;
            if (args.contentType === "tweets") {
                // Get user's Twitter accounts
                const twitterAccounts = await ctx.runQuery(api.userQueries.getUserTwitterAccounts, {
                    userId: user._id,
                });
                const accountIds = twitterAccounts.map((acc) => acc._id);
                // Get tweets without embeddings
                let tweets = await ctx.runQuery(api.userQueries.getTweetsByAccountIds, {
                    accountIds,
                    limit: 10000,
                });
                if (args.skipExisting) {
                    const embeddedTweetIds = new Set((await ctx.runQuery(api.userQueries.getAllTweetEmbeddings, { limit: 10000 })).map((e) => e.tweetId));
                    tweets = tweets.filter((tweet) => !embeddedTweetIds.has(tweet._id));
                }
                tweets = tweets.slice(0, limit);
                for (const tweet of tweets) {
                    try {
                        await ctx.runAction(api.embeddings.embeddingMutations.generateTweetEmbedding, {
                            tweetId: tweet._id,
                            content: tweet.content,
                        });
                        processed++;
                    }
                    catch (error) {
                        console.error(`Failed to generate embedding for tweet ${tweet._id}:`, error);
                        errors++;
                    }
                }
            }
            else if (args.contentType === "mentions") {
                // Get user's mentions
                const twitterAccounts = await ctx.runQuery(api.userQueries.getUserTwitterAccounts, {
                    userId: user._id,
                });
                const accountIds = twitterAccounts.map((acc) => acc._id);
                let mentions = await ctx.runQuery(api.userQueries.getMentionsByAccountIds, {
                    accountIds,
                    limit: 10000,
                });
                if (args.skipExisting) {
                    const embeddedMentionIds = new Set((await ctx.runQuery(api.userQueries.getAllMentionEmbeddings, { limit: 10000 })).map((e) => e.mentionId));
                    mentions = mentions.filter((mention) => !embeddedMentionIds.has(mention._id));
                }
                mentions = mentions.slice(0, limit);
                for (const mention of mentions) {
                    try {
                        await ctx.runAction(api.embeddings.embeddingMutations.generateMentionEmbedding, {
                            mentionId: mention._id,
                            content: mention.mentionContent,
                        });
                        processed++;
                    }
                    catch (error) {
                        console.error(`Failed to generate embedding for mention ${mention._id}:`, error);
                        errors++;
                    }
                }
            }
            else if (args.contentType === "responses") {
                // Get user's responses
                const allResponses = await ctx.runQuery(api.userQueries.getAllResponses, { limit: 10000 });
                let responses = allResponses.filter((response) => response.userId === user._id).slice(0, limit);
                if (args.skipExisting) {
                    const embeddedResponseIds = new Set((await ctx.runQuery(api.userQueries.getAllResponseEmbeddings, { limit: 10000 })).map((e) => e.responseId));
                    responses = responses.filter((response) => !embeddedResponseIds.has(response._id));
                }
                for (const response of responses) {
                    try {
                        await ctx.runAction(api.embeddings.embeddingMutations.generateResponseEmbedding, {
                            responseId: response._id,
                            content: response.content,
                        });
                        processed++;
                    }
                    catch (error) {
                        console.error(`Failed to generate embedding for response ${response._id}:`, error);
                        errors++;
                    }
                }
            }
            return {
                contentType: args.contentType,
                processed,
                errors,
                total: processed + errors,
            };
        }
        catch (error) {
            console.error("Bulk embedding generation failed:", error);
            throw new Error(`Bulk embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    },
});
