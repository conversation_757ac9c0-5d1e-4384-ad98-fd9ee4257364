/**
 * Generate embeddings for tweet content
 */
export declare const generateTweetEmbedding: any;
/**
 * Generate embeddings for mention content
 */
export declare const generateMentionEmbedding: any;
/**
 * Generate embeddings for response content
 */
export declare const generateResponseEmbedding: any;
/**
 * Generate embeddings for user context
 */
export declare const generateUserContextEmbedding: any;
/**
 * Batch generate embeddings for multiple tweets
 */
export declare const generateTweetEmbeddingsBatch: any;
/**
 * Generate embeddings for semantic search query
 */
export declare const generateSearchEmbedding: any;
/**
 * Database mutations for storing embeddings
 */
export declare const storeTweetEmbedding: any;
export declare const storeMentionEmbedding: any;
export declare const storeResponseEmbedding: any;
export declare const storeUserContextEmbedding: any;
/**
 * Query functions for vector search
 */
export declare const findSimilarTweets: any;
export declare const findSimilarMentions: any;
export declare const findSimilarResponses: any;
export declare const findSimilarUserContext: any;
