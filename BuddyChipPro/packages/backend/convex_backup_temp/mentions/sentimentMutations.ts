import { mutation } from "../_generated/server";
import { v } from "convex/values";

/**
 * Mutations for managing sentiment analysis data on mentions
 */

/**
 * Update sentiment analysis for a mention
 */
export const updateMentionSentiment = mutation({
  args: {
    mentionId: v.id("mentions"),
    sentimentAnalysis: v.object({
      sentiment: v.union(v.literal("bullish"), v.literal("bearish"), v.literal("neutral")),
      sentimentScore: v.number(),
      confidence: v.number(),
      marketSentiment: v.object({
        bullishScore: v.number(),
        bearishScore: v.number(),
        neutralScore: v.number(),
        marketContext: v.array(v.string()),
      }),
      emotions: v.optional(v.object({
        excitement: v.number(),
        fear: v.number(),
        greed: v.number(),
        fomo: v.number(),
        panic: v.number(),
      })),
      reasoning: v.string(),
      keyWords: v.array(v.string()),
      analysisModel: v.string(),
      analyzedAt: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    // Get the mention to ensure it exists
    const mention = await ctx.db.get(args.mentionId);
    if (!mention) {
      throw new Error("Mention not found");
    }

    // Update the mention with sentiment analysis
    await ctx.db.patch(args.mentionId, {
      sentimentAnalysis: args.sentimentAnalysis,
    });

    return {
      success: true,
      mentionId: args.mentionId,
      sentiment: args.sentimentAnalysis.sentiment,
      score: args.sentimentAnalysis.sentimentScore,
    };
  },
});

/**
 * Batch update sentiment analysis for multiple mentions
 */
export const batchUpdateMentionSentiments = mutation({
  args: {
    updates: v.array(v.object({
      mentionId: v.id("mentions"),
      sentimentAnalysis: v.object({
        sentiment: v.union(v.literal("bullish"), v.literal("bearish"), v.literal("neutral")),
        sentimentScore: v.number(),
        confidence: v.number(),
        marketSentiment: v.object({
          bullishScore: v.number(),
          bearishScore: v.number(),
          neutralScore: v.number(),
          marketContext: v.array(v.string()),
        }),
        emotions: v.optional(v.object({
          excitement: v.number(),
          fear: v.number(),
          greed: v.number(),
          fomo: v.number(),
          panic: v.number(),
        })),
        reasoning: v.string(),
        keyWords: v.array(v.string()),
        analysisModel: v.string(),
        analyzedAt: v.number(),
      }),
    })),
  },
  handler: async (ctx, args) => {
    const results = [];
    
    for (const update of args.updates) {
      try {
        // Verify mention exists
        const mention = await ctx.db.get(update.mentionId);
        if (!mention) {
          results.push({
            mentionId: update.mentionId,
            success: false,
            error: "Mention not found",
          });
          continue;
        }

        // Update sentiment analysis
        await ctx.db.patch(update.mentionId, {
          sentimentAnalysis: update.sentimentAnalysis,
        });

        results.push({
          mentionId: update.mentionId,
          success: true,
          sentiment: update.sentimentAnalysis.sentiment,
          score: update.sentimentAnalysis.sentimentScore,
        });
      } catch (error) {
        results.push({
          mentionId: update.mentionId,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    return {
      total: args.updates.length,
      successful,
      failed,
      results,
    };
  },
});

/**
 * Remove sentiment analysis from a mention
 */
export const removeMentionSentiment = mutation({
  args: {
    mentionId: v.id("mentions"),
  },
  handler: async (ctx, args) => {
    const mention = await ctx.db.get(args.mentionId);
    if (!mention) {
      throw new Error("Mention not found");
    }

    await ctx.db.patch(args.mentionId, {
      sentimentAnalysis: undefined,
    });

    return {
      success: true,
      mentionId: args.mentionId,
    };
  },
});