import { query } from "../_generated/server";
import { v } from "convex/values";
/**
 * Advanced sentiment analytics for comprehensive insights
 */
/**
 * Get sentiment vs engagement correlation analysis
 */
export const getSentimentEngagementCorrelation = query({
    args: {
        accountId: v.optional(v.id("twitterAccounts")),
        timeRange: v.optional(v.union(v.literal("24h"), v.literal("7d"), v.literal("30d"))),
    },
    handler: async (ctx, args) => {
        const timeRange = args.timeRange || "7d";
        const now = Date.now();
        const startTime = now - (timeRange === "24h" ? 24 * 60 * 60 * 1000 :
            timeRange === "7d" ? 7 * 24 * 60 * 60 * 1000 :
                30 * 24 * 60 * 60 * 1000);
        const mentions = args.accountId
            ? await ctx.db.query("mentions")
                .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", args.accountId))
                .filter((q) => q.gte(q.field("discoveredAt"), startTime))
                .collect()
            : await ctx.db.query("mentions")
                .withIndex("by_discovered_at")
                .filter((q) => q.gte(q.field("discoveredAt"), startTime))
                .collect();
        // Filter mentions with sentiment analysis
        const withSentiment = mentions.filter(m => m.sentimentAnalysis);
        if (withSentiment.length === 0) {
            return {
                totalMentions: mentions.length,
                analyzedMentions: 0,
                correlations: {
                    bullishHighEngagement: 0,
                    bearishHighEngagement: 0,
                    sentimentEngagementScore: 0,
                },
                insights: [],
                buckets: [],
            };
        }
        // Create engagement buckets
        const buckets = [
            { label: "Very High", min: 500, mentions: [] },
            { label: "High", min: 100, max: 499, mentions: [] },
            { label: "Medium", min: 20, max: 99, mentions: [] },
            { label: "Low", min: 0, max: 19, mentions: [] },
        ];
        // Categorize mentions by engagement
        withSentiment.forEach(mention => {
            const totalEngagement = mention.engagement.likes + mention.engagement.retweets + mention.engagement.replies;
            for (const bucket of buckets) {
                if (totalEngagement >= bucket.min && (!bucket.max || totalEngagement <= bucket.max)) {
                    bucket.mentions.push({
                        ...mention,
                        totalEngagement,
                    });
                    break;
                }
            }
        });
        // Calculate sentiment distribution per bucket
        const bucketAnalysis = buckets.map(bucket => {
            const sentiments = {
                bullish: bucket.mentions.filter(m => m.sentimentAnalysis?.sentiment === "bullish").length,
                bearish: bucket.mentions.filter(m => m.sentimentAnalysis?.sentiment === "bearish").length,
                neutral: bucket.mentions.filter(m => m.sentimentAnalysis?.sentiment === "neutral").length,
            };
            const avgScore = bucket.mentions.length > 0
                ? bucket.mentions.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / bucket.mentions.length
                : 50;
            return {
                label: bucket.label,
                range: bucket.max ? `${bucket.min}-${bucket.max}` : `${bucket.min}+`,
                count: bucket.mentions.length,
                sentiments,
                averageScore: Math.round(avgScore),
                percentage: bucket.mentions.length > 0 ? Math.round((bucket.mentions.length / withSentiment.length) * 100) : 0,
            };
        });
        // Calculate correlations
        const highEngagementMentions = withSentiment.filter(m => (m.engagement.likes + m.engagement.retweets + m.engagement.replies) >= 100);
        const bullishHighEngagement = highEngagementMentions.filter(m => m.sentimentAnalysis?.sentiment === "bullish").length;
        const bearishHighEngagement = highEngagementMentions.filter(m => m.sentimentAnalysis?.sentiment === "bearish").length;
        // Generate insights
        const insights = [];
        if (bullishHighEngagement > bearishHighEngagement * 1.5) {
            insights.push({
                type: "positive",
                title: "Bullish sentiment drives engagement",
                description: `${Math.round((bullishHighEngagement / Math.max(highEngagementMentions.length, 1)) * 100)}% of high-engagement mentions are bullish`,
            });
        }
        if (bearishHighEngagement > bullishHighEngagement * 1.5) {
            insights.push({
                type: "negative",
                title: "Bearish sentiment creates buzz",
                description: `${Math.round((bearishHighEngagement / Math.max(highEngagementMentions.length, 1)) * 100)}% of high-engagement mentions are bearish`,
            });
        }
        const highScoreMentions = withSentiment.filter(m => (m.sentimentAnalysis?.sentimentScore || 50) > 75 || (m.sentimentAnalysis?.sentimentScore || 50) < 25);
        if (highScoreMentions.length > withSentiment.length * 0.3) {
            insights.push({
                type: "warning",
                title: "High sentiment volatility",
                description: `${Math.round((highScoreMentions.length / withSentiment.length) * 100)}% of mentions show extreme sentiment`,
            });
        }
        return {
            totalMentions: mentions.length,
            analyzedMentions: withSentiment.length,
            correlations: {
                bullishHighEngagement,
                bearishHighEngagement,
                sentimentEngagementScore: Math.round((bullishHighEngagement - bearishHighEngagement) / Math.max(highEngagementMentions.length, 1) * 100),
            },
            insights,
            buckets: bucketAnalysis,
        };
    },
});
/**
 * Detect sentiment alerts and anomalies
 */
export const detectSentimentAlerts = query({
    args: {
        accountId: v.optional(v.id("twitterAccounts")),
        alertThreshold: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        const alertThreshold = args.alertThreshold || 0.3; // 30% change threshold
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);
        const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);
        const mentions = args.accountId
            ? await ctx.db.query("mentions")
                .withIndex("by_monitored_account", (q) => q.eq("monitoredAccountId", args.accountId))
                .filter((q) => q.gte(q.field("discoveredAt"), twentyFourHoursAgo))
                .collect()
            : await ctx.db.query("mentions")
                .withIndex("by_discovered_at")
                .filter((q) => q.gte(q.field("discoveredAt"), twentyFourHoursAgo))
                .collect();
        const withSentiment = mentions.filter(m => m.sentimentAnalysis);
        if (withSentiment.length < 5) {
            return {
                alerts: [],
                currentSentiment: { score: 50, trend: "stable" },
                recentActivity: { mentions: withSentiment.length, timeframe: "24h" },
            };
        }
        // Split into recent (1h) and baseline (24h) periods
        const recentMentions = withSentiment.filter(m => m.discoveredAt >= oneHourAgo);
        const baselineMentions = withSentiment.filter(m => m.discoveredAt < oneHourAgo);
        const alerts = [];
        if (recentMentions.length >= 3 && baselineMentions.length >= 3) {
            // Calculate average scores for comparison
            const recentAvg = recentMentions.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / recentMentions.length;
            const baselineAvg = baselineMentions.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / baselineMentions.length;
            const change = recentAvg - baselineAvg;
            const changePercentage = Math.abs(change) / 50; // Normalize to 0-1 scale
            // Sentiment spike alerts
            if (changePercentage > alertThreshold) {
                if (change > 0) {
                    alerts.push({
                        type: "bullish_spike",
                        severity: changePercentage > 0.5 ? "high" : "medium",
                        title: "Bullish Sentiment Spike",
                        description: `Sentiment increased by ${Math.round(change)} points in the last hour`,
                        score: Math.round(recentAvg),
                        change: Math.round(change),
                        timestamp: now,
                    });
                }
                else {
                    alerts.push({
                        type: "bearish_spike",
                        severity: changePercentage > 0.5 ? "high" : "medium",
                        title: "Bearish Sentiment Drop",
                        description: `Sentiment decreased by ${Math.round(Math.abs(change))} points in the last hour`,
                        score: Math.round(recentAvg),
                        change: Math.round(change),
                        timestamp: now,
                    });
                }
            }
            // Volume alerts
            if (recentMentions.length > baselineMentions.length / 23 * 3) { // More than 3x normal hourly rate
                alerts.push({
                    type: "volume_spike",
                    severity: "medium",
                    title: "Mention Volume Spike",
                    description: `${recentMentions.length} mentions in the last hour (above normal)`,
                    score: Math.round(recentAvg),
                    change: 0,
                    timestamp: now,
                });
            }
            // Extreme sentiment alerts
            const extremeMentions = recentMentions.filter(m => {
                const score = m.sentimentAnalysis?.sentimentScore || 50;
                return score <= 20 || score >= 80;
            });
            if (extremeMentions.length >= Math.ceil(recentMentions.length * 0.6)) {
                alerts.push({
                    type: "extreme_sentiment",
                    severity: "high",
                    title: "Extreme Sentiment Activity",
                    description: `${extremeMentions.length}/${recentMentions.length} mentions show extreme sentiment`,
                    score: Math.round(recentAvg),
                    change: 0,
                    timestamp: now,
                });
            }
        }
        // Determine trend
        const currentScore = withSentiment.length > 0
            ? withSentiment.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / withSentiment.length
            : 50;
        let trend = "stable";
        if (recentMentions.length >= 2 && baselineMentions.length >= 2) {
            const recentAvg = recentMentions.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / recentMentions.length;
            const baselineAvg = baselineMentions.reduce((sum, m) => sum + (m.sentimentAnalysis?.sentimentScore || 50), 0) / baselineMentions.length;
            if (recentAvg > baselineAvg + 5)
                trend = "bullish";
            else if (recentAvg < baselineAvg - 5)
                trend = "bearish";
        }
        return {
            alerts: alerts.sort((a, b) => b.timestamp - a.timestamp),
            currentSentiment: {
                score: Math.round(currentScore),
                trend
            },
            recentActivity: {
                mentions: recentMentions.length,
                timeframe: "1h"
            },
        };
    },
});
/**
 * Get sentiment-driven response suggestions
 */
export const getSentimentBasedResponseSuggestions = query({
    args: {
        mentionId: v.id("mentions"),
    },
    handler: async (ctx, args) => {
        const mention = await ctx.db.get(args.mentionId);
        if (!mention || !mention.sentimentAnalysis) {
            return {
                suggestions: [],
                reasoning: "No sentiment analysis available",
            };
        }
        const sentiment = mention.sentimentAnalysis.sentiment;
        const score = mention.sentimentAnalysis.sentimentScore;
        const confidence = mention.sentimentAnalysis.confidence;
        const suggestions = [];
        // Base response strategy on sentiment
        if (sentiment === "bullish") {
            if (score >= 80) {
                suggestions.push({
                    strategy: "amplify",
                    tone: "celebratory",
                    template: "🚀 Love the energy! This is exactly the kind of momentum we're seeing...",
                    reasoning: "Very bullish sentiment - amplify the positivity",
                });
                suggestions.push({
                    strategy: "engage",
                    tone: "enthusiastic",
                    template: "Absolutely! The fundamentals are really strong here. What's got you most excited?",
                    reasoning: "High bullish sentiment - engage with enthusiasm",
                });
            }
            else {
                suggestions.push({
                    strategy: "support",
                    tone: "encouraging",
                    template: "Great perspective! We're seeing similar positive indicators...",
                    reasoning: "Moderately bullish - provide supportive engagement",
                });
            }
        }
        else if (sentiment === "bearish") {
            if (score <= 20) {
                suggestions.push({
                    strategy: "address_concerns",
                    tone: "empathetic",
                    template: "I understand your concerns. Let me share some context that might help...",
                    reasoning: "Very bearish sentiment - address concerns directly",
                });
                suggestions.push({
                    strategy: "provide_perspective",
                    tone: "balanced",
                    template: "These are valid concerns. Here's how we're thinking about it...",
                    reasoning: "Very bearish - provide balanced perspective",
                });
            }
            else {
                suggestions.push({
                    strategy: "acknowledge",
                    tone: "respectful",
                    template: "Fair point! It's always good to consider different perspectives...",
                    reasoning: "Moderately bearish - acknowledge viewpoint respectfully",
                });
            }
        }
        else {
            // Neutral sentiment
            suggestions.push({
                strategy: "educate",
                tone: "informative",
                template: "Great question! Here's some additional context that might be helpful...",
                reasoning: "Neutral sentiment - provide educational value",
            });
            suggestions.push({
                strategy: "engage_discussion",
                tone: "conversational",
                template: "Interesting perspective! What's your take on...",
                reasoning: "Neutral sentiment - encourage discussion",
            });
        }
        // Add confidence-based adjustments
        if (confidence < 0.7) {
            suggestions.push({
                strategy: "cautious",
                tone: "careful",
                template: "Thanks for sharing your thoughts! Always good to hear different viewpoints.",
                reasoning: "Low confidence in sentiment analysis - use cautious approach",
            });
        }
        // Priority-based suggestions
        if (mention.priority === "high") {
            suggestions.forEach((s) => {
                s.urgency = "immediate";
                s.reasoning += " (High priority mention - respond quickly)";
            });
        }
        return {
            suggestions: suggestions.slice(0, 3), // Limit to top 3 suggestions
            reasoning: `Detected ${sentiment} sentiment (${score}/100, ${Math.round(confidence * 100)}% confidence)`,
            recommendedStrategy: suggestions[0]?.strategy || "engage",
        };
    },
});
