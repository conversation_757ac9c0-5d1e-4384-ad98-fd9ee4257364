/**
 * Export user data for backup or transfer
 */
export declare const exportUserData: import("convex/server").RegisteredAction<"public", {
    includeContent?: boolean | undefined;
    includeAnalytics?: boolean | undefined;
    includeEmbeddings?: boolean | undefined;
}, Promise<{
    success: boolean;
    exportData: any;
    size: number;
    timestamp: number;
    error?: undefined;
} | {
    success: boolean;
    error: string;
    timestamp: number;
    exportData?: undefined;
    size?: undefined;
}>>;
/**
 * Get export preview (metadata only, no content)
 */
export declare const getExportPreview: any;
/**
 * Clean up old data based on retention policies
 */
export declare const cleanupOldData: any;
/**
 * Get storage usage statistics
 */
export declare const getStorageStats: any;
