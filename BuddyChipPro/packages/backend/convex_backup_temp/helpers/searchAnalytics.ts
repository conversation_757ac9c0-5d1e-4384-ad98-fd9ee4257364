import { query, mutation } from "../_generated/server";
import { v } from "convex/values";
import { Id } from "../_generated/dataModel";

// Simple helper function to get user ID
async function getUserId(ctx: any): Promise<Id<"users"> | null> {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) return null;
  
  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", identity.subject))
    .first();
    
  return user?._id || null;
}

/**
 * Search Analytics Helper Functions
 * Tracks and analyzes live search performance and patterns
 */

export const getSearchAnalytics = query({
  handler: async (ctx) => {
    const userId = await getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    try {
      // Get search history for analytics for this user
      const searches = await ctx.db
        .query("searchResults")
        .withIndex("by_user", (q: any) => q.eq("userId", userId))
        .order("desc")
        .take(1000);

      // Calculate basic stats
      const totalSearches = searches.length;
      const successfulSearches = searches.filter((s: any) => s.metadata?.success).length;
      const successRate = totalSearches > 0 ? Math.round((successfulSearches / totalSearches) * 100) : 0;

      // Calculate average response time
      const responseTimes = searches
        .filter((s: any) => s.metadata?.responseTime)
        .map((s: any) => s.metadata?.responseTime || 0);
      const avgResponseTime = responseTimes.length > 0 
        ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
        : 0;

      // Get top queries
      const queryCount: Record<string, number> = {};
      searches.forEach((s: any) => {
        if (s.query) {
          queryCount[s.query] = (queryCount[s.query] || 0) + 1;
        }
      });
      
      const topQueries = Object.entries(queryCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([query]) => query);

      // Count sources used
      const xaiSearches = searches.filter((s: any) => s.searchType === 'xai' || s.searchType === 'hybrid').length;
      const tweetioSearches = searches.filter((s: any) => s.searchType === 'tweetio' || s.searchType === 'hybrid').length;

      // Count trends detected (searches with insights)
      const trendsDetected = searches.filter((s: any) => s.metadata?.insights && s.metadata.insights.length > 0).length;

      return {
        totalSearches,
        successRate,
        avgResponseTime,
        topQueries,
        sourcesUsed: {
          xai: xaiSearches,
          tweetio: tweetioSearches
        },
        trendsDetected
      };
    } catch (error) {
      console.error("Error getting search analytics:", error);
      return {
        totalSearches: 0,
        successRate: 0,
        avgResponseTime: 0,
        topQueries: [],
        sourcesUsed: { xai: 0, tweetio: 0 },
        trendsDetected: 0
      };
    }
  },
});

export const storeSearchResult = mutation({
  args: {
    query: v.string(),
    searchType: v.string(),
    success: v.boolean(),
    resultCount: v.optional(v.number()),
    responseTime: v.optional(v.number()),
    insights: v.optional(v.array(v.string())),
    citations: v.optional(v.array(v.string())),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.insert("searchResults", {
      userId,
      query: args.query,
      searchType: args.searchType,
      content: "", // Content will be filled when available
      citations: args.citations || [],
      metadata: {
        responseTime: args.responseTime,
        success: args.success,
        insights: args.insights || [],
        tokensUsed: args.resultCount,
        model: args.searchType,
      },
      createdAt: Date.now(),
    });
  },
});

export const getSearchHistory = query({
  args: {
    limit: v.optional(v.number()),
    searchType: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    let query = ctx.db
      .query("searchResults")
      .withIndex("by_user", (q: any) => q.eq("userId", userId))
      .order("desc");

    if (args.searchType) {
      query = query.filter((q: any) => q.eq(q.field("searchType"), args.searchType));
    }

    const searches = await query.take(args.limit || 50);
    return searches;
  },
});

export const getPopularQueries = query({
  args: {
    timeframe: v.optional(v.string()), // "24h", "7d", "30d"
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Calculate time filter
    let timeFilter = 0;
    const now = Date.now();
    switch (args.timeframe) {
      case "24h":
        timeFilter = now - 24 * 60 * 60 * 1000;
        break;
      case "7d":
        timeFilter = now - 7 * 24 * 60 * 60 * 1000;
        break;
      case "30d":
        timeFilter = now - 30 * 24 * 60 * 60 * 1000;
        break;
      default:
        timeFilter = now - 7 * 24 * 60 * 60 * 1000; // Default to 7 days
    }

    const searches = await ctx.db
      .query("searchResults")
      .withIndex("by_user", (q: any) => q.eq("userId", identity.subject as Id<"users">))
      .filter((q: any) => q.gte(q.field("createdAt"), timeFilter))
      .collect();

    // Count query frequency
    const queryCount: Record<string, number> = {};
    searches.forEach((search: any) => {
      if (search.query) {
        queryCount[search.query] = (queryCount[search.query] || 0) + 1;
      }
    });

    // Sort and limit
    const popularQueries = Object.entries(queryCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, args.limit || 10)
      .map(([query, count]) => ({ query, count }));

    return popularQueries;
  },
});

export const getSearchTrends = query({
  args: {
    timeframe: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const timeframe = args.timeframe || "7d";
    let timeFilter = 0;
    const now = Date.now();
    
    switch (timeframe) {
      case "24h":
        timeFilter = now - 24 * 60 * 60 * 1000;
        break;
      case "7d":
        timeFilter = now - 7 * 24 * 60 * 60 * 1000;
        break;
      case "30d":
        timeFilter = now - 30 * 24 * 60 * 60 * 1000;
        break;
    }

    const searches = await ctx.db
      .query("searchResults")
      .withIndex("by_user", (q: any) => q.eq("userId", identity.subject as Id<"users">))
      .filter((q: any) => q.gte(q.field("createdAt"), timeFilter))
      .collect();

    // Group by time periods for trend analysis
    const periods: Record<string, { searches: number; success: number }> = {};
    const periodLength = timeframe === "24h" ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000; // 1 hour or 1 day

    searches.forEach((search: any) => {
      const periodKey = Math.floor(search.createdAt / periodLength) * periodLength;
      const periodStr = new Date(periodKey).toISOString();
      
      if (!periods[periodStr]) {
        periods[periodStr] = { searches: 0, success: 0 };
      }
      
      periods[periodStr].searches++;
      if (search.metadata?.success) {
        periods[periodStr].success++;
      }
    });

    // Convert to array and sort by time
    const trends = Object.entries(periods)
      .map(([period, data]) => ({
        period,
        ...data,
        successRate: data.searches > 0 ? Math.round((data.success / data.searches) * 100) : 0
      }))
      .sort((a, b) => new Date(a.period).getTime() - new Date(b.period).getTime());

    return trends;
  },
});

export const getInsightsSummary = query({
  args: {
    timeframe: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const timeframe = args.timeframe || "7d";
    let timeFilter = 0;
    const now = Date.now();
    
    switch (timeframe) {
      case "24h":
        timeFilter = now - 24 * 60 * 60 * 1000;
        break;
      case "7d":
        timeFilter = now - 7 * 24 * 60 * 60 * 1000;
        break;
      case "30d":
        timeFilter = now - 30 * 24 * 60 * 60 * 1000;
        break;
    }

    const searches = await ctx.db
      .query("searchResults")
      .withIndex("by_user", (q: any) => q.eq("userId", identity.subject as Id<"users">))
      .filter((q: any) => q.gte(q.field("createdAt"), timeFilter))
      .collect();

    // Collect all insights
    const allInsights: string[] = [];
    searches.forEach((search: any) => {
      if (search.metadata?.insights) {
        allInsights.push(...search.metadata.insights);
      }
    });

    // Count insight types (simple keyword matching)
    const insightTypes = {
      trends: allInsights.filter(i => i.toLowerCase().includes('trend')).length,
      sentiment: allInsights.filter(i => i.toLowerCase().includes('sentiment')).length,
      opportunities: allInsights.filter(i => i.toLowerCase().includes('opportunity')).length,
      risks: allInsights.filter(i => i.toLowerCase().includes('risk')).length,
    };

    return {
      totalInsights: allInsights.length,
      insightTypes,
      searchesWithInsights: searches.filter((s: any) => s.metadata?.insights && s.metadata.insights.length > 0).length,
      totalSearches: searches.length,
    };
  },
});