/**
 * Remove a wallet from user's account
 */
export declare const removeWallet: import("convex/server").RegisteredMutation<"public", {
    walletId: import("convex/values").GenericId<"wallets">;
}, Promise<{
    success: boolean;
}>>;
/**
 * Update wallet metadata
 */
export declare const updateWalletMetadata: import("convex/server").RegisteredMutation<"public", {
    ensName?: string | undefined;
    solanaName?: string | undefined;
    balance?: string | undefined;
    lastBalanceUpdate?: number | undefined;
    publicKey?: string | undefined;
    walletId: import("convex/values").GenericId<"wallets">;
}, Promise<{
    _id: import("convex/values").GenericId<"wallets">;
    _creationTime: number;
    lastUsedAt?: number | undefined;
    metadata?: {
        ensName?: string | undefined;
        solanaName?: string | undefined;
        balance?: string | undefined;
        lastBalanceUpdate?: number | undefined;
        publicKey?: string | undefined;
    } | undefined;
    userId: import("convex/values").GenericId<"users">;
    address: string;
    blockchain: "ethereum" | "solana" | "polygon" | "base";
    walletType: string;
    verified: boolean;
    isPrimary: boolean;
    connectedVia: "clerk" | "manual";
    connectedAt: number;
} | null>>;
/**
 * Update user wallet preferences
 */
export declare const updateWalletPreferences: import("convex/server").RegisteredMutation<"public", {
    preferredBlockchain?: "ethereum" | "solana" | "polygon" | "base" | undefined;
    autoConnectWallet?: boolean | undefined;
    showBalances?: boolean | undefined;
}, Promise<{
    _id: import("convex/values").GenericId<"users">;
    _creationTime: number;
    image?: string | undefined;
    primaryWalletId?: import("convex/values").GenericId<"wallets"> | undefined;
    walletPreferences?: {
        preferredBlockchain?: "ethereum" | "solana" | "polygon" | "base" | undefined;
        autoConnectWallet?: boolean | undefined;
        showBalances?: boolean | undefined;
    } | undefined;
    lastMentionRefresh?: number | undefined;
    updatedAt?: number | undefined;
    name: string;
    email: string;
    clerkId: string;
    createdAt: number;
} | null>>;
/**
 * Mark wallet as recently used
 */
export declare const markWalletUsed: import("convex/server").RegisteredMutation<"public", {
    walletId: import("convex/values").GenericId<"wallets">;
}, Promise<{
    success: boolean;
}>>;
/**
 * Refresh wallet balance (to be called from frontend)
 */
export declare const refreshWalletBalance: import("convex/server").RegisteredMutation<"public", {
    balance: string;
    walletId: import("convex/values").GenericId<"wallets">;
}, Promise<{
    _id: import("convex/values").GenericId<"wallets">;
    _creationTime: number;
    lastUsedAt?: number | undefined;
    metadata?: {
        ensName?: string | undefined;
        solanaName?: string | undefined;
        balance?: string | undefined;
        lastBalanceUpdate?: number | undefined;
        publicKey?: string | undefined;
    } | undefined;
    userId: import("convex/values").GenericId<"users">;
    address: string;
    blockchain: "ethereum" | "solana" | "polygon" | "base";
    walletType: string;
    verified: boolean;
    isPrimary: boolean;
    connectedVia: "clerk" | "manual";
    connectedAt: number;
} | null>>;
