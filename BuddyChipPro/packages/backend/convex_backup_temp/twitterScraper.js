import { action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";
import { createTweetIOClient, normalizeTwitterTweet } from "./lib/twitter_client";
import { createXAIClient } from "./lib/xai_client";
import { checkRateLimit } from "./lib/rate_limiter";
export const scrapeTweetsForAccount = action({
    args: {
        handle: v.string(),
        maxResults: v.optional(v.number()),
        sinceId: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            // 🔐 SECURITY: Apply rate limiting for Twitter API operations
            await checkRateLimit(ctx, 'scrapeTweetsForAccount', 'twitterApi');
            const apiKey = process.env.TWEETIO_API_KEY;
            const tweetioClient = createTweetIOClient(apiKey);
            // Get the Twitter account from our database
            const account = await ctx.runQuery(api.userQueries.getTwitterAccountByHandle, {
                handle: args.handle,
            });
            if (!account) {
                // Create the account if it doesn't exist
                const result = await tweetioClient.getUserByUsername(args.handle);
                if (!result) {
                    throw new Error(`User @${args.handle} not found on Twitter`);
                }
                // We'll need the user ID to create the account
                // For now, return an error asking to add the account first
                throw new Error(`Twitter account @${args.handle} not found in database. Please add it first.`);
            }
            // Fetch tweets from TweetIO API
            const result = await tweetioClient.getTweetsByUsername(args.handle, {
                maxResults: args.maxResults || 20,
                sinceId: args.sinceId,
                excludeReplies: true,
                excludeRetweets: false,
            });
            if (!result.targetUser) {
                throw new Error(`User @${args.handle} not found on Twitter`);
            }
            const storedTweets = [];
            // Store each tweet in the database
            for (const tweet of result.tweets) {
                const normalizedTweet = normalizeTwitterTweet(tweet, result.users);
                const tweetId = await ctx.runMutation(api.tweets.storeTweet, {
                    twitterAccountId: account._id,
                    ...normalizedTweet,
                });
                storedTweets.push(tweetId);
            }
            // Update the account's last scraped timestamp
            await ctx.runMutation(api.helperMutations.updateTwitterAccountLastScraped, {
                accountId: account._id,
                timestamp: Date.now(),
            });
            return {
                success: true,
                scraped: storedTweets.length,
                tweets: storedTweets,
                latestTweetId: result.tweets[0]?.id,
            };
        }
        catch (error) {
            console.error("Error scraping tweets:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                scraped: 0,
                tweets: [],
            };
        }
    },
});
export const liveSearch = action({
    args: {
        query: v.string(),
        maxResults: v.optional(v.number()),
        startTime: v.optional(v.string()),
        endTime: v.optional(v.string()),
        useXAI: v.optional(v.boolean()),
        sources: v.optional(v.array(v.string())),
    },
    handler: async (ctx, args) => {
        try {
            // Use xAI Live Search if enabled and available
            if (args.useXAI && process.env.XAI_API_KEY) {
                const xaiClient = createXAIClient();
                const searchRequest = {
                    messages: [
                        {
                            role: "system",
                            content: "You are a social media search assistant. Find and analyze relevant content based on the user's query. Provide structured results with engagement metrics and relevance scores."
                        },
                        {
                            role: "user",
                            content: `Search for: ${args.query}. Include engagement data, author information, and relevance analysis.`
                        }
                    ],
                    model: "grok-3-latest",
                    search_parameters: {
                        mode: "on",
                        sources: (args.sources || ["x", "web"]).map(type => ({ type: type })),
                        from_date: args.startTime,
                        to_date: args.endTime,
                        max_search_results: args.maxResults || 20,
                        return_citations: true
                    },
                    temperature: 0.3,
                    max_tokens: 2500
                };
                const xaiResult = await xaiClient.liveSearch(searchRequest);
                // Store xAI search result
                const searchId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
                    searchType: "live_search",
                    query: args.query,
                    content: xaiResult.choices[0]?.message.content || "",
                    citations: xaiResult.citations || [],
                    createdAt: Date.now(),
                });
                return {
                    success: true,
                    searchId,
                    content: xaiResult.choices[0]?.message.content || "",
                    citations: xaiResult.citations || [],
                    tokensUsed: xaiResult.usage?.total_tokens || 0,
                    query: args.query,
                };
            }
            // Fallback to TweetIO for traditional search
            const apiKey = process.env.TWEETIO_API_KEY;
            const tweetioClient = createTweetIOClient(apiKey);
            const result = await tweetioClient.searchTweets(args.query, {
                maxResults: args.maxResults || 20,
                queryType: "Latest",
            });
            // Format results for frontend
            const formattedResults = result.tweets.map(tweet => {
                const author = result.users.find(u => u.id === tweet.author_id);
                return {
                    id: tweet.id,
                    content: tweet.text,
                    author: author?.name || "Unknown",
                    authorHandle: author?.username || "unknown",
                    authorProfileImage: author?.profile_image_url,
                    createdAt: new Date(tweet.created_at).getTime(),
                    engagement: {
                        likes: tweet.public_metrics?.like_count || 0,
                        retweets: tweet.public_metrics?.retweet_count || 0,
                        replies: tweet.public_metrics?.reply_count || 0,
                        views: tweet.public_metrics?.impression_count || 0,
                    },
                    url: `https://twitter.com/${author?.username || "unknown"}/status/${tweet.id}`,
                    isRetweet: tweet.referenced_tweets?.some(ref => ref.type === "retweeted") || false,
                };
            });
            return {
                success: true,
            };
        }
        catch (error) {
            console.error("Error performing live search:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    },
});
export const searchMentionsForAccount = action({
    args: {
        handle: v.string(),
        maxResults: v.optional(v.number()),
        startTime: v.optional(v.string()),
        endTime: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
        try {
            const apiKey = process.env.TWEETIO_API_KEY;
            const tweetioClient = createTweetIOClient(apiKey);
            // Get mentions of the account
            const result = await tweetioClient.searchMentions(args.handle, {
                maxResults: args.maxResults || 50,
                startTime: args.startTime,
                endTime: args.endTime,
            });
            // Get the monitored account from our database
            const account = await ctx.runQuery(api.userQueries.getTwitterAccountByHandle, {
                handle: args.handle,
            });
            if (!account) {
                throw new Error(`Twitter account @${args.handle} not found in database`);
            }
            const storedMentions = [];
            // Store mentions in the database
            for (const tweet of result.tweets) {
                const author = result.users.find(u => u.id === tweet.author_id);
                if (!author)
                    continue;
                // Use helper mutation to store mention with duplicate check
                const mentionId = await ctx.runMutation(api.mentions.mentionMutations.storeMentionWithCheck, {
                    mentionTweetId: tweet.id,
                    mentionContent: tweet.text,
                    mentionAuthor: author.name,
                    mentionAuthorHandle: author.username,
                    mentionAuthorFollowers: author.followers_count,
                    mentionAuthorVerified: author.verified,
                    monitoredAccountId: account._id,
                    mentionType: tweet.in_reply_to_user_id
                        ? "reply"
                        : tweet.referenced_tweets?.some(ref => ref.type === "quoted")
                            ? "quote"
                            : "mention",
                    originalTweetId: tweet.conversation_id !== tweet.id ? tweet.conversation_id : undefined,
                    engagement: {
                        likes: tweet.public_metrics?.like_count || 0,
                        retweets: tweet.public_metrics?.retweet_count || 0,
                        replies: tweet.public_metrics?.reply_count || 0,
                        views: tweet.public_metrics?.impression_count || 0,
                    },
                    priority: author.verified || (author.followers_count || 0) > 10000
                        ? "high"
                        : (author.followers_count || 0) > 1000
                            ? "medium"
                            : "low",
                    url: `https://twitter.com/${author.username}/status/${tweet.id}`,
                    createdAt: new Date(tweet.created_at).getTime(),
                });
                if (mentionId) {
                    storedMentions.push(mentionId);
                }
            }
            return {
                success: true,
                found: result.tweets.length,
                stored: storedMentions.length,
                mentions: storedMentions,
                account: args.handle,
            };
        }
        catch (error) {
            console.error("Error searching mentions:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                found: 0,
                stored: 0,
                mentions: [],
                account: args.handle,
            };
        }
    },
});
export const bulkScrapeMentions = action({
    args: {
        accounts: v.optional(v.array(v.string())),
        hoursBack: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        try {
            // Get all active monitored accounts or use provided list
            const allAccounts = await ctx.runQuery(api.userQueries.getActiveTwitterAccounts, {});
            const accounts = args.accounts
                ? allAccounts.filter((acc) => args.accounts.includes(acc.handle))
                : allAccounts.filter((acc) => acc.isMonitoringEnabled);
            if (accounts.length === 0) {
                return {
                    success: true,
                    error: "No accounts to monitor",
                    processedAccounts: 0,
                    totalFound: 0,
                    totalStored: 0,
                    results: [],
                };
            }
            const hoursBack = args.hoursBack || 24;
            const startTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();
            const results = [];
            // Process each account
            for (const account of accounts) {
                try {
                    const result = await ctx.runAction(api.twitterScraper.searchMentionsForAccount, {
                        handle: account.handle,
                        maxResults: 50,
                        startTime,
                    });
                    results.push({
                        account: account.handle,
                        ...result,
                    });
                    // Add a small delay to avoid hitting rate limits too hard
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                catch (error) {
                    console.error(`Error processing account ${account.handle}:`, error);
                    results.push({
                        account: account.handle,
                        success: false,
                        error: error instanceof Error ? error.message : "Unknown error",
                        found: 0,
                        stored: 0,
                    });
                }
            }
            const totalFound = results.reduce((sum, r) => sum + (r.found || 0), 0);
            const totalStored = results.reduce((sum, r) => sum + (r.stored || 0), 0);
            return {
                success: true,
                processedAccounts: accounts.length,
                totalFound,
                totalStored,
                results,
            };
        }
        catch (error) {
            console.error("Error in bulk mention scraping:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                processedAccounts: 0,
                totalFound: 0,
                totalStored: 0,
                results: [],
            };
        }
    },
});
