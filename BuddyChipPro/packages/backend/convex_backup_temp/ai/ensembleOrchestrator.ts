import { mutation, action, query } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { getOpenRouterClient } from "../lib/openrouter_client";

/**
 * Ensemble Orchestrator for AI-powered mention monitoring
 * Combines multiple AI models and strategies for optimal mention processing
 */

// Simplified interfaces for Convex JSON validation compatibility
export interface EnsembleConfig {
  analysisModels: string;
  priorityModels: string;
  responseModels: string;
  confidenceThreshold: number;
  priorityThreshold: number;
  engagementThreshold: number;
  followersWeight: number;
  engagementWeight: number;
  verifiedWeight: number;
  timingWeight: number;
}

export interface MentionAnalysis {
  shouldRespond: boolean;
  confidence: number;
  priority: "high" | "medium" | "low";
  sentiment: "positive" | "negative" | "neutral" | "mixed";
  topics: string;
  urgency: "immediate" | "soon" | "low";
  responseStrategy: string;
  riskLevel: "low" | "medium" | "high";
  reasoning: string;
  // Enhanced sentiment analysis
  sentimentAnalysis?: {
    sentiment: "bullish" | "bearish" | "neutral";
    sentimentScore: number;
    confidence: number;
    reasoning: string;
    keyWords: string[];
  };
}

export interface ProcessingMetrics {
  startTime: number;
  endTime: number;
  duration: number;
  modelsUsed: string;
  cacheHits: number;
  cacheMisses: number;
  errorCount: number;
}

/**
 * Ensemble AI analysis for mentions with advanced optimization
 */
export const analyzeMentionEnsemble = action({
  args: {
    mentionId: v.id("mentions"),
    useCache: v.optional(v.boolean()),
    priority: v.optional(v.union(v.literal("high"), v.literal("medium"), v.literal("low"))),
    timeout: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const startTime = Date.now();
    const metrics: ProcessingMetrics = {
      startTime,
      endTime: 0,
      duration: 0,
      modelsUsed: "",
      cacheHits: 0,
      cacheMisses: 0,
      errorCount: 0,
    };

    try {
      // Get mention details
      const mention = await ctx.runQuery(api.mentions.mentionQueries.getMentionById, {
        mentionId: args.mentionId,
      });

      if (!mention) {
        throw new Error("Mention not found");
      }

      // Check cache first if enabled
      if (args.useCache !== false) {
        const cached = await checkAnalysisCache(ctx, mention.mentionTweetId);
        if (cached) {
          metrics.cacheHits++;
          return {
            analysis: cached,
            metrics: {
              ...metrics,
              endTime: Date.now(),
              duration: Date.now() - startTime,
            },
            cached: true,
          };
        }
        metrics.cacheMisses++;
      }

      // Get ensemble configuration
      const config = await getEnsembleConfig(ctx);
      
      // Determine processing strategy based on priority
      const strategy = args.priority === "high" ? "comprehensive" : "optimized";
      
      // Run ensemble analysis with sentiment
      const analysis = await runEnsembleAnalysis(
        ctx,
        mention,
        config,
        strategy,
        metrics,
        args.timeout || 30000
      );

      // Add sentiment analysis if not already included
      if (!analysis.sentimentAnalysis && strategy === "comprehensive") {
        try {
          const sentimentResult = await ctx.runAction(api.ai.sentimentAnalysis.analyzeMentionSentiment, {
            mentionContent: mention.mentionContent,
            mentionAuthor: mention.mentionAuthor,
            mentionAuthorHandle: mention.mentionAuthorHandle,
            mentionType: mention.mentionType,
            accountHandle: mention.monitoredAccount?.handle || "unknown",
            engagement: mention.engagement,
            priority: args.priority,
          });

          analysis.sentimentAnalysis = {
            sentiment: sentimentResult.sentiment,
            sentimentScore: sentimentResult.sentimentScore,
            confidence: sentimentResult.confidence,
            reasoning: sentimentResult.reasoning,
            keyWords: sentimentResult.keyWords,
          };
        } catch (sentimentError) {
          console.error("Failed to add sentiment analysis:", sentimentError);
          // Continue without sentiment analysis
        }
      }

      // Cache results for future use
      if (args.useCache !== false) {
        await cacheAnalysisResult(ctx, mention.mentionTweetId, analysis);
      }

      metrics.endTime = Date.now();
      metrics.duration = metrics.endTime - startTime;

      return {
        analysis,
        metrics,
        cached: false,
      };
    } catch (error) {
      metrics.errorCount++;
      metrics.endTime = Date.now();
      metrics.duration = metrics.endTime - startTime;

      console.error("Ensemble analysis failed:", error);
      
      // Return fallback analysis
      const fallbackAnalysis = getFallbackAnalysis();
      return {
        analysis: fallbackAnalysis,
        metrics,
        cached: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

/**
 * Get ensemble configuration with smart defaults
 */
async function getEnsembleConfig(ctx: any): Promise<EnsembleConfig> {
  // Try to get user-specific config, fall back to defaults
  return {
    analysisModels: "google/gemini-2.0-flash-exp:free,google/gemini-flash-1.5-8b:free,meta-llama/llama-3.1-8b-instruct:free",
    priorityModels: "google/gemini-2.0-flash-exp:free",
    responseModels: "google/gemini-pro-1.5,anthropic/claude-3.5-haiku",
    confidenceThreshold: 0.7,
    priorityThreshold: 0.8,
    engagementThreshold: 100,
    followersWeight: 0.3,
    engagementWeight: 0.4,
    verifiedWeight: 0.2,
    timingWeight: 0.1,
  };
}

/**
 * Run ensemble analysis with multiple AI models
 */
async function runEnsembleAnalysis(
  ctx: any,
  mention: any,
  config: EnsembleConfig,
  strategy: "comprehensive" | "optimized",
  metrics: ProcessingMetrics,
  timeout: number
): Promise<MentionAnalysis> {
  const client = getOpenRouterClient();
  const analyses: Partial<MentionAnalysis>[] = [];
  
  // Parse models from config
  const analysisModelsList = config.analysisModels.split(',');
  const modelsToUse = strategy === "comprehensive" 
    ? analysisModelsList 
    : [analysisModelsList[0]]; // Use fastest model for optimized strategy

  // Run analysis with multiple models in parallel with progressive fallback
  const analysisPromises = modelsToUse.map(async (model, index) => {
    try {
      metrics.modelsUsed = metrics.modelsUsed ? `${metrics.modelsUsed},${model}` : model;
      // Add progressive delay to reduce API pressure
      if (index > 0) {
        await new Promise(resolve => setTimeout(resolve, index * 500));
      }
      const analysis = await analyzeWithModel(client, mention, model, config);
      return { analysis, model, success: true };
    } catch (error) {
      metrics.errorCount++;
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Model ${model} failed:`, { error: errorMessage, attempt: index + 1 });
      
      // Try fallback with simplified prompt if main model fails
      if (index === 0 && modelsToUse.length > 1) {
        try {
          const fallbackAnalysis = await analyzeWithSimplifiedPrompt(client, mention, model);
          console.log(`Fallback analysis succeeded for ${model}`);
          return { analysis: fallbackAnalysis, model: `${model}_fallback`, success: true };
        } catch (fallbackError) {
          console.error(`Fallback also failed for ${model}:`, fallbackError);
        }
      }
      
      return { analysis: null, model, success: false, error: errorMessage };
    }
  });

  // Wait for all analyses with timeout
  const results = await Promise.allSettled(
    analysisPromises.map(p => 
      Promise.race([
        p,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error("Analysis timeout")), timeout)
        )
      ])
    )
  );

  // Collect successful analyses with enhanced error reporting
  for (const result of results) {
    if (result.status === "fulfilled" && result.value) {
      const resultValue = result.value as any;
      if (resultValue.success && resultValue.analysis) {
        analyses.push(resultValue.analysis);
      }
    }
  }

  // Enhanced fallback strategy if no analyses succeeded
  if (analyses.length === 0) {
    console.error("All ensemble models failed, attempting emergency fallback...");
    
    // Try one more time with the most reliable model and minimal prompt
    try {
      const emergencyModel = "google/gemini-2.0-flash-exp:free"; // Most reliable free model
      const emergencyAnalysis = await analyzeWithEmergencyPrompt(client, mention, emergencyModel);
      analyses.push(emergencyAnalysis);
      metrics.modelsUsed += `,${emergencyModel}_emergency`;
      console.log("Emergency fallback succeeded");
    } catch (emergencyError) {
      console.error("Emergency fallback also failed:", emergencyError);
      throw new Error("All model analyses failed including emergency fallback");
    }
  }

  // Combine results using ensemble voting
  return combineAnalyses(analyses, config, mention);
}

/**
 * Analyze mention with a specific AI model
 */
async function analyzeWithModel(
  client: { generateCompletion: (prompt: string, options: any) => Promise<{ content: string }> },
  mention: {
    mentionContent: string;
    mentionAuthorHandle: string;
    mentionAuthor: string;
    mentionType: string;
    mentionAuthorVerified?: boolean;
    mentionAuthorFollowers?: number;
    engagement: { likes: number; retweets: number };
  },
  model: string,
  config: EnsembleConfig
): Promise<Partial<MentionAnalysis>> {
  const prompt = `
Analyze this mention for response appropriateness and strategy:

Mention: "${mention.mentionContent}"
Author: @${mention.mentionAuthorHandle} (${mention.mentionAuthor})
Type: ${mention.mentionType}
${mention.mentionAuthorVerified ? 'Verified account' : 'Unverified account'}
Followers: ${mention.mentionAuthorFollowers || 0}
Engagement: ${mention.engagement.likes} likes, ${mention.engagement.retweets} retweets

Provide analysis in this exact JSON format:
{
  "shouldRespond": boolean,
  "confidence": number (0-1),
  "priority": "high" | "medium" | "low",
  "sentiment": "positive" | "negative" | "neutral" | "mixed",
  "topics": ["topic1", "topic2"],
  "urgency": "immediate" | "soon" | "low",
  "responseStrategy": "engage" | "educate" | "support" | "promote" | "deflect",
  "riskLevel": "low" | "medium" | "high",
  "reasoning": "explanation for decision"
}`;

  const response = await client.generateCompletion(prompt, {
    model,
    systemPrompt: 'You are an expert social media manager who analyzes mentions to determine the best response strategy. Always return valid JSON.',
    maxTokens: 500,
    temperature: 0.3,
  });

  try {
    return JSON.parse(response.content);
  } catch (parseError) {
    console.error(`Failed to parse response from ${model}:`, response.content);
    throw new Error(`Invalid JSON response from ${model}`);
  }
}

/**
 * Combine multiple analyses using ensemble voting
 */
function combineAnalyses(
  analyses: Partial<MentionAnalysis>[],
  config: EnsembleConfig,
  mention: {
    mentionAuthorFollowers?: number;
    engagement: { likes: number; retweets: number };
    createdAt: number;
  }
): MentionAnalysis {
  const combined: MentionAnalysis = {
    shouldRespond: false,
    confidence: 0,
    priority: "low",
    sentiment: "neutral",
    topics: "",
    urgency: "low",
    responseStrategy: "engage",
    riskLevel: "low",
    reasoning: "Ensemble analysis",
  };

  // Voting for boolean fields
  const shouldRespondVotes = analyses.filter(a => a.shouldRespond === true).length;
  combined.shouldRespond = shouldRespondVotes > analyses.length / 2;

  // Average confidence
  const confidences = analyses.map(a => a.confidence || 0).filter(c => c > 0);
  combined.confidence = confidences.length > 0 
    ? confidences.reduce((sum, c) => sum + c, 0) / confidences.length 
    : 0;

  // Priority by highest vote
  const priorities = analyses.map(a => a.priority).filter(Boolean);
  const priorityCount = { high: 0, medium: 0, low: 0 };
  priorities.forEach(p => p && priorityCount[p]++);
  combined.priority = Object.entries(priorityCount).reduce((a, b) => 
    priorityCount[a[0] as keyof typeof priorityCount] > priorityCount[b[0] as keyof typeof priorityCount] ? a : b
  )[0] as "high" | "medium" | "low";

  // Sentiment by majority vote
  const sentiments = analyses.map(a => a.sentiment).filter(Boolean);
  const sentimentCount = { positive: 0, negative: 0, neutral: 0, mixed: 0 };
  sentiments.forEach(s => s && sentimentCount[s]++);
  combined.sentiment = Object.entries(sentimentCount).reduce((a, b) => 
    sentimentCount[a[0] as keyof typeof sentimentCount] > sentimentCount[b[0] as keyof typeof sentimentCount] ? a : b
  )[0] as "positive" | "negative" | "neutral" | "mixed";

  // Combine topics as comma-separated string
  const allTopics = analyses.flatMap(a => a.topics ? a.topics.split(',') : []);
  const topicCount: Record<string, number> = {};
  allTopics.forEach(topic => {
    const cleanTopic = topic.trim();
    if (cleanTopic) {
      topicCount[cleanTopic] = (topicCount[cleanTopic] || 0) + 1;
    }
  });
  const topTopics = Object.entries(topicCount)
    .filter(([_, count]) => count >= Math.ceil(analyses.length / 2))
    .map(([topic]) => topic)
    .slice(0, 5);
  combined.topics = topTopics.join(',');

  // Strategy by consensus
  const strategies = analyses.map(a => a.responseStrategy).filter(Boolean);
  const strategyCount: Record<string, number> = {};
  strategies.forEach(s => {
    if (s) strategyCount[s] = (strategyCount[s] || 0) + 1;
  });
  const topStrategy = Object.entries(strategyCount).reduce((a, b) => 
    a[1] > b[1] ? a : b
  );
  combined.responseStrategy = topStrategy[0] || "engage";

  // Apply context-based adjustments
  applyContextualAdjustments(combined, mention, config);

  return combined;
}

/**
 * Apply contextual adjustments based on mention metadata
 */
function applyContextualAdjustments(
  analysis: MentionAnalysis,
  mention: any,
  config: EnsembleConfig
): void {
  // Boost priority for verified users
  if (mention.mentionAuthorVerified && analysis.priority !== "high") {
    analysis.priority = "medium";
    analysis.confidence = Math.min(analysis.confidence + 0.1, 1.0);
  }

  // Boost for high engagement
  const totalEngagement = mention.engagement.likes + mention.engagement.retweets + mention.engagement.replies;
  if (totalEngagement > config.engagementThreshold) {
    analysis.confidence = Math.min(analysis.confidence + 0.15, 1.0);
    if (analysis.priority === "low") analysis.priority = "medium";
  }

  // Adjust urgency based on timing and context
  const hoursSinceCreated = (Date.now() - mention.createdAt) / (1000 * 60 * 60);
  if (hoursSinceCreated < 2 && analysis.priority === "high") {
    analysis.urgency = "immediate";
  } else if (hoursSinceCreated < 12 && analysis.priority !== "low") {
    analysis.urgency = "soon";
  }

  // Risk assessment
  if (mention.mentionContent.toLowerCase().includes("scam") || 
      mention.mentionContent.toLowerCase().includes("fake")) {
    analysis.riskLevel = "high";
    analysis.shouldRespond = false;
  }
}

/**
 * Check cache for existing analysis
 */
async function checkAnalysisCache(ctx: any, tweetId: string): Promise<MentionAnalysis | null> {
  try {
    // Check if we have a recent analysis cached
    const recent = await ctx.db
      .query("mentions")
      .filter((q: any) => q.eq(q.field("mentionTweetId"), tweetId))
      .first();

    if (recent?.aiAnalysisResult && recent.processedAt && 
        (Date.now() - recent.processedAt) < 24 * 60 * 60 * 1000) { // 24 hour cache
      return {
        shouldRespond: recent.aiAnalysisResult.shouldRespond,
        confidence: recent.aiAnalysisResult.confidence || 0.7,
        priority: "medium", // Default fallback
        sentiment: recent.aiAnalysisResult.sentiment as any || "neutral",
        topics: Array.isArray(recent.aiAnalysisResult.topics) 
          ? recent.aiAnalysisResult.topics.join(',') 
          : recent.aiAnalysisResult.topics || "",
        urgency: "low",
        responseStrategy: recent.aiAnalysisResult.responseStrategy || "engage",
        riskLevel: "low",
        reasoning: "Cached analysis",
      };
    }
    return null;
  } catch (error) {
    console.error("Cache check failed:", error);
    return null;
  }
}

/**
 * Cache analysis result for future use
 */
async function cacheAnalysisResult(
  ctx: any,
  tweetId: string,
  analysis: MentionAnalysis
): Promise<void> {
  try {
    // We'll store this in the mention's aiAnalysisResult field
    // This is handled by the calling function
  } catch (error) {
    console.error("Failed to cache analysis:", error);
  }
}

/**
 * Analyze with simplified prompt for fallback scenarios
 */
async function analyzeWithSimplifiedPrompt(
  client: { generateCompletion: (prompt: string, options: any) => Promise<{ content: string }> },
  mention: any,
  model: string
): Promise<Partial<MentionAnalysis>> {
  const simplePrompt = `Analyze this mention: "${mention.mentionContent}"
Is this worth responding to? Return JSON:
{"shouldRespond": boolean, "confidence": 0.1-1.0, "priority": "low/medium/high", "sentiment": "positive/negative/neutral"}`;

  const response = await client.generateCompletion(simplePrompt, {
    model,
    maxTokens: 200,
    temperature: 0.1,
  });

  try {
    const parsed = JSON.parse(response.content);
    return {
      shouldRespond: parsed.shouldRespond || false,
      confidence: parsed.confidence || 0.5,
      priority: parsed.priority || "low",
      sentiment: parsed.sentiment || "neutral",
      topics: "general",
      urgency: "low",
      responseStrategy: "engage",
      riskLevel: "low",
      reasoning: "Simplified analysis due to model failure",
    };
  } catch {
    // If JSON parsing fails, return basic safe analysis
    return getFallbackAnalysis();
  }
}

/**
 * Emergency analysis with minimal prompt for maximum reliability
 */
async function analyzeWithEmergencyPrompt(
  client: { generateCompletion: (prompt: string, options: any) => Promise<{ content: string }> },
  mention: any,
  model: string
): Promise<Partial<MentionAnalysis>> {
  const emergencyPrompt = `Should we respond to: "${mention.mentionContent}"? Reply "yes" or "no" with brief reason.`;

  try {
    const response = await client.generateCompletion(emergencyPrompt, {
      model,
      maxTokens: 50,
      temperature: 0,
    });

    const shouldRespond = response.content.toLowerCase().includes('yes');
    return {
      shouldRespond,
      confidence: 0.6,
      priority: shouldRespond ? "medium" : "low",
      sentiment: "neutral",
      topics: "general",
      urgency: "low",
      responseStrategy: "engage",
      riskLevel: "low",
      reasoning: `Emergency analysis: ${response.content.slice(0, 100)}`,
    };
  } catch {
    return getFallbackAnalysis();
  }
}

/**
 * Get fallback analysis when all else fails
 */
function getFallbackAnalysis(): MentionAnalysis {
  return {
    shouldRespond: false,
    confidence: 0.5,
    priority: "low",
    sentiment: "neutral",
    topics: "general",
    urgency: "low",
    responseStrategy: "engage",
    riskLevel: "low",
    reasoning: "Fallback analysis due to processing failure",
  };
}

/**
 * Batch process multiple mentions with intelligent prioritization
 */
export const batchProcessMentions = action({
  args: {
    mentionIds: v.array(v.id("mentions")),
    maxConcurrent: v.optional(v.number()),
    priorityFirst: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const maxConcurrent = args.maxConcurrent || 3;
    const results = [];
    
    // Get all mentions with priority info
    const mentions = await Promise.all(
      args.mentionIds.map(id => 
        ctx.runQuery(api.mentions.mentionQueries.getMentionById, { mentionId: id })
      )
    );
    
    // Filter out null mentions and sort by priority if requested
    const validMentions = mentions.filter(Boolean);
    if (args.priorityFirst) {
      validMentions.sort((a: any, b: any) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aScore = (priorityOrder[a.priority as keyof typeof priorityOrder] || 0) +
                      (a.mentionAuthorVerified ? 1 : 0) +
                      (a.mentionAuthorFollowers > 10000 ? 1 : 0);
        const bScore = (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) +
                      (b.mentionAuthorVerified ? 1 : 0) +
                      (b.mentionAuthorFollowers > 10000 ? 1 : 0);
        return bScore - aScore;
      });
    }
    
    // Process in batches
    for (let i = 0; i < validMentions.length; i += maxConcurrent) {
      const batch = validMentions.slice(i, i + maxConcurrent);
      const batchPromises = batch.map((mention: any) => 
        ctx.runAction(api.ai.ensembleOrchestrator.analyzeMentionEnsemble, {
          mentionId: mention._id,
          priority: mention.priority,
          useCache: true,
        })
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map((result: any, idx: number) => ({
        mentionId: batch[idx]._id,
        success: result.status === "fulfilled",
        result: result.status === "fulfilled" ? result.value : null,
        error: result.status === "rejected" ? result.reason : null,
      })));
      
      // Small delay between batches to prevent overwhelming the system
      if (i + maxConcurrent < validMentions.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return {
      processed: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  },
});

/**
 * Get processing metrics and performance analytics
 */
export const getProcessingMetrics = query({
  args: {
    timeRange: v.optional(v.union(v.literal("24h"), v.literal("7d"), v.literal("30d"))),
  },
  handler: async (ctx, args) => {
    const timeRange = args.timeRange || "24h";
    const now = Date.now();
    const startTime = now - (
      timeRange === "24h" ? 24 * 60 * 60 * 1000 :
      timeRange === "7d" ? 7 * 24 * 60 * 60 * 1000 :
      30 * 24 * 60 * 60 * 1000
    );
    
    // Get recent mentions to analyze processing performance
    const mentions = await ctx.db
      .query("mentions")
      .withIndex("by_discovered_at")
      .filter(q => q.gte(q.field("discoveredAt"), startTime))
      .collect();
    
    const processed = mentions.filter(m => m.isProcessed);
    const avgProcessingTime = processed.length > 0 
      ? processed.reduce((sum, m) => sum + ((m.processedAt || m.discoveredAt) - m.discoveredAt), 0) / processed.length
      : 0;
    
    return {
      timeRange,
      totalMentions: mentions.length,
      processedMentions: processed.length,
      processingRate: mentions.length > 0 ? processed.length / mentions.length : 0,
      avgProcessingTimeMs: avgProcessingTime,
      priorityBreakdown: {
        high: mentions.filter(m => m.priority === "high").length,
        medium: mentions.filter(m => m.priority === "medium").length,
        low: mentions.filter(m => m.priority === "low").length,
      },
      responseOpportunities: processed.filter(m => m.aiAnalysisResult?.shouldRespond).length,
    };
  },
});
