import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { getOpenRouterClient } from "../lib/openrouter_client";

/**
 * Generate contextual responses for mentions
 * Reads the mention content and crafts appropriate, concise replies
 */
export const generateResponsesForMention = action({
  args: {
    mentionId: v.id("mentions"),
    mentionContent: v.string(),
    authorHandle: v.optional(v.string()),
    authorName: v.optional(v.string()),
    context: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      console.log("🤖 Generating contextual responses for mention");
      
      const client = getOpenRouterClient();
      
      // Get current user
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("User not authenticated");
      }
      
      const user = await ctx.runQuery(api.users.getCurrentUser);
      if (!user) {
        throw new Error("User not found");
      }

      // Generate 3 different response styles
      const styles = [
        { 
          name: "professional", 
          prompt: "Create a professional, informative response that adds value to the conversation" 
        },
        { 
          name: "casual", 
          prompt: "Create a friendly, conversational response that builds rapport" 
        },
        { 
          name: "supportive", 
          prompt: "Create a helpful, supportive response that offers assistance or encouragement" 
        }
      ];

      const responses = [];
      
      for (const style of styles) {
        const prompt = `You are responding to this mention on Twitter:

"${args.mentionContent}"

Author: ${args.authorName || "Unknown"} (@${args.authorHandle || "unknown"})

${style.prompt}

Requirements:
- Keep under 280 characters (Twitter limit)
- Be authentic and conversational
- Add value to the conversation
- Match the ${style.name} tone
- Respond directly to what they said
- Be helpful and engaging

Generate ONLY the reply text, no quotes or explanations:`;

        const response = await client.generateCompletion(prompt, {
          model: "google/gemini-2.5-flash-preview-05-20",
          maxTokens: 100,
          temperature: 0.7,
          systemPrompt: `You are a helpful social media assistant. Create concise, engaging Twitter replies under 280 characters.`
        });

        const cleanContent = response.content
          .replace(/^(Reply:|Response:|Tweet:|Answer:)\s*/i, '')
          .replace(/^[\"']|[\"']$/g, '')
          .trim();

        // Save the response to database
        const responseId = await ctx.runMutation(api.responseMutations.storeResponse, {
          targetType: "mention",
          targetId: args.mentionId,
          content: cleanContent,
          style: style.name,
          confidence: 0.85,
          generationModel: "google/gemini-2.5-flash-preview-05-20",
        });

        responses.push({
          id: responseId,
          content: cleanContent,
          style: style.name,
          characterCount: cleanContent.length,
        });
      }

      console.log(`✅ Generated ${responses.length} contextual responses`);
      
      return {
        success: true,
        totalGenerated: responses.length,
        responses: responses,
        generatedAt: Date.now(),
      };

    } catch (error) {
      console.error("❌ Response generation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        totalGenerated: 0,
      };
    }
  },
});