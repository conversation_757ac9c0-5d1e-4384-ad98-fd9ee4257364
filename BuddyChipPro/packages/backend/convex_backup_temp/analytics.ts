import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel"; // If using internal user IDs

// Define the structure of a single event for validation
const eventSchema = v.object({
  eventName: v.string(),
  timestamp: v.number(),
  userId: v.optional(v.id("users")),
  clerkUserId: v.optional(v.string()),
  sessionId: v.optional(v.string()),
  path: v.optional(v.string()),
  properties: v.any(),
});

export const ingestAnalyticsBatch = mutation({
  args: {
    events: v.array(eventSchema),
  },
  handler: async (ctx, args) => {
    // Optional: Get authenticated user if events should be associated with the caller
    // const identity = await ctx.auth.getUserIdentity();
    // let userIdToLog: Id<"users"> | null = null; // Correct type for internal user ID
    // if (identity) {
    //   // Example: Fetch your internal user ID based on Clerk ID (identity.subject)
    //   // This assumes you have a 'users' table with an index on 'clerkId'
    //   const user = await ctx.db.query("users").withIndex("by_clerk_id", q => q.eq("clerkId", identity.subject)).first();
    //   if (user) {
    //     userIdToLog = user._id;
    //   }
    // }

    for (const event of args.events) {
      // Example of associating event with the calling user if specific IDs aren't provided by client
      // Ensure the types match what `eventSchema` expects.
      // if (!event.userId && !event.clerkUserId && userIdToLog) {
      //   event.userId = userIdToLog; // Use internal Id<"users">
      // } else if (!event.clerkUserId && !event.userId && identity?.subject) {
      //   event.clerkUserId = identity.subject; // Use Clerk's subject ID
      // }

      await ctx.db.insert("analyticsEvents", event);
    }
    return { received: args.events.length };
  },
});
