/**
 * BuddyChip Pro - Automated Cron Jobs
 *
 * These cron jobs run automated workflows to:
 * - Monitor Twitter accounts for new tweets
 * - Check for mentions and replies
 * - Analyze content with AI
 * - Generate response suggestions
 * - Maintain data freshness
 */
declare const crons: import("convex/server").Crons;
export default crons;
/**
 * Cron Job Configuration Summary:
 *
 * HIGH FREQUENCY (15-60 minutes):
 * - Mention monitoring for time-sensitive opportunities
 * - Quick health checks
 * - Peak hours enhanced monitoring
 *
 * MEDIUM FREQUENCY (2-24 hours):
 * - Tweet scraping for fresh content
 * - Content analysis and AI processing
 * - Response generation
 * - Daily maintenance tasks
 *
 * LOW FREQUENCY (Weekly):
 * - Comprehensive analysis
 * - Data cleanup and optimization
 * - Embedding generation
 * - Strategic insights
 *
 * TOTAL ESTIMATED COST PER DAY:
 * - ~50-100 API calls to Twitter/scraping services
 * - ~200-500 AI model calls for analysis/generation
 * - ~$1-5 per day in API costs (depending on usage)
 *
 * SCALING NOTES:
 * - Increase frequency for high-engagement accounts
 * - Reduce frequency during low-activity periods
 * - Monitor API costs and adjust limits as needed
 * - Consider user-specific schedules for personalization
 */ 
