/**
 * Input Sanitization and Validation for AI Prompts and User Content
 * 🔐 SECURITY: Prevents prompt injection, XSS, and other input-based attacks
 */
export interface SanitizationResult {
    sanitized: string;
    isValid: boolean;
    warnings: string[];
    blocked: boolean;
    originalLength: number;
    sanitizedLength: number;
}
export interface ValidationOptions {
    maxLength?: number;
    allowHtml?: boolean;
    strictMode?: boolean;
    logViolations?: boolean;
}
/**
 * Main sanitization function for AI prompts and user content
 */
export declare function sanitizeInput(input: string, context?: string, options?: ValidationOptions): SanitizationResult;
/**
 * Specialized sanitization for AI prompts
 */
export declare function sanitizeAIPrompt(prompt: string): SanitizationResult;
/**
 * Sanitization for user-generated content
 */
export declare function sanitizeUserContent(content: string): SanitizationResult;
/**
 * Sanitization for tweet content
 */
export declare function sanitizeTweetContent(content: string): SanitizationResult;
/**
 * Validate and sanitize batch inputs
 */
export declare function sanitizeBatch(inputs: string[], context?: string, options?: ValidationOptions): SanitizationResult[];
/**
 * Check if content contains suspicious patterns
 */
export declare function containsSuspiciousPatterns(content: string): boolean;
/**
 * Extract and validate URLs from content
 */
export declare function sanitizeUrls(content: string): string;
