/**
 * Backend Debug Configuration System
 *
 * Convex-side debug controls with environment variable switches.
 * Ensures debug functionality and logging are controlled for production security.
 */
export declare const DEBUG_ENABLED: boolean;
export declare const DEBUG_CONFIG: {
    readonly auth: {
        readonly enabled: boolean;
        readonly logging: boolean;
        readonly validation: boolean;
        readonly functions: boolean;
    };
    readonly performance: {
        readonly enabled: boolean;
        readonly monitoring: boolean;
        readonly caching: boolean;
        readonly queries: boolean;
    };
    readonly database: {
        readonly enabled: boolean;
        readonly queries: boolean;
        readonly mutations: boolean;
        readonly indexes: boolean;
    };
    readonly external: {
        readonly enabled: boolean;
        readonly twitter: boolean;
        readonly webhooks: boolean;
        readonly integrations: boolean;
    };
    readonly workflows: {
        readonly enabled: boolean;
        readonly crons: boolean;
        readonly batching: boolean;
    };
};
export declare enum DebugLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3,
    TRACE = 4
}
export declare const debugLog: {
    error: (category: string, message: string, data?: any) => void;
    warn: (category: string, message: string, data?: any) => void;
    info: (category: string, message: string, data?: any) => void;
    debug: (category: string, message: string, data?: any) => void;
    trace: (category: string, message: string, data?: any) => void;
};
export declare const debugTimer: {
    timers: Map<any, any>;
    start: (label: string) => void;
    end: (label: string) => number;
    mark: (label: string, data?: any) => void;
};
export declare const withDebug: <T extends any[], R>(category: keyof typeof DEBUG_CONFIG, functionName: string, fn: (...args: T) => R | Promise<R>) => (...args: T) => Promise<R>;
export declare const debugMiddleware: (category: keyof typeof DEBUG_CONFIG) => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare const getBackendDebugInfo: () => {
    debug: boolean;
    mode: string;
    config?: undefined;
    environment?: undefined;
    timestamp?: undefined;
} | {
    debug: true;
    mode: string;
    config: {
        readonly auth: {
            readonly enabled: boolean;
            readonly logging: boolean;
            readonly validation: boolean;
            readonly functions: boolean;
        };
        readonly performance: {
            readonly enabled: boolean;
            readonly monitoring: boolean;
            readonly caching: boolean;
            readonly queries: boolean;
        };
        readonly database: {
            readonly enabled: boolean;
            readonly queries: boolean;
            readonly mutations: boolean;
            readonly indexes: boolean;
        };
        readonly external: {
            readonly enabled: boolean;
            readonly twitter: boolean;
            readonly webhooks: boolean;
            readonly integrations: boolean;
        };
        readonly workflows: {
            readonly enabled: boolean;
            readonly crons: boolean;
            readonly batching: boolean;
        };
    };
    environment: {
        DEBUG_MODE: string | undefined;
        DEBUG_AUTH: string | undefined;
        DEBUG_PERFORMANCE: string | undefined;
        DEBUG_LOG_LEVEL: string | undefined;
        NODE_ENV: string | undefined;
    };
    timestamp: string;
};
export declare const isDebugEnabled: (category: keyof typeof DEBUG_CONFIG) => boolean;
export declare const debugOnly: <T>(category: keyof typeof DEBUG_CONFIG, fn: () => T) => T | undefined;
