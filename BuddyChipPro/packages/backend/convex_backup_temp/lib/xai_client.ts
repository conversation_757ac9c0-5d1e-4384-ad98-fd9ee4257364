/**
 * xA<PERSON>rok Live Search Client for BuddyChip Pro
 * 
 * This client provides xAI's native Live Search capabilities including:
 * - Real-time X/Twitter search and monitoring
 * - Multi-source data integration (web, X, news, RSS)
 * - AI-powered relevance scoring and analysis
 * - Citation tracking and source verification
 */

export interface XAISearchParameters {
  mode: "auto" | "on" | "off";
  sources?: XAISearchSource[];
  from_date?: string; // ISO8601 format YYYY-MM-DD
  to_date?: string; // ISO8601 format YYYY-MM-DD
  max_search_results?: number; // default: 20
  return_citations?: boolean; // default: true
}

export interface XAISearchSource {
  type: "web" | "x" | "news" | "rss";
  // Web and News parameters
  country?: string; // ISO alpha-2 country code
  excluded_websites?: string[]; // max 5 websites
  allowed_websites?: string[]; // max 5 websites, cannot use with excluded_websites
  safe_search?: boolean; // default: true
  // X-specific parameters
  x_handles?: string[]; // specific X handles to search
  // RSS-specific parameters
  links?: string[]; // RSS feed URLs (currently max 1)
}

export interface XAIMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

export interface XAILiveSearchRequest {
  messages: XAIMessage[];
  model: string; // "grok-3-latest", "grok-beta", etc.
  search_parameters: XAISearchParameters;
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
}

export interface XAILiveSearchResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  citations?: string[]; // URLs of sources used
}

export class XAILiveSearchError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = "XAILiveSearchError";
  }
}

export class XAILiveSearchClient {
  private baseUrl = "https://api.x.ai/v1";
  private apiKey: string;

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error("xAI API key is required");
    }
    this.apiKey = apiKey;
  }

  /**
   * Perform live search using xAI chat completions with search parameters
   */
  async liveSearch(request: XAILiveSearchRequest): Promise<XAILiveSearchResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          errorMessage = errorText || errorMessage;
        }

        throw new XAILiveSearchError(errorMessage, response.status);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof XAILiveSearchError) {
        throw error;
      }
      throw new XAILiveSearchError(`Network error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Search for mentions of specific X handles with real-time data
   */
  async searchMentions(handles: string[], options: {
    dateRange?: { from?: string; to?: string };
    maxResults?: number;
    additionalContext?: string;
  } = {}): Promise<{
    content: string;
    citations: string[];
    relevantMentions: any[];
  }> {
    const query = `Find recent mentions, discussions, and interactions involving these X/Twitter handles: ${handles.map(h => `@${h.replace(/^@/, '')}`).join(', ')}. ${options.additionalContext || 'Include engagement metrics, sentiment, and priority level of each mention.'}`;

    const searchRequest: XAILiveSearchRequest = {
      messages: [
        {
          role: "system",
          content: "You are an expert social media analyst. When searching for mentions, provide detailed analysis including engagement metrics, sentiment, priority level, and actionable insights. Format your response with clear sections for each mention found."
        },
        {
          role: "user",
          content: query
        }
      ],
      model: "grok-3-latest",
      search_parameters: {
        mode: "on",
        sources: [
          {
            type: "x",
            x_handles: handles.map(h => h.replace(/^@/, ''))
          }
        ],
        from_date: options.dateRange?.from,
        to_date: options.dateRange?.to,
        max_search_results: options.maxResults || 20,
        return_citations: true
      },
      temperature: 0.3, // Lower temperature for more factual, consistent results
      max_tokens: 2000
    };

    const response = await this.liveSearch(searchRequest);
    
    return {
      content: response.choices[0]?.message.content || "",
      citations: response.citations || [],
      relevantMentions: this.parseMentionsFromResponse(response.choices[0]?.message.content || "")
    };
  }

  /**
   * Search for trending topics and conversations related to user's interests
   */
  async searchTrendingTopics(topics: string[], options: {
    dateRange?: { from?: string; to?: string };
    sources?: ("web" | "x" | "news")[];
    maxResults?: number;
  } = {}): Promise<{
    content: string;
    citations: string[];
    trends: any[];
  }> {
    const query = `Find current trending topics, viral content, and important conversations related to: ${topics.join(', ')}. Include engagement metrics, viral potential, and relevance scores.`;

    const searchRequest: XAILiveSearchRequest = {
      messages: [
        {
          role: "system",
          content: "You are a social media trend analyst. Identify trending topics with high engagement potential, viral characteristics, and relevance to the specified interests. Provide actionable insights for content strategy."
        },
        {
          role: "user",
          content: query
        }
      ],
      model: "grok-3-latest",
      search_parameters: {
        mode: "on",
        sources: (options.sources || ["x", "web", "news"]).map(type => ({ type })),
        from_date: options.dateRange?.from,
        to_date: options.dateRange?.to,
        max_search_results: options.maxResults || 15,
        return_citations: true
      },
      temperature: 0.4,
      max_tokens: 2500
    };

    const response = await this.liveSearch(searchRequest);
    
    return {
      content: response.choices[0]?.message.content || "",
      citations: response.citations || [],
      trends: this.parseTrendsFromResponse(response.choices[0]?.message.content || "")
    };
  }

  /**
   * Analyze content for response worthiness using live context
   */
  async analyzeContentWorthiness(content: string, userContext: {
    expertise: string[];
    interests: string[];
    brand?: string;
  }): Promise<{
    worthinessScore: number;
    reasoning: string;
    suggestedStrategy: string;
    liveContext: string;
    citations: string[];
  }> {
    const query = `Analyze this content for response worthiness: "${content}". Consider current trends, real-time engagement patterns, and strategic value for someone with expertise in: ${userContext.expertise.join(', ')} and interests in: ${userContext.interests.join(', ')}. ${userContext.brand ? `Brand context: ${userContext.brand}` : ''}`;

    const searchRequest: XAILiveSearchRequest = {
      messages: [
        {
          role: "system",
          content: "You are an expert social media strategist. Analyze content for response opportunities using real-time data including current trends, engagement patterns, and competitive landscape. Provide a worthiness score (0-100), clear reasoning, and strategic recommendations."
        },
        {
          role: "user",
          content: query
        }
      ],
      model: "grok-3-latest",
      search_parameters: {
        mode: "auto", // Let the model decide if live search is needed
        sources: [
          { type: "x" },
          { type: "web" },
          { type: "news" }
        ],
        max_search_results: 10,
        return_citations: true
      },
      temperature: 0.2, // Low temperature for consistent scoring
      max_tokens: 1500
    };

    const response = await this.liveSearch(searchRequest);
    const analysis = this.parseWorthinessAnalysis(response.choices[0]?.message.content || "");
    
    return {
      ...analysis,
      liveContext: response.choices[0]?.message.content || "",
      citations: response.citations || []
    };
  }

  /**
   * Generate contextually aware responses using live data
   */
  async generateContextualResponse(originalContent: string, context: {
    responseType: "reply" | "quote" | "comment";
    userProfile: {
      expertise: string[];
      interests: string[];
      brand?: string;
      tone?: string;
    };
    targetAudience?: string;
    strategy?: string;
  }): Promise<{
    suggestedResponse: string;
    alternativeResponses: string[];
    liveContext: string;
    citations: string[];
    confidence: number;
  }> {
    const query = `Generate an engaging ${context.responseType} to: "${originalContent}". Use current trends and real-time context. User profile: expertise in ${context.userProfile.expertise.join(', ')}, interests in ${context.userProfile.interests.join(', ')}. ${context.userProfile.brand ? `Brand: ${context.userProfile.brand}. ` : ''}${context.userProfile.tone ? `Tone: ${context.userProfile.tone}. ` : ''}${context.targetAudience ? `Target audience: ${context.targetAudience}. ` : ''}${context.strategy ? `Strategy: ${context.strategy}` : ''}`;

    const searchRequest: XAILiveSearchRequest = {
      messages: [
        {
          role: "system",
          content: "You are an expert social media content creator. Generate engaging, contextually relevant responses that leverage current trends and real-time insights. Provide multiple options with confidence scoring and strategic reasoning."
        },
        {
          role: "user",
          content: query
        }
      ],
      model: "grok-3-latest",
      search_parameters: {
        mode: "auto",
        sources: [
          { type: "x" },
          { type: "web" }
        ],
        max_search_results: 8,
        return_citations: true
      },
      temperature: 0.7, // Higher temperature for creative responses
      max_tokens: 2000
    };

    const response = await this.liveSearch(searchRequest);
    const parsed = this.parseResponseGeneration(response.choices[0]?.message.content || "");
    
    return {
      ...parsed,
      liveContext: response.choices[0]?.message.content || "",
      citations: response.citations || []
    };
  }

  /**
   * Monitor competitors and industry conversations
   */
  async monitorCompetitors(competitors: string[], industry: string, options: {
    dateRange?: { from?: string; to?: string };
    focus?: string;
  } = {}): Promise<{
    insights: string;
    competitorActivity: any[];
    opportunities: string[];
    citations: string[];
  }> {
    const query = `Monitor and analyze competitor activity for ${competitors.join(', ')} in the ${industry} industry. ${options.focus ? `Focus on: ${options.focus}. ` : ''}Identify opportunities, gaps, and strategic insights.`;

    const searchRequest: XAILiveSearchRequest = {
      messages: [
        {
          role: "system",
          content: "You are a competitive intelligence analyst. Monitor competitor activity, identify strategic opportunities, and provide actionable insights for competitive advantage in social media and business strategy."
        },
        {
          role: "user",
          content: query
        }
      ],
      model: "grok-3-latest",
      search_parameters: {
        mode: "on",
        sources: [
          { type: "x", x_handles: competitors },
          { type: "web" },
          { type: "news" }
        ],
        from_date: options.dateRange?.from,
        to_date: options.dateRange?.to,
        max_search_results: 25,
        return_citations: true
      },
      temperature: 0.3,
      max_tokens: 3000
    };

    const response = await this.liveSearch(searchRequest);
    const analysis = this.parseCompetitorAnalysis(response.choices[0]?.message.content || "");
    
    return {
      ...analysis,
      citations: response.citations || []
    };
  }

  // Helper methods to parse structured data from responses
  private parseMentionsFromResponse(content: string): any[] {
    // Extract structured mention data from the response
    // Implementation would parse the AI response to extract mentions
    return [];
  }

  private parseTrendsFromResponse(content: string): any[] {
    // Extract structured trend data from the response
    return [];
  }

  private parseWorthinessAnalysis(content: string): {
    worthinessScore: number;
    reasoning: string;
    suggestedStrategy: string;
  } {
    // Extract worthiness analysis from the response
    return {
      worthinessScore: 0,
      reasoning: "",
      suggestedStrategy: ""
    };
  }

  private parseResponseGeneration(content: string): {
    suggestedResponse: string;
    alternativeResponses: string[];
    confidence: number;
  } {
    // Extract response suggestions from the AI output
    return {
      suggestedResponse: "",
      alternativeResponses: [],
      confidence: 0
    };
  }

  private parseCompetitorAnalysis(content: string): {
    insights: string;
    competitorActivity: any[];
    opportunities: string[];
  } {
    // Extract competitor analysis from the response
    return {
      insights: "",
      competitorActivity: [],
      opportunities: []
    };
  }
}

/**
 * Create an xAI Live Search client instance
 */
export function createXAIClient(apiKey?: string): XAILiveSearchClient {
  const key = apiKey || process.env.XAI_API_KEY;
  if (!key) {
    throw new Error(
      "XAI_API_KEY environment variable is required. " +
      "Please add it to your .env.local file or set it in your Convex environment variables. " +
      "Get your API key from https://console.x.ai/"
    );
  }
  return new XAILiveSearchClient(key);
}

/**
 * Helper function to format date for xAI search parameters
 */
export function formatDateForXAI(date: Date): string {
  return date.toISOString().split('T')[0]; // YYYY-MM-DD format
}

/**
 * Helper function to get date range for recent searches
 */
export function getRecentDateRange(hoursBack: number = 24): { from: string; to: string } {
  const now = new Date();
  const from = new Date(now.getTime() - hoursBack * 60 * 60 * 1000);
  
  return {
    from: formatDateForXAI(from),
    to: formatDateForXAI(now)
  };
}