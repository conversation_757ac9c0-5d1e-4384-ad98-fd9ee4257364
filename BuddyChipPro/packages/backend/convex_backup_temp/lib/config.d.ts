/**
 * Configuration file for BuddyChip Pro backend
 *
 * This file centralizes all environment variable configuration
 * and provides type-safe access to configuration values.
 *
 * TwitterAPI.io Integration Configuration:
 * - Get your API key from https://twitterapi.io/
 * - Configure rate limiting and quota management
 * - Set up proper environment variable validation
 */
export interface TwitterAPIConfig {
    apiKey: string;
    baseUrl: string;
    rateLimitHandling: {
        enableRateLimiting: boolean;
        defaultDelay: number;
        retryAttempts: number;
        retryDelay: number;
        exponentialBackoff: boolean;
    };
    scraping: {
        defaultMaxResults: number;
        maxAccountsPerBatch: number;
        mentionLookbackHours: number;
        requestTimeout: number;
    };
    quotaManagement: {
        enableQuotaTracking: boolean;
        dailyRequestLimit: number;
        warningThreshold: number;
        emergencyStopThreshold: number;
    };
    monitoring: {
        enableUsageLogging: boolean;
        enableCostTracking: boolean;
        logRetentionDays: number;
    };
}
export interface AppConfig {
    twitterapi: TwitterAPIConfig;
    features: {
        enableAiAnalysis: boolean;
        enableMentionMonitoring: boolean;
        enableTweetScraping: boolean;
        enableResponseGeneration: boolean;
    };
    monitoring: {
        defaultScrapeInterval: number;
        mentionCheckInterval: number;
        maxMentionsPerAccount: number;
        priorityWeights: {
            verified: number;
            followerThresholds: {
                high: number;
                medium: number;
            };
        };
    };
    environment: {
        isDevelopment: boolean;
        isProduction: boolean;
        nodeEnv: string;
    };
}
/**
 * Load and validate configuration from environment variables
 */
export declare function loadConfig(): AppConfig;
/**
 * Validate that all required environment variables are present and properly formatted
 */
export declare function validateConfig(): {
    valid: boolean;
    errors: string[];
    warnings: string[];
};
/**
 * Get feature flags
 */
export declare function getFeatureFlags(): {
    enableAiAnalysis: boolean;
    enableMentionMonitoring: boolean;
    enableTweetScraping: boolean;
    enableResponseGeneration: boolean;
};
/**
 * Get TwitterAPI.io configuration
 */
export declare function getTwitterAPIConfig(): TwitterAPIConfig;
/**
 * Legacy function name for backwards compatibility
 * @deprecated Use getTwitterAPIConfig instead
 */
export declare function getTweetIOConfig(): TwitterAPIConfig;
/**
 * Legacy function name for backwards compatibility
 * @deprecated Use getTwitterAPIConfig instead
 */
export declare function getTwitterConfig(): TwitterAPIConfig;
/**
 * Get environment-specific configuration
 */
export declare function getEnvironmentConfig(): {
    isDevelopment: boolean;
    isProduction: boolean;
    nodeEnv: string;
};
/**
 * Check if we're in development mode
 */
export declare function isDevelopment(): boolean;
/**
 * Check if we're in production mode
 */
export declare function isProduction(): boolean;
/**
 * Get API usage and quota information
 */
export declare function getQuotaConfig(): {
    enableQuotaTracking: boolean;
    dailyRequestLimit: number;
    warningThreshold: number;
    emergencyStopThreshold: number;
};
/**
 * Get monitoring configuration
 */
export declare function getAPIMonitoringConfig(): {
    enableUsageLogging: boolean;
    enableCostTracking: boolean;
    logRetentionDays: number;
};
/**
 * Get monitoring configuration
 */
export declare function getMonitoringConfig(): {
    defaultScrapeInterval: number;
    mentionCheckInterval: number;
    maxMentionsPerAccount: number;
    priorityWeights: {
        verified: number;
        followerThresholds: {
            high: number;
            medium: number;
        };
    };
};
