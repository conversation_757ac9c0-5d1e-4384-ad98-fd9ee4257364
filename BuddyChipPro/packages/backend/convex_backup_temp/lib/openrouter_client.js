import OpenAI from 'openai';
/**
 * OpenRouter client for AI completions and embeddings
 */
export class OpenRouterClient {
    client;
    config;
    constructor(config) {
        // Parse fallback models from environment variable or use legacy defaults
        const envFallbackModels = process.env.OPENROUTER_FALLBACK_MODELS;
        const defaultFallbackModels = [
            'anthropic/claude-3.5-haiku',
            'google/gemini-pro-1.5',
            'meta-llama/llama-3.1-8b-instruct',
            'microsoft/phi-3-mini-128k-instruct',
            'google/gemini-2.0-flash-exp',
            'mistralai/mistral-7b-instruct'
        ];
        this.config = {
            baseURL: process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
            // Legacy support - will be overridden by model selector
            defaultModel: process.env.OPENROUTER_DEFAULT_MODEL || process.env.TEXT_MODEL_FAST || 'openai/gpt-4o-mini',
            fallbackModels: envFallbackModels ? envFallbackModels.split(',').map(s => s.trim()) : defaultFallbackModels,
            maxRetries: parseInt(process.env.OPENROUTER_MAX_RETRIES || process.env.MAX_RETRIES_FAST || '2'),
            timeout: parseInt(process.env.OPENROUTER_TIMEOUT || process.env.FAST_TIMEOUT || '15000'),
            ...config,
        };
        this.client = new OpenAI({
            apiKey: this.config.apiKey,
            baseURL: this.config.baseURL,
            timeout: this.config.timeout, // Add timeout support
            defaultHeaders: {
                'HTTP-Referer': 'https://buddychip.pro',
                'X-Title': 'BuddyChip Pro AI Agent',
            },
        });
    }
    /**
     * Generate AI completion with automatic fallback
     */
    async generateCompletion(prompt, options = {}) {
        const models = options.model
            ? [options.model, ...this.config.fallbackModels]
            : [this.config.defaultModel, ...this.config.fallbackModels];
        let lastError = null;
        for (const model of models) {
            try {
                const messages = [];
                if (options.systemPrompt) {
                    messages.push({
                        role: 'system',
                        content: options.systemPrompt,
                    });
                }
                messages.push({
                    role: 'user',
                    content: prompt,
                });
                const completion = await this.client.chat.completions.create({
                    model,
                    messages,
                    max_tokens: options.maxTokens || 1000,
                    temperature: options.temperature ?? 0.7,
                    stream: options.streaming || false,
                });
                const choice = completion.choices[0];
                if (!choice || !choice.message?.content) {
                    throw new Error('No valid response from AI model');
                }
                return {
                    content: choice.message.content,
                    model,
                    usage: completion.usage ? {
                        promptTokens: completion.usage.prompt_tokens,
                        completionTokens: completion.usage.completion_tokens,
                        totalTokens: completion.usage.total_tokens,
                    } : undefined,
                    finishReason: choice.finish_reason || undefined,
                };
            }
            catch (error) {
                lastError = error;
                const errorMessage = error instanceof Error ? error.message : String(error);
                // Enhanced error logging and classification
                const attempt = models.indexOf(model) + 1;
                const errorDetails = {
                    error: errorMessage,
                    status: error?.status,
                    code: error?.code,
                    type: error?.type,
                    attempt,
                    totalModels: models.length,
                    model
                };
                // Classify error type for better handling
                const isRateLimit = errorMessage.includes('rate limit') || errorMessage.includes('429');
                const isQuotaError = errorMessage.includes('quota') || errorMessage.includes('billing');
                const isAuthError = errorMessage.includes('401') || errorMessage.includes('unauthorized');
                const isModelError = errorMessage.includes('model') && errorMessage.includes('not found');
                if (isRateLimit) {
                    console.warn(`Rate limit hit for model ${model}. Switching to fallback.`, errorDetails);
                    // Longer delay for rate limits
                    await new Promise(resolve => setTimeout(resolve, Math.min(5000 * attempt, 30000)));
                }
                else if (isQuotaError) {
                    console.error(`Quota exceeded for model ${model}. Switching to fallback.`, errorDetails);
                }
                else if (isAuthError) {
                    console.error(`Authentication failed for model ${model}. Switching to fallback.`, errorDetails);
                }
                else if (isModelError) {
                    console.warn(`Model ${model} not available. Switching to fallback.`, errorDetails);
                }
                else {
                    console.warn(`Model ${model} failed with unknown error:`, errorDetails);
                }
                // Progressive delay between failed attempts
                if (attempt < models.length) {
                    const delay = isRateLimit ? Math.min(5000 * attempt, 30000) : 1000 * attempt;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                continue;
            }
        }
        // Enhanced error message with recovery suggestions
        const lastErrorMessage = lastError?.message || 'Unknown error';
        const errorType = lastErrorMessage.includes('rate limit') ? 'rate_limit' :
            lastErrorMessage.includes('quota') ? 'quota_exceeded' :
                lastErrorMessage.includes('401') ? 'auth_failed' :
                    lastErrorMessage.includes('timeout') ? 'timeout' : 'unknown';
        let userFriendlyMessage = '';
        switch (errorType) {
            case 'rate_limit':
                userFriendlyMessage = 'AI services are currently rate limited. Please try again in a few minutes.';
                break;
            case 'quota_exceeded':
                userFriendlyMessage = 'AI service quota exceeded. Please contact support or try again later.';
                break;
            case 'auth_failed':
                userFriendlyMessage = 'AI service authentication failed. Please check configuration.';
                break;
            case 'timeout':
                userFriendlyMessage = 'AI service timed out. Please try again with a shorter request.';
                break;
            default:
                userFriendlyMessage = 'AI services are temporarily unavailable. Please try again later.';
        }
        throw new Error(`${userFriendlyMessage} (Technical details: ${lastErrorMessage})`);
    }
    /**
     * Generate embeddings for vector search
     */
    async generateEmbedding(text, model = process.env.DEFAULT_EMBEDDING_MODEL || 'text-embedding-3-small') {
        try {
            const response = await this.client.embeddings.create({
                model,
                input: text,
                encoding_format: 'float',
            });
            const embedding = response.data[0];
            if (!embedding || !embedding.embedding) {
                throw new Error('No embedding returned from API');
            }
            return {
                embedding: embedding.embedding,
                model,
                usage: response.usage ? {
                    promptTokens: response.usage.prompt_tokens,
                    totalTokens: response.usage.total_tokens,
                } : undefined,
            };
        }
        catch (error) {
            console.error('Embedding generation failed:', error);
            throw new Error(`Embedding generation failed: ${error.message}`);
        }
    }
    /**
     * Batch generate embeddings for multiple texts
     */
    async generateEmbeddingsBatch(texts, model = process.env.DEFAULT_EMBEDDING_MODEL || 'text-embedding-3-small') {
        // Process in smaller batches to avoid rate limits
        const batchSize = 10;
        const results = [];
        for (let i = 0; i < texts.length; i += batchSize) {
            const batch = texts.slice(i, i + batchSize);
            const batchPromises = batch.map(text => this.generateEmbedding(text, model));
            try {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            catch (error) {
                console.error(`Batch embedding failed for batch starting at ${i}:`, error);
                // Continue with individual fallbacks
                for (const text of batch) {
                    try {
                        const result = await this.generateEmbedding(text, model);
                        results.push(result);
                    }
                    catch (individualError) {
                        console.error('Individual embedding failed:', individualError);
                        // Push a dummy embedding to maintain array alignment
                        results.push({
                            embedding: new Array(1536).fill(0),
                            model: 'fallback',
                        });
                    }
                }
            }
        }
        return results;
    }
    /**
     * Stream AI completion for real-time response generation
     */
    async *generateCompletionStream(prompt, options = {}) {
        const model = options.model || this.config.defaultModel;
        const messages = [];
        if (options.systemPrompt) {
            messages.push({
                role: 'system',
                content: options.systemPrompt,
            });
        }
        messages.push({
            role: 'user',
            content: prompt,
        });
        try {
            const stream = await this.client.chat.completions.create({
                model,
                messages,
                max_tokens: options.maxTokens || 1000,
                temperature: options.temperature ?? 0.7,
                stream: true,
            });
            for await (const chunk of stream) {
                const content = chunk.choices[0]?.delta?.content;
                if (content) {
                    yield content;
                }
            }
        }
        catch (error) {
            console.error('Streaming completion failed:', error);
            throw new Error(`Streaming failed: ${error.message}`);
        }
    }
    /**
     * Test the connection to OpenRouter
     */
    async testConnection() {
        try {
            const response = await this.generateCompletion('Say "OK" if you can hear me.', { maxTokens: 10, temperature: 0 });
            return response.content.toLowerCase().includes('ok');
        }
        catch (error) {
            console.error('Connection test failed:', error);
            return false;
        }
    }
    /**
     * Get available models from OpenRouter
     */
    async getAvailableModels() {
        try {
            const response = await fetch('https://openrouter.ai/api/v1/models', {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                },
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const data = await response.json();
            return data.data?.map((model) => model.id) || [];
        }
        catch (error) {
            console.error('Failed to fetch available models:', error);
            return this.config.fallbackModels;
        }
    }
}
/**
 * Initialize OpenRouter client with environment variables
 */
export function createOpenRouterClient() {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
        throw new Error('OPENROUTER_API_KEY environment variable is required. ' +
            'Get your API key from https://openrouter.ai/keys');
    }
    return new OpenRouterClient({
        apiKey,
    });
}
/**
 * Singleton instance for use across the application
 */
let openRouterClient = null;
export function getOpenRouterClient() {
    if (!openRouterClient) {
        openRouterClient = createOpenRouterClient();
    }
    return openRouterClient;
}
