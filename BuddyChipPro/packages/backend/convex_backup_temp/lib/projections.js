/**
 * 🚀 BANDWIDTH OPTIMIZATION PROJECTION FUNCTIONS
 *
 * These functions transform full database objects into lightweight versions
 * for different UI contexts, reducing bandwidth by 60-80%
 */
import { Transformers } from "../types/optimized";
// =============================================================================
// MENTION PROJECTIONS
// =============================================================================
/**
 * Transform full mention to lightweight version for list views
 * 🚀 BANDWIDTH REDUCTION: ~2-5KB → ~300-500 bytes (80-90% reduction)
 */
export function projectLightweightMention(mention) {
    return {
        _id: mention._id,
        content: Transformers.truncateContent(mention.content, 280),
        priority: mention.priority,
        authorHandle: mention.authorHandle,
        authorName: mention.authorName,
        authorProfileImage: mention.authorProfileImage,
        createdAt: mention.createdAt,
        discoveredAt: mention.discoveredAt,
        engagement: Transformers.safeEngagement(mention.engagement),
        mentionType: mention.mentionType,
        isProcessed: mention.isProcessed,
        hasResponse: !!mention.responseGenerated,
        responseCount: mention.responseCount || 0,
        // Heavy fields deliberately omitted:
        // ❌ fullContent, sentimentAnalysis, metadata, embeddingId, 
        // ❌ aiAnalysisResult, rawMentionData, etc.
    };
}
/**
 * Transform full mention to ultra-lightweight summary for stats
 * 🚀 BANDWIDTH REDUCTION: ~2-5KB → ~100-150 bytes (95-97% reduction)
 */
export function projectMentionSummary(mention) {
    return {
        _id: mention._id,
        priority: mention.priority,
        mentionType: mention.mentionType,
        discoveredAt: mention.discoveredAt,
        isProcessed: mention.isProcessed,
        isNotificationSent: mention.isNotificationSent,
        shouldRespond: Transformers.extractShouldRespond(mention.aiAnalysisResult),
    };
}
/**
 * Batch project mentions to lightweight format
 */
export function projectMentionList(mentions) {
    return mentions.map(projectLightweightMention);
}
/**
 * Batch project mentions to summary format
 */
export function projectMentionSummaries(mentions) {
    return mentions.map(projectMentionSummary);
}
// =============================================================================
// TWEET PROJECTIONS
// =============================================================================
/**
 * Transform full tweet to lightweight version for feeds
 * 🚀 BANDWIDTH REDUCTION: ~1-3KB → ~200-400 bytes (70-85% reduction)
 */
export function projectLightweightTweet(tweet) {
    return {
        _id: tweet._id,
        content: Transformers.truncateContent(tweet.content, 280),
        author: tweet.author,
        authorHandle: tweet.authorHandle,
        authorProfileImage: tweet.authorProfileImage,
        createdAt: tweet.createdAt,
        scrapedAt: tweet.scrapedAt,
        engagement: Transformers.safeEngagement(tweet.engagement),
        isRetweet: tweet.isRetweet || false,
        retweetedFrom: tweet.retweetedFrom,
        url: tweet.url,
        analysisStatus: tweet.analysisStatus,
        // Heavy fields deliberately omitted:
        // ❌ fullContent, analysisResult, metadata, embeddingId, rawTweetData
    };
}
/**
 * Transform full tweet to ultra-lightweight summary for stats
 * 🚀 BANDWIDTH REDUCTION: ~1-3KB → ~80-100 bytes (95-97% reduction)
 */
export function projectTweetSummary(tweet) {
    return {
        _id: tweet._id,
        scrapedAt: tweet.scrapedAt,
        analysisStatus: tweet.analysisStatus,
        engagement: {
            likes: tweet.engagement?.likes || 0,
            retweets: tweet.engagement?.retweets || 0,
            replies: tweet.engagement?.replies || 0,
        },
    };
}
/**
 * Batch project tweets to lightweight format
 */
export function projectTweetList(tweets) {
    return tweets.map(projectLightweightTweet);
}
/**
 * Batch project tweets to summary format
 */
export function projectTweetSummaries(tweets) {
    return tweets.map(projectTweetSummary);
}
// =============================================================================
// RESPONSE PROJECTIONS
// =============================================================================
/**
 * Transform full response to lightweight version for management views
 * 🚀 BANDWIDTH REDUCTION: ~1-2KB → ~250-350 bytes (70-80% reduction)
 */
export function projectLightweightResponse(response) {
    return {
        _id: response._id,
        mentionId: response.targetId, // Assuming targetType is "mention"
        targetType: response.targetType,
        content: Transformers.truncateContent(response.content, 280),
        status: response.status,
        aiModel: response.aiModel || "unknown",
        confidence: response.confidence,
        createdAt: response.createdAt,
        scheduledAt: response.scheduledAt,
        postedAt: response.postedAt,
        // Heavy fields deliberately omitted:
        // ❌ fullContent, originalPrompt, metadata, aiAnalysisContext, generationParameters
    };
}
/**
 * Transform full response to ultra-lightweight summary for stats
 * 🚀 BANDWIDTH REDUCTION: ~1-2KB → ~50-80 bytes (95-97% reduction)
 */
export function projectResponseSummary(response) {
    return {
        _id: response._id,
        status: response.status,
        createdAt: response.createdAt,
        confidence: response.confidence,
    };
}
/**
 * Batch project responses to lightweight format
 */
export function projectResponseList(responses) {
    return responses.map(projectLightweightResponse);
}
/**
 * Batch project responses to summary format
 */
export function projectResponseSummaries(responses) {
    return responses.map(projectResponseSummary);
}
// =============================================================================
// PERFORMANCE CALCULATION HELPERS
// =============================================================================
/**
 * Calculate bandwidth savings from projections
 */
export function calculateBandwidthSavings(originalCount, originalSizePerItem, projectedSizePerItem) {
    const originalTotal = originalCount * originalSizePerItem;
    const projectedTotal = originalCount * projectedSizePerItem;
    const savings = originalTotal - projectedTotal;
    const savingsPercent = Math.round((savings / originalTotal) * 100);
    return {
        originalTotal,
        projectedTotal,
        savings,
        savingsPercent,
    };
}
/**
 * Log bandwidth optimization metrics (for monitoring)
 */
export function logProjectionMetrics(operation, itemCount, originalSize, projectedSize) {
    const savings = calculateBandwidthSavings(itemCount, originalSize, projectedSize);
    console.log(`🚀 BANDWIDTH OPTIMIZATION [${operation}]:`, {
        items: itemCount,
        originalMB: (savings.originalTotal / 1024 / 1024).toFixed(2),
        projectedMB: (savings.projectedTotal / 1024 / 1024).toFixed(2),
        savingsMB: (savings.savings / 1024 / 1024).toFixed(2),
        savingsPercent: `${savings.savingsPercent}%`
    });
}
// =============================================================================
// SMART PROJECTION SELECTORS
// =============================================================================
/**
 * Smart projection selector based on UI context
 */
export const ProjectionSelector = {
    /**
     * Select appropriate mention projection based on context
     */
    selectMentionProjection: (context) => {
        switch (context) {
            case "list":
            case "card":
                return projectLightweightMention;
            case "stats":
                return projectMentionSummary;
            case "full":
                return (mention) => mention; // No projection
            default:
                return projectLightweightMention;
        }
    },
    /**
     * Select appropriate tweet projection based on context
     */
    selectTweetProjection: (context) => {
        switch (context) {
            case "feed":
            case "card":
                return projectLightweightTweet;
            case "stats":
                return projectTweetSummary;
            case "full":
                return (tweet) => tweet; // No projection
            default:
                return projectLightweightTweet;
        }
    },
    /**
     * Select appropriate response projection based on context
     */
    selectResponseProjection: (context) => {
        switch (context) {
            case "queue":
            case "management":
                return projectLightweightResponse;
            case "stats":
                return projectResponseSummary;
            case "full":
                return (response) => response; // No projection
            default:
                return projectLightweightResponse;
        }
    },
};
