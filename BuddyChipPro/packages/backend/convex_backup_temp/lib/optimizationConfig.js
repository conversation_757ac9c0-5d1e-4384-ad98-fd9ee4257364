import { mutation, action, query } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
/**
 * Get optimization configuration with intelligent defaults
 */
export const getOptimizationConfig = query({
    args: {
        userId: v.optional(v.id("users")),
        environment: v.optional(v.union(v.literal("production"), v.literal("development"), v.literal("staging"))),
    },
    handler: async (ctx, args) => {
        const environment = args.environment || "production";
        // Try to get user-specific config
        let userConfig = null;
        if (args.userId) {
            userConfig = await ctx.db
                .query("optimizationConfigs")
                .filter(q => q.eq(q.field("userId"), args.userId))
                .first();
        }
        // Get global config
        const globalConfig = await ctx.db
            .query("optimizationConfigs")
            .filter(q => q.eq(q.field("isGlobal"), true))
            .first();
        // Environment-specific defaults
        const environmentDefaults = getEnvironmentDefaults(environment);
        // Merge configs: user -> global -> environment defaults
        const config = {
            ...environmentDefaults,
            ...(globalConfig?.settings || {}),
            ...(userConfig?.settings || {}),
        };
        return {
            config,
            source: userConfig ? 'user' : globalConfig ? 'global' : 'default',
            environment,
            lastUpdated: userConfig?.updatedAt || globalConfig?.updatedAt || Date.now(),
        };
    },
});
/**
 * Update optimization configuration
 */
export const updateOptimizationConfig = mutation({
    args: {
        userId: v.optional(v.id("users")),
        settings: v.any(), // OptimizationSettings type
        isGlobal: v.optional(v.boolean()),
    },
    handler: async (ctx, args) => {
        const now = Date.now();
        if (args.isGlobal) {
            // Update global config
            const existing = await ctx.db
                .query("optimizationConfigs")
                .filter(q => q.eq(q.field("isGlobal"), true))
                .first();
            if (existing) {
                await ctx.db.patch(existing._id, {
                    settings: args.settings,
                    updatedAt: now,
                });
                return existing._id;
            }
            else {
                return await ctx.db.insert("optimizationConfigs", {
                    settings: args.settings,
                    isGlobal: true,
                    createdAt: now,
                    updatedAt: now,
                });
            }
        }
        else if (args.userId) {
            // Update user-specific config
            const existing = await ctx.db
                .query("optimizationConfigs")
                .filter(q => q.eq(q.field("userId"), args.userId))
                .first();
            if (existing) {
                await ctx.db.patch(existing._id, {
                    settings: args.settings,
                    updatedAt: now,
                });
                return existing._id;
            }
            else {
                return await ctx.db.insert("optimizationConfigs", {
                    userId: args.userId,
                    settings: args.settings,
                    isGlobal: false,
                    createdAt: now,
                    updatedAt: now,
                });
            }
        }
        else {
            throw new Error("Must specify either userId or isGlobal");
        }
    },
});
/**
 * Auto-tune optimization settings based on performance metrics
 */
export const autoTuneOptimizations = action({
    args: {
        userId: v.optional(v.id("users")),
        analysisWindow: v.optional(v.number()), // Hours of data to analyze
    },
    handler: async (ctx, args) => {
        const analysisWindow = args.analysisWindow || 24;
        const startTime = Date.now() - (analysisWindow * 60 * 60 * 1000);
        try {
            // Get current config
            const currentConfig = await ctx.runQuery(api.lib.optimizationConfig.getOptimizationConfig, {
                userId: args.userId,
            });
            // Analyze performance metrics
            const metrics = await analyzePerformanceMetrics(ctx, startTime);
            // Generate optimization recommendations
            const recommendations = generateTuningRecommendations(metrics, currentConfig.config);
            // Apply conservative auto-tuning
            const newSettings = applyAutoTuning(currentConfig.config, recommendations);
            // Update configuration if improvements are significant
            if (recommendations.confidence > 0.7 && recommendations.improvements.length > 0) {
                await ctx.runMutation(api.lib.optimizationConfig.updateOptimizationConfig, {
                    userId: args.userId,
                    settings: newSettings,
                });
                return {
                    success: true,
                    applied: true,
                    recommendations,
                    oldSettings: currentConfig.config,
                    newSettings,
                    confidence: recommendations.confidence,
                };
            }
            else {
                return {
                    success: true,
                    applied: false,
                    recommendations,
                    reason: recommendations.confidence <= 0.7 ?
                        "Insufficient confidence for auto-tuning" :
                        "No significant improvements found",
                };
            }
        }
        catch (error) {
            console.error("Auto-tuning failed:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error),
            };
        }
    },
});
/**
 * Get environment-specific defaults
 */
function getEnvironmentDefaults(environment) {
    const baseConfig = {
        caching: {
            enabled: true,
            defaultTtl: 300000, // 5 minutes
            maxEntries: 1000,
            cleanupInterval: 3600000, // 1 hour
        },
        processing: {
            maxConcurrency: 3,
            batchSize: 10,
            timeoutMs: 30000,
            retryAttempts: 3,
        },
        viralDetection: {
            enabled: true,
            threshold: 0.7,
            realTimeMode: false,
            modelConfigs: ["google/gemini-2.0-flash-exp:free"],
        },
        rateLimit: {
            requestsPerMinute: 60,
            burstLimit: 10,
            cooldownMs: 1000,
        },
        monitoring: {
            metricsEnabled: true,
            detailedLogging: false,
            performanceTracking: true,
            alertThresholds: {
                errorRate: 0.05, // 5%
                responseTime: 5000, // 5 seconds
                queueDepth: 100,
            },
        },
    };
    switch (environment) {
        case "development":
            return {
                ...baseConfig,
                processing: {
                    ...baseConfig.processing,
                    maxConcurrency: 2,
                    timeoutMs: 60000, // Longer timeout for debugging
                },
                monitoring: {
                    ...baseConfig.monitoring,
                    detailedLogging: true,
                },
            };
        case "staging":
            return {
                ...baseConfig,
                viralDetection: {
                    ...baseConfig.viralDetection,
                    realTimeMode: true, // Test real-time features
                },
            };
        case "production":
            return {
                ...baseConfig,
                processing: {
                    ...baseConfig.processing,
                    maxConcurrency: 5, // Higher concurrency for production
                },
                rateLimit: {
                    ...baseConfig.rateLimit,
                    requestsPerMinute: 120, // Higher rate limit
                },
            };
        default:
            return baseConfig;
    }
}
/**
 * Analyze performance metrics for auto-tuning
 */
async function analyzePerformanceMetrics(ctx, startTime) {
    // Get recent mentions and processing data
    const mentions = await ctx.db
        .query("mentions")
        .withIndex("by_discovered_at")
        .filter((q) => q.gte(q.field("discoveredAt"), startTime))
        .collect();
    // Calculate key metrics
    const totalMentions = mentions.length;
    const processedMentions = mentions.filter(m => m.isProcessed).length;
    const processingRate = totalMentions > 0 ? processedMentions / totalMentions : 0;
    const avgProcessingTime = processedMentions > 0 ?
        mentions
            .filter(m => m.isProcessed && m.processedAt)
            .reduce((sum, m) => sum + ((m.processedAt || m.discoveredAt) - m.discoveredAt), 0) / processedMentions
        : 0;
    const errorRate = mentions.filter(m => m.aiAnalysisResult?.error).length / Math.max(totalMentions, 1);
    const highPriorityMentions = mentions.filter(m => m.priority === "high").length;
    const viralMentions = mentions.filter(m => {
        const engagement = m.engagement.likes + m.engagement.retweets + m.engagement.replies;
        return engagement > 100;
    }).length;
    return {
        totalMentions,
        processingRate,
        avgProcessingTime,
        errorRate,
        highPriorityMentions,
        viralMentions,
        timeWindow: Date.now() - startTime,
    };
}
/**
 * Generate tuning recommendations based on metrics
 */
function generateTuningRecommendations(metrics, currentConfig) {
    const recommendations = {
        improvements: [],
        confidence: 0,
        reasoning: [],
    };
    // Processing rate recommendations
    if (metrics.processingRate < 0.8) {
        if (currentConfig.processing.maxConcurrency < 8) {
            recommendations.improvements.push({
                setting: "processing.maxConcurrency",
                currentValue: currentConfig.processing.maxConcurrency,
                recommendedValue: Math.min(currentConfig.processing.maxConcurrency + 2, 8),
                reason: "Low processing rate detected",
                impact: "high",
            });
            recommendations.reasoning.push("Increasing concurrency to improve processing rate");
        }
        if (currentConfig.processing.batchSize < 20) {
            recommendations.improvements.push({
                setting: "processing.batchSize",
                currentValue: currentConfig.processing.batchSize,
                recommendedValue: Math.min(currentConfig.processing.batchSize + 5, 20),
                reason: "Low processing rate detected",
                impact: "medium",
            });
        }
    }
    // Response time recommendations
    if (metrics.avgProcessingTime > 10000) {
        recommendations.improvements.push({
            setting: "caching.defaultTtl",
            currentValue: currentConfig.caching.defaultTtl,
            recommendedValue: Math.max(currentConfig.caching.defaultTtl * 1.5, 600000),
            reason: "High processing time detected",
            impact: "medium",
        });
        recommendations.reasoning.push("Increasing cache TTL to reduce processing overhead");
    }
    // Error rate recommendations
    if (metrics.errorRate > 0.1) {
        recommendations.improvements.push({
            setting: "processing.retryAttempts",
            currentValue: currentConfig.processing.retryAttempts,
            recommendedValue: Math.min(currentConfig.processing.retryAttempts + 1, 5),
            reason: "High error rate detected",
            impact: "high",
        });
        recommendations.improvements.push({
            setting: "processing.timeoutMs",
            currentValue: currentConfig.processing.timeoutMs,
            recommendedValue: currentConfig.processing.timeoutMs + 10000,
            reason: "High error rate may indicate timeouts",
            impact: "medium",
        });
    }
    // Viral detection recommendations
    if (metrics.viralMentions > 5 && !currentConfig.viralDetection.realTimeMode) {
        recommendations.improvements.push({
            setting: "viralDetection.realTimeMode",
            currentValue: false,
            recommendedValue: true,
            reason: "High viral activity detected",
            impact: "high",
        });
    }
    // Calculate overall confidence
    const highImpactChanges = recommendations.improvements.filter((i) => i.impact === "high").length;
    const totalChanges = recommendations.improvements.length;
    if (totalChanges === 0) {
        recommendations.confidence = 0;
    }
    else if (metrics.totalMentions < 10) {
        recommendations.confidence = 0.3; // Low confidence with little data
    }
    else if (highImpactChanges > 0) {
        recommendations.confidence = 0.8;
    }
    else {
        recommendations.confidence = 0.6;
    }
    return recommendations;
}
/**
 * Apply auto-tuning recommendations conservatively
 */
function applyAutoTuning(currentConfig, recommendations) {
    const newConfig = JSON.parse(JSON.stringify(currentConfig)); // Deep clone
    for (const improvement of recommendations.improvements) {
        const settingPath = improvement.setting.split('.');
        // Only apply high-impact changes automatically
        if (improvement.impact === "high") {
            let target = newConfig;
            for (let i = 0; i < settingPath.length - 1; i++) {
                target = target[settingPath[i]];
            }
            target[settingPath[settingPath.length - 1]] = improvement.recommendedValue;
        }
    }
    return newConfig;
}
/**
 * Reset optimization config to defaults
 */
export const resetOptimizationConfig = mutation({
    args: {
        userId: v.optional(v.id("users")),
        environment: v.optional(v.union(v.literal("production"), v.literal("development"), v.literal("staging"))),
    },
    handler: async (ctx, args) => {
        const environment = args.environment || "production";
        const defaults = getEnvironmentDefaults(environment);
        if (args.userId) {
            const existing = await ctx.db
                .query("optimizationConfigs")
                .filter(q => q.eq(q.field("userId"), args.userId))
                .first();
            if (existing) {
                await ctx.db.patch(existing._id, {
                    settings: defaults,
                    updatedAt: Date.now(),
                });
                return existing._id;
            }
            else {
                return await ctx.db.insert("optimizationConfigs", {
                    userId: args.userId,
                    settings: defaults,
                    isGlobal: false,
                    createdAt: Date.now(),
                    updatedAt: Date.now(),
                });
            }
        }
        else {
            throw new Error("userId is required for config reset");
        }
    },
});
/**
 * Get optimization performance report
 */
export const getOptimizationReport = query({
    args: {
        timeRange: v.optional(v.union(v.literal("1h"), v.literal("6h"), v.literal("24h"), v.literal("7d"))),
    },
    handler: async (ctx, args) => {
        const timeRange = args.timeRange || "24h";
        const hours = timeRange === "1h" ? 1 :
            timeRange === "6h" ? 6 :
                timeRange === "24h" ? 24 : 168; // 7 days
        const startTime = Date.now() - (hours * 60 * 60 * 1000);
        // Get performance data
        const mentions = await ctx.db
            .query("mentions")
            .withIndex("by_discovered_at")
            .filter(q => q.gte(q.field("discoveredAt"), startTime))
            .collect();
        const cacheStats = await ctx.runQuery(api.lib.mentionCache.getCacheStats, {});
        const report = {
            timeRange,
            period: {
                start: startTime,
                end: Date.now(),
                hours,
            },
            processing: {
                totalMentions: mentions.length,
                processedMentions: mentions.filter(m => m.isProcessed).length,
                processingRate: mentions.length > 0 ?
                    mentions.filter(m => m.isProcessed).length / mentions.length : 0,
                avgProcessingTime: calculateAvgProcessingTime(mentions),
                errorRate: calculateErrorRate(mentions),
            },
            caching: {
                totalEntries: cacheStats.totalEntries,
                hitRate: cacheStats.hitRate,
                memoryUsage: cacheStats.memoryUsage,
            },
            viralDetection: {
                viralMentions: mentions.filter(m => {
                    const engagement = m.engagement.likes + m.engagement.retweets + m.engagement.replies;
                    return engagement > 100;
                }).length,
                highPriorityMentions: mentions.filter(m => m.priority === "high").length,
            },
            recommendations: await generateSystemRecommendations(mentions, cacheStats),
        };
        return report;
    },
});
/**
 * Helper functions
 */
function calculateAvgProcessingTime(mentions) {
    const processed = mentions.filter(m => m.isProcessed && m.processedAt);
    if (processed.length === 0)
        return 0;
    return processed.reduce((sum, m) => sum + ((m.processedAt || m.discoveredAt) - m.discoveredAt), 0) / processed.length;
}
function calculateErrorRate(mentions) {
    const totalProcessed = mentions.filter(m => m.isProcessed).length;
    if (totalProcessed === 0)
        return 0;
    const errors = mentions.filter(m => m.aiAnalysisResult?.error).length;
    return errors / totalProcessed;
}
async function generateSystemRecommendations(mentions, cacheStats) {
    const recommendations = [];
    const processingRate = mentions.length > 0 ?
        mentions.filter(m => m.isProcessed).length / mentions.length : 0;
    if (processingRate < 0.8) {
        recommendations.push("Consider increasing processing concurrency to improve mention processing rate");
    }
    if (cacheStats.hitRate < 0.6) {
        recommendations.push("Cache hit rate is low - consider increasing cache TTL or warming cache more frequently");
    }
    const errorRate = calculateErrorRate(mentions);
    if (errorRate > 0.1) {
        recommendations.push("High error rate detected - check API connectivity and increase timeout values");
    }
    const viralMentions = mentions.filter(m => {
        const engagement = m.engagement.likes + m.engagement.retweets + m.engagement.replies;
        return engagement > 100;
    }).length;
    if (viralMentions > 5) {
        recommendations.push("High viral activity detected - enable real-time viral detection for faster response");
    }
    if (recommendations.length === 0) {
        recommendations.push("System is performing optimally - no recommendations at this time");
    }
    return recommendations;
}
