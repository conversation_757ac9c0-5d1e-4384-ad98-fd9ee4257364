/**
 * OpenAI Client Configuration for Direct OpenAI API Access
 * Used specifically for image generation with gpt-image-1 model
 */
export interface OpenAIConfig {
    apiKey: string;
    baseURL?: string;
    maxRetries?: number;
    timeout?: number;
}
export interface ImageGenerationResponse {
    url?: string;
    base64?: string;
    revisedPrompt?: string;
    model: string;
    usage?: {
        totalTokens: number;
    };
}
export interface ResponsesAPIImageResponse {
    content: Array<{
        type: 'text' | 'image';
        text?: string;
        image?: {
            url?: string;
            base64?: string;
            detail?: string;
        };
    }>;
    model: string;
    usage?: {
        totalTokens: number;
    };
}
/**
 * OpenAI client for image generation and Responses API
 */
export declare class OpenAIClient {
    private client;
    private config;
    constructor(config: OpenAIConfig);
    /**
     * Generate image using DALL-E 3 model
     */
    generateImage(prompt: string, options?: {
        model?: 'dall-e-2' | 'dall-e-3';
        size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
        quality?: 'standard' | 'hd';
        style?: 'vivid' | 'natural';
        responseFormat?: 'url' | 'b64_json';
        user?: string;
    }): Promise<ImageGenerationResponse>;
    /**
     * Generate response with image using OpenAI Responses API
     * Uses the image_generation tool to create both text and images
     */
    generateResponseWithImage(prompt: string, options?: {
        systemPrompt?: string;
        maxTokens?: number;
        temperature?: number;
        imagePrompt?: string;
        imageModel?: 'gpt-image-1';
        imageSize?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
        imageQuality?: 'standard' | 'hd';
        imageStyle?: 'vivid' | 'natural';
    }): Promise<ResponsesAPIImageResponse>;
    /**
     * Generate image from text description optimized for social media
     */
    generateSocialMediaImage(description: string, options?: {
        platform?: 'twitter' | 'instagram' | 'linkedin';
        style?: 'minimal' | 'vibrant' | 'professional' | 'artistic';
        includeText?: string;
        aspectRatio?: 'square' | 'landscape' | 'portrait';
    }): Promise<ImageGenerationResponse>;
    /**
     * Test the connection to OpenAI
     */
    testConnection(): Promise<boolean>;
}
/**
 * Initialize OpenAI client with environment variables
 */
export declare function createOpenAIClient(): OpenAIClient;
export declare function getOpenAIClient(): OpenAIClient;
