/**
 * Rate limiting logic for Twitter API integration
 */
export class TwitterRateLimiter {
    config;
    rateLimitState = new Map();
    constructor(config) {
        this.config = config;
    }
    /**
     * Check if we're rate limited for a specific endpoint
     */
    isRateLimited(endpoint) {
        const limitInfo = this.rateLimitState.get(endpoint);
        if (!limitInfo)
            return false;
        const now = Math.floor(Date.now() / 1000);
        if (now >= limitInfo.reset) {
            // Rate limit has reset, remove from state
            this.rateLimitState.delete(endpoint);
            return false;
        }
        return limitInfo.remaining <= 0;
    }
    /**
     * Update rate limit information from response headers
     */
    updateRateLimitInfo(endpoint, headers) {
        const limit = headers.get("x-rate-limit-limit");
        const remaining = headers.get("x-rate-limit-remaining");
        const reset = headers.get("x-rate-limit-reset");
        if (limit && remaining && reset) {
            this.rateLimitState.set(endpoint, {
                limit: parseInt(limit),
                remaining: parseInt(remaining),
                reset: parseInt(reset),
            });
        }
    }
    /**
     * Update rate limit info from 429 response
     */
    updateRateLimitFromResponse(endpoint, response) {
        const resetHeader = response.headers.get("x-rate-limit-reset");
        if (resetHeader) {
            const resetTime = parseInt(resetHeader);
            this.rateLimitState.set(endpoint, {
                limit: parseInt(response.headers.get("x-rate-limit-limit") || "0"),
                remaining: 0,
                reset: resetTime,
            });
        }
    }
    /**
     * Get rate limit info for an endpoint
     */
    getRateLimitInfo(endpoint) {
        return this.rateLimitState.get(endpoint) || null;
    }
    /**
     * Get all rate limit status for debugging
     */
    getAllRateLimitStatus() {
        const status = {};
        this.rateLimitState.forEach((info, endpoint) => {
            status[endpoint] = { ...info };
        });
        return status;
    }
    /**
     * Apply exponential backoff for retries
     */
    async applyBackoff(attempt, baseDelay) {
        if (!this.config.rateLimitHandling.exponentialBackoff) {
            await new Promise(resolve => setTimeout(resolve, baseDelay));
            return;
        }
        const backoffDelay = baseDelay * Math.pow(2, attempt - 1);
        const jitter = Math.random() * 1000; // Add jitter to avoid thundering herd
        const totalDelay = Math.min(backoffDelay + jitter, 60000); // Cap at 60 seconds
        await new Promise(resolve => setTimeout(resolve, totalDelay));
    }
    /**
     * Apply default rate limiting delay
     */
    async applyDefaultDelay() {
        if (this.config.rateLimitHandling.enableRateLimiting) {
            await new Promise(resolve => setTimeout(resolve, this.config.rateLimitHandling.defaultDelay));
        }
    }
    /**
     * Calculate time until rate limit resets
     */
    getTimeUntilReset(endpoint) {
        const limitInfo = this.rateLimitState.get(endpoint);
        if (!limitInfo)
            return 0;
        const now = Math.floor(Date.now() / 1000);
        return Math.max(0, limitInfo.reset - now);
    }
    /**
     * Clear rate limit state for an endpoint (useful for testing)
     */
    clearRateLimitState(endpoint) {
        if (endpoint) {
            this.rateLimitState.delete(endpoint);
        }
        else {
            this.rateLimitState.clear();
        }
    }
}
