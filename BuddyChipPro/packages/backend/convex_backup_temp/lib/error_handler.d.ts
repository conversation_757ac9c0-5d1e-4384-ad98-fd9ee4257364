/**
 * Production Error Handler
 * 🔐 SECURITY: Sanitizes error messages and prevents information disclosure
 */
/**
 * Standard error types with safe production messages
 */
export declare enum ErrorType {
    AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED",
    AUTH<PERSON><PERSON>ZATION_FAILED = "AUTHORIZATION_FAILED",
    VALIDATION_FAILED = "VALIDATION_FAILED",
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND",
    EXTERNAL_API_ERROR = "EXTERNAL_API_ERROR",
    DATABASE_ERROR = "DATABASE_ERROR",
    INTERNAL_ERROR = "INTERNAL_ERROR",
    BAD_REQUEST = "BAD_REQUEST",
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"
}
/**
 * Custom application error class
 */
export declare class ApplicationError extends Error {
    readonly type: ErrorType;
    readonly statusCode: number;
    readonly isOperational: boolean;
    readonly context?: Record<string, any>;
    constructor(type: ErrorType, message?: string, context?: Record<string, any>);
}
/**
 * Sanitizes error message for production
 */
export declare function sanitizeErrorMessage(error: any, fallbackType?: ErrorType): string;
/**
 * Enhanced error handler for Convex functions
 */
export declare function handleError(error: any, operation: string, context?: Record<string, any>): never;
/**
 * Wrapper for Convex functions with error handling and retry logic
 */
export declare function withErrorHandling<T extends any[], R>(operation: string, handler: (...args: T) => Promise<R>, options?: {
    retries?: number;
    retryDelay?: number;
    retryCondition?: (error: any) => boolean;
}): (...args: T) => Promise<R>;
/**
 * Creates specific error types for common scenarios
 */
export declare const createError: {
    authenticationRequired: (message?: string) => ApplicationError;
    authorizationFailed: (message?: string) => ApplicationError;
    validationFailed: (message?: string, context?: Record<string, any>) => ApplicationError;
    rateLimitExceeded: (message?: string) => ApplicationError;
    resourceNotFound: (resource: string) => ApplicationError;
    externalApiError: (service: string, originalError?: any) => ApplicationError;
    databaseError: (operation: string, originalError?: any) => ApplicationError;
    quotaExceeded: (quotaType: string) => ApplicationError;
};
/**
 * Error monitoring and metrics
 */
interface ErrorMetrics {
    errorCount: number;
    errorsByType: Record<string, number>;
    errorsByOperation: Record<string, number>;
    lastErrors: Array<{
        timestamp: number;
        type: string;
        operation: string;
        message: string;
    }>;
}
/**
 * Get error metrics for monitoring
 */
export declare function getErrorMetrics(): ErrorMetrics;
/**
 * Check if error rate is concerning
 */
export declare function isErrorRateHigh(timeWindowMs?: number): boolean;
/**
 * Get error recovery suggestions
 */
export declare function getErrorRecoverySuggestions(error: any): string[];
/**
 * Enhanced error handler with monitoring
 */
export declare function handleErrorWithMonitoring(error: any, operation: string, context?: Record<string, any>): never;
/**
 * Validates that an error is safe to return to client
 */
export declare function isSafeError(error: any): boolean;
export {};
