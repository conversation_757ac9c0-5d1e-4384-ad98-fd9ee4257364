/**
 * TwitterAPI.io Usage Monitoring and Quota Management
 *
 * This module provides comprehensive monitoring of TwitterAPI.io usage,
 * including rate limiting, quota tracking, cost monitoring, and emergency controls.
 */
export interface APIUsageEntry {
    timestamp: number;
    endpoint: string;
    requestCount: number;
    responseSize?: number;
    duration?: number;
    status: "success" | "error" | "rate_limited";
    errorMessage?: string;
    cost?: number;
}
export interface QuotaStatus {
    currentUsage: number;
    dailyLimit: number;
    percentageUsed: number;
    resetTime: number;
    isWarning: boolean;
    isEmergency: boolean;
    remainingRequests: number;
}
export interface RateLimitStatus {
    endpoint: string;
    limit: number;
    remaining: number;
    resetTime: number;
    isLimited: boolean;
}
/**
 * Track API usage for monitoring and quota management
 */
export declare const trackAPIUsage: any;
/**
 * Get current quota status
 */
export declare const getQuotaStatus: any;
/**
 * Check if we should block requests due to quota limits
 */
export declare const shouldBlockRequests: any;
/**
 * Get API usage analytics
 */
export declare const getUsageAnalytics: any;
/**
 * Get rate limit status for all endpoints
 */
export declare const getRateLimitStatus: any;
/**
 * Health check for TwitterAPI.io integration
 */
export declare const apiHealthCheck: any;
/**
 * Get cost analysis for API usage
 */
export declare const getCostAnalysis: any;
