import { httpRouter } from "convex/server";
import { httpAction } from "../_generated/server";
/**
 * Security headers configuration for production environments
 * Implements CORS policies, CSP, and other security headers
 */
// CORS configuration for different environments
export const CORS_CONFIG = {
    development: {
        origin: ['http://localhost:3000', 'http://localhost:5173', 'http://127.0.0.1:3000'],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Origin',
            'Cache-Control',
            'X-CSRF-Token'
        ],
    },
    production: {
        origin: [
            'https://buddychip.pro',
            'https://www.buddychip.pro',
            'https://app.buddychip.pro',
            // Add your production domains here
        ],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE'],
        allowedHeaders: [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Cache-Control',
            'X-CSRF-Token'
        ],
    },
};
// Content Security Policy configuration
export const CSP_CONFIG = {
    directives: {
        'default-src': ["'self'"],
        'script-src': [
            "'self'",
            "'unsafe-inline'", // Required for Vite in development
            "'unsafe-eval'", // Required for development tools
            'https://apis.google.com',
            'https://www.googletagmanager.com',
        ],
        'style-src': [
            "'self'",
            "'unsafe-inline'", // Required for CSS-in-JS
            'https://fonts.googleapis.com',
        ],
        'font-src': [
            "'self'",
            'https://fonts.gstatic.com',
        ],
        'img-src': [
            "'self'",
            'data:',
            'blob:',
            'https:',
            'https://pbs.twimg.com', // Twitter images
            'https://abs.twimg.com', // Twitter assets
        ],
        'connect-src': [
            "'self'",
            'https://api.convex.dev',
            'https://api.openai.com',
            'https://api.anthropic.com',
            'https://openrouter.ai',
            'https://api.twitterapi.io',
            'wss://api.convex.dev', // WebSocket connections
        ],
        'frame-ancestors': ["'none'"],
        'base-uri': ["'self'"],
        'form-action': ["'self'"],
        'upgrade-insecure-requests': [],
    },
};
/**
 * Generate CSP header value from configuration
 */
function generateCSPHeader() {
    const directives = Object.entries(CSP_CONFIG.directives)
        .map(([directive, sources]) => {
        if (Array.isArray(sources) && sources.length > 0) {
            return `${directive} ${sources.join(' ')}`;
        }
        else if (sources.length === 0) {
            return directive; // For directives like upgrade-insecure-requests
        }
        return null;
    })
        .filter(Boolean);
    return directives.join('; ');
}
/**
 * Security headers for all HTTP responses
 */
export const SECURITY_HEADERS = {
    // Prevent XSS attacks
    'X-Content-Type-Options': 'nosniff',
    // Prevent clickjacking
    'X-Frame-Options': 'DENY',
    // XSS protection (legacy, but still useful)
    'X-XSS-Protection': '1; mode=block',
    // Referrer policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    // Content Security Policy
    'Content-Security-Policy': generateCSPHeader(),
    // HSTS (only in production with HTTPS)
    ...(process.env.NODE_ENV === 'production' ? {
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
    } : {}),
    // Permissions policy
    'Permissions-Policy': [
        'camera=()',
        'microphone=()',
        'geolocation=()',
        'payment=()',
        'usb=()',
        'magnetometer=()',
        'accelerometer=()',
        'gyroscope=()',
    ].join(', '),
};
/**
 * Get CORS headers based on environment and origin
 */
export function getCORSHeaders(origin) {
    const isProduction = process.env.NODE_ENV === 'production';
    const config = isProduction ? CORS_CONFIG.production : CORS_CONFIG.development;
    const headers = {
        'Access-Control-Allow-Methods': config.methods.join(', '),
        'Access-Control-Allow-Headers': config.allowedHeaders.join(', '),
        'Access-Control-Max-Age': '86400', // 24 hours
    };
    // Handle credentials
    if (config.credentials) {
        headers['Access-Control-Allow-Credentials'] = 'true';
    }
    // Handle origin
    if (origin && config.origin.includes(origin)) {
        headers['Access-Control-Allow-Origin'] = origin;
    }
    else if (!isProduction) {
        // Allow any origin in development
        headers['Access-Control-Allow-Origin'] = '*';
    }
    return headers;
}
/**
 * Apply security headers to HTTP response
 */
export function applySecurityHeaders(response, origin) {
    // Apply CORS headers
    const corsHeaders = getCORSHeaders(origin);
    Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
    });
    // Apply security headers
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
        response.headers.set(key, value);
    });
    return response;
}
/**
 * Create HTTP action with security headers
 */
export function createSecureHttpAction(handler) {
    return httpAction(async (ctx, request) => {
        const origin = request.headers.get('Origin') || undefined;
        // Handle preflight requests
        if (request.method === 'OPTIONS') {
            const response = new Response(null, { status: 204 });
            return applySecurityHeaders(response, origin);
        }
        try {
            const response = await handler(ctx, request);
            return applySecurityHeaders(response, origin);
        }
        catch (error) {
            console.error('HTTP action error:', error);
            const errorResponse = new Response(JSON.stringify({ error: 'Internal server error' }), {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            });
            return applySecurityHeaders(errorResponse, origin);
        }
    });
}
/**
 * Validate origin against allowed origins
 */
export function isValidOrigin(origin) {
    const isProduction = process.env.NODE_ENV === 'production';
    const config = isProduction ? CORS_CONFIG.production : CORS_CONFIG.development;
    return config.origin.includes(origin);
}
/**
 * Create secure HTTP router with default security headers
 */
export function createSecureHttpRouter() {
    const router = httpRouter();
    // Note: Security headers are applied via createSecureHttpAction wrapper
    // Convex doesn't support global middleware, so we use individual action wrappers
    return router;
}
export { createSecureHttpAction as secureHttpAction };
