/**
 * Unified Image Generation Client
 * Intelligently routes image generation requests between OpenAI and Fal.ai
 */
import { type ModelStrategy, type ModelUsageContext } from './model_selector';
export type ImageProvider = 'openai' | 'fal' | 'auto';
export interface UnifiedImageRequest {
    prompt: string;
    provider?: ImageProvider;
    strategy?: ModelStrategy;
    context?: ModelUsageContext;
    size?: string;
    quality?: 'standard' | 'hd';
    style?: 'minimal' | 'vibrant' | 'professional' | 'artistic' | 'vivid' | 'natural';
    aspectRatio?: 'square' | 'landscape' | 'portrait';
    platform?: 'twitter' | 'instagram' | 'linkedin';
    openaiOptions?: {
        model?: 'dall-e-2' | 'dall-e-3';
        responseFormat?: 'url' | 'b64_json';
        user?: string;
    };
    falOptions?: {
        numInferenceSteps?: number;
        guidanceScale?: number;
        seed?: number;
        expandPrompt?: boolean;
        format?: 'jpeg' | 'png';
    };
}
export interface UnifiedImageResponse {
    url: string;
    provider: 'openai' | 'fal';
    model: string;
    width?: number;
    height?: number;
    contentType?: string;
    revisedPrompt?: string;
    seed?: number;
    inferenceTime?: number;
    estimatedCost: number;
    strategy: ModelStrategy;
    isFallback: boolean;
    generatedAt: number;
    usage?: {
        totalTokens?: number;
    };
}
/**
 * Unified image generation client with intelligent provider selection
 */
export declare class UnifiedImageClient {
    private static instance;
    private constructor();
    static getInstance(): UnifiedImageClient;
    /**
     * Generate image with intelligent provider selection
     */
    generateImage(request: UnifiedImageRequest): Promise<UnifiedImageResponse>;
    /**
     * Generate multiple image variations using optimal provider mix
     */
    generateImageVariations(request: UnifiedImageRequest, count?: number): Promise<UnifiedImageResponse[]>;
    /**
     * Generate image with specific provider
     */
    private generateWithProvider;
    /**
     * Generate image using OpenAI DALL-E
     */
    private generateWithOpenAI;
    /**
     * Generate image using Fal.ai Flux Pro
     */
    private generateWithFal;
    /**
     * Intelligent provider selection based on request characteristics
     */
    private selectProvider;
    /**
     * Get optimal provider mix for variations
     */
    private getOptimalProviderMix;
    /**
     * Map unified size to OpenAI format
     */
    private mapSizeToOpenAI;
    /**
     * Map unified size to Fal format
     */
    private mapSizeToFal;
    /**
     * Map unified style to OpenAI format
     */
    private mapStyleToOpenAI;
    /**
     * Get inference steps based on strategy
     */
    private getInferenceSteps;
    /**
     * Get OpenAI pricing
     */
    private getOpenAICost;
    /**
     * Test all providers
     */
    testAllProviders(): Promise<{
        openai: boolean;
        fal: boolean;
        overall: boolean;
    }>;
}
/**
 * Convenience functions for common use cases
 */
export declare function generateImage(request: UnifiedImageRequest): Promise<UnifiedImageResponse>;
export declare function generateSocialMediaImage(prompt: string, options?: {
    platform?: 'twitter' | 'instagram' | 'linkedin';
    style?: 'minimal' | 'vibrant' | 'professional' | 'artistic';
    provider?: ImageProvider;
}): Promise<UnifiedImageResponse>;
export declare function generateImageVariations(prompt: string, count?: number, options?: Partial<UnifiedImageRequest>): Promise<UnifiedImageResponse[]>;
export declare function testImageGeneration(): Promise<{
    openai: boolean;
    fal: boolean;
    overall: boolean;
}>;
