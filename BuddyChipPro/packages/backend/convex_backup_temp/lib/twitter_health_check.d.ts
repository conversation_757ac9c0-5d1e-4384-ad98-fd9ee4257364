/**
 * Twitter API Health Check and Testing Utilities
 *
 * This file provides utilities to test the TwitterAPI.io integration
 * and verify that everything is working correctly.
 */
export declare class TwitterApiError extends Error {
    statusCode?: number | undefined;
    rateLimitReset?: number | undefined;
    constructor(message: string, statusCode?: number | undefined, rateLimitReset?: number | undefined);
}
export declare const healthCheck: any;
export declare const testApiKey: any;
export declare const testTweetUrl: any;
export declare const testAccountScraping: any;
export declare const testMentionSearch: any;
export declare const runFullTest: any;
