/**
 * AI Fallback Client - Resilient AI response generation with multiple providers
 * Handles rate limits, model failures, and provides graceful degradation
 */
import { getOpenRouterClient } from './openrouter_client';
import { selectTextModel } from './model_selector';
/**
 * Comprehensive AI client with multiple fallback strategies
 */
export class AIFallbackClient {
    config;
    rateLimitCache = new Map();
    constructor(config = {}) {
        this.config = {
            enableFallbacks: true,
            maxRetryAttempts: 1, // Ultra-fast: only 1 retry
            baseDelayMs: 500, // Much faster delays
            exponentialBackoff: false, // No exponential backoff for speed
            enableLocalFallbacks: true,
            ...config,
        };
    }
    /**
     * Check if a model is currently rate limited
     */
    isRateLimited(model) {
        const limitTime = this.rateLimitCache.get(model);
        if (!limitTime)
            return false;
        if (Date.now() > limitTime) {
            this.rateLimitCache.delete(model);
            return false;
        }
        return true;
    }
    /**
     * Mark a model as rate limited for a specific duration
     */
    markRateLimited(model, durationMs = 60000) {
        this.rateLimitCache.set(model, Date.now() + durationMs);
    }
    /**
     * Apply exponential backoff delay
     */
    async applyDelay(attempt) {
        if (!this.config.exponentialBackoff) {
            await new Promise(resolve => setTimeout(resolve, this.config.baseDelayMs));
            return;
        }
        const delay = this.config.baseDelayMs * Math.pow(2, attempt - 1);
        const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
        await new Promise(resolve => setTimeout(resolve, delay + jitter));
    }
    /**
     * Generate AI response with comprehensive fallback handling
     */
    async generateResponse(options) {
        const startTime = Date.now();
        // Strategy 1: Try OpenRouter with ultra-fast timeout
        if (this.config.enableFallbacks) {
            try {
                const openRouterResponse = await this.tryOpenRouter(options);
                if (openRouterResponse) {
                    return {
                        ...openRouterResponse,
                        provider: 'OpenRouter',
                        isFallback: false,
                        generatedAt: startTime,
                    };
                }
            }
            catch (error) {
                console.warn('OpenRouter failed, trying fallbacks:', error);
            }
        }
        // Strategy 2: Local/offline fallbacks
        if (this.config.enableLocalFallbacks) {
            console.log('🔄 All AI providers failed, using local fallback generation');
            return this.generateLocalFallback(options, startTime);
        }
        throw new Error('All AI generation strategies failed');
    }
    /**
     * INTELLIGENT OpenRouter with smart model selection
     */
    async tryOpenRouter(options) {
        try {
            const client = getOpenRouterClient();
            // Use intelligent model selection based on context
            const context = {
                urgency: options.urgency || 'medium',
                quality: options.quality || 'standard',
                ...options.context,
            };
            const modelSelection = selectTextModel(options.strategy || 'fast', context);
            // Build model priority list: primary -> backup -> fallbacks
            const models = [
                modelSelection.primary,
                ...(modelSelection.backup ? [modelSelection.backup] : []),
                ...modelSelection.fallbacks,
            ].filter(Boolean);
            // Filter out rate-limited models
            const availableModels = models.filter(model => !this.isRateLimited(model));
            if (availableModels.length === 0) {
                console.warn('⚡ All models are currently rate limited, using fallback');
                return null;
            }
            // INTELLIGENT: Use model selection settings for timeout and retries
            const maxModelsToTry = Math.min(availableModels.length, modelSelection.maxRetries + 1);
            for (const model of availableModels.slice(0, maxModelsToTry)) {
                try {
                    console.log(`🧠 Trying intelligent AI with ${model} (strategy: ${modelSelection.strategy})...`);
                    // Create a timeout promise based on model selection
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error(`${modelSelection.strategy} timeout`)), modelSelection.timeout);
                    });
                    // Race the AI call against the timeout
                    const aiPromise = client.generateCompletion(options.prompt, {
                        model,
                        systemPrompt: options.systemPrompt,
                        maxTokens: Math.min(options.maxTokens || 150, 200), // Limit tokens for speed
                        temperature: options.temperature || 0.7,
                    });
                    const response = await Promise.race([aiPromise, timeoutPromise]);
                    console.log(`✅ Intelligent AI success with ${model} using ${modelSelection.strategy} strategy!`);
                    return {
                        content: response.content,
                        model: response.model,
                        provider: 'OpenRouter',
                        confidence: 0.9,
                        isFallback: false,
                        generatedAt: Date.now(),
                        usage: response.usage,
                        // Add model selection metadata
                        strategy: modelSelection.strategy,
                        estimatedCost: modelSelection.estimatedCost,
                        timeout: modelSelection.timeout,
                    };
                }
                catch (error) {
                    const errorMessage = error?.message || String(error);
                    console.log(`⚡ Fast-fail ${model}:`, errorMessage.substring(0, 100));
                    // Quick circuit breaker: mark as unavailable for 2 minutes
                    if (error?.status === 429 || errorMessage.includes('rate limit')) {
                        this.markRateLimited(model, 120000); // 2 minute cooldown
                    }
                    else if (error?.status === 404 || errorMessage.includes('No endpoints found')) {
                        this.markRateLimited(model, 180000); // 3 minute cooldown
                    }
                    else {
                        this.markRateLimited(model, 60000); // 1 minute cooldown for other errors
                    }
                    // Continue to next model immediately (no delays)
                    continue;
                }
            }
            console.log(`🧠 All intelligent AI attempts failed (strategy: ${modelSelection.strategy}), using local fallback`);
            return null;
        }
        catch (error) {
            console.error('🧠 OpenRouter intelligent client failed:', error);
            return null;
        }
    }
    /**
     * Generate super-smart local fallback with deep prompt analysis
     */
    generateLocalFallback(options, startTime) {
        const { prompt } = options;
        console.log('🧠 Generating smart local fallback response...');
        // Advanced prompt analysis
        const isQuestion = prompt.includes('?') || /\b(how|what|why|when|where|who)\b/i.test(prompt);
        const isCasual = /\b(lol|haha|😂|awesome|cool)\b/i.test(prompt);
        const isProfessional = /\b(business|professional|corporate|strategy|ROI)\b/i.test(prompt);
        const isTechnical = /\b(code|programming|API|tech|software|development)\b/i.test(prompt);
        const isPositive = /\b(great|amazing|excellent|love|awesome|fantastic)\b/i.test(prompt);
        const isNegative = /\b(bad|terrible|hate|awful|problem|issue|bug)\b/i.test(prompt);
        // Context-aware response generation
        let fallbackContent;
        if (isTechnical) {
            fallbackContent = this.generateTechnicalResponse(prompt);
        }
        else if (isQuestion) {
            fallbackContent = this.generateQuestionResponse(prompt);
        }
        else if (isCasual && isPositive) {
            fallbackContent = this.generateCasualPositiveResponse(prompt);
        }
        else if (isProfessional) {
            fallbackContent = this.generateProfessionalResponse(prompt);
        }
        else if (isNegative) {
            fallbackContent = this.generateSupportiveResponse(prompt);
        }
        else {
            fallbackContent = this.generateContextualGenericResponse(prompt);
        }
        console.log('✅ Smart fallback generated successfully');
        return {
            content: fallbackContent,
            model: 'smart-local-fallback-v2',
            provider: 'LocalSmart',
            confidence: 0.75, // Higher confidence for smart fallbacks
            isFallback: true,
            generatedAt: startTime,
        };
    }
    /**
     * Generate response to questions
     */
    generateQuestionResponse(prompt) {
        const responses = [
            "That's a great question! Let me share some thoughts on that.",
            "Interesting point! Here's my perspective on this.",
            "Thanks for asking! This is definitely worth discussing.",
            "Good question! I think there are a few ways to look at this.",
            "That's something I've been thinking about too. Here's what I've found:",
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
     * Generate casual/humorous response
     */
    generateCasualResponse(prompt) {
        const responses = [
            "Haha, totally feel you on this one! 😄",
            "This made me laugh! Thanks for sharing 😂",
            "I can relate to this so much! 💯",
            "This is exactly the kind of content I'm here for! 🙌",
            "Love this energy! Keep it coming! ✨",
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
     * Generate professional response
     */
    generateProfessionalResponse(prompt) {
        const responses = [
            "Thank you for sharing this valuable perspective.",
            "This raises some important points worth considering.",
            "I appreciate you bringing attention to this topic.",
            "This is a thoughtful observation that merits discussion.",
            "Your insights on this matter are quite valuable.",
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
     * Generate technical response
     */
    generateTechnicalResponse(prompt) {
        const responses = [
            "This is a really interesting technical perspective! I'd love to hear more about your implementation approach.",
            "Great technical insight! Have you considered the performance implications of this approach?",
            "This is exactly the kind of technical discussion I enjoy. Thanks for sharing your expertise!",
            "Really solid technical approach here. I appreciate you breaking this down clearly.",
            "This technical perspective is valuable. Have you run into any edge cases with this?",
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
     * Generate casual positive response
     */
    generateCasualPositiveResponse(prompt) {
        const responses = [
            "This is absolutely amazing! Love the energy here! 🔥",
            "Totally agree! This is exactly what I needed to see today! ✨",
            "This is so cool! Thanks for sharing the good vibes! 🙌",
            "Love this! You always know how to brighten my day! 😊",
            "This is fantastic! Keep spreading the positivity! 💯",
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
     * Generate supportive response
     */
    generateSupportiveResponse(prompt) {
        const responses = [
            "I hear you on this. It sounds frustrating, but I believe you'll find a way through it.",
            "That sounds challenging. Thanks for sharing your experience - it helps others who might be facing similar issues.",
            "I appreciate you being honest about this. It's not easy to talk about difficulties.",
            "This is a tough situation, but your willingness to address it head-on is admirable.",
            "Thanks for bringing this up. These kinds of challenges are important to discuss openly.",
        ];
        return responses[Math.floor(Math.random() * responses.length)];
    }
    /**
     * Generate contextual generic response
     */
    generateContextualGenericResponse(prompt) {
        // Extract key words for more contextual responses
        const keyWords = prompt.toLowerCase().match(/\b(innovation|future|growth|community|learning|success|impact)\b/g);
        if (keyWords && keyWords.includes('innovation')) {
            return "Love seeing innovative thinking like this! Innovation is what drives real progress.";
        }
        else if (keyWords && keyWords.includes('community')) {
            return "This is what I love about our community - thoughtful perspectives like this make us all better.";
        }
        else if (keyWords && keyWords.includes('learning')) {
            return "This is such a valuable learning opportunity. Thanks for sharing your insights!";
        }
        else {
            const responses = [
                "Thanks for sharing this! Really appreciate your perspective.",
                "This is exactly the kind of content that sparks great conversations.",
                "Love seeing posts like this - thanks for the insights!",
                "Really appreciate you taking the time to share this.",
                "This resonates with me. Thanks for posting!",
                "Great point! This definitely got me thinking.",
                "Thanks for bringing this up - it's so important to discuss.",
                "Really valuable perspective here. Appreciate the share!",
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }
    }
    /**
     * Get current rate limit status for debugging
     */
    getRateLimitStatus() {
        const status = {};
        for (const [model, resetTime] of this.rateLimitCache.entries()) {
            const remaining = Math.max(0, resetTime - Date.now());
            if (remaining > 0) {
                status[model] = remaining;
            }
        }
        return status;
    }
    /**
     * Clear rate limit cache (for testing/debugging)
     */
    clearRateLimits() {
        this.rateLimitCache.clear();
    }
}
/**
 * Singleton instance for use across the application
 */
let aiFallbackClient = null;
export function getAIFallbackClient(config) {
    if (!aiFallbackClient) {
        aiFallbackClient = new AIFallbackClient(config);
    }
    return aiFallbackClient;
}
/**
 * Quick generation function for simple use cases
 */
export async function generateAIResponse(prompt, options = {}) {
    const client = getAIFallbackClient();
    return await client.generateResponse({
        prompt,
        ...options,
    });
}
/**
 * Convenience functions for common scenarios
 */
export async function generateFastResponse(prompt, systemPrompt) {
    return generateAIResponse(prompt, {
        systemPrompt,
        strategy: 'fast',
        urgency: 'medium',
        quality: 'standard',
    });
}
export async function generateQualityResponse(prompt, systemPrompt) {
    return generateAIResponse(prompt, {
        systemPrompt,
        strategy: 'quality',
        urgency: 'low',
        quality: 'premium',
    });
}
export async function generateRealtimeResponse(prompt, systemPrompt) {
    return generateAIResponse(prompt, {
        systemPrompt,
        strategy: 'ultra_fast',
        urgency: 'critical',
        quality: 'draft',
        maxTokens: 100, // Keep it short for speed
    });
}
export async function generateBulkResponse(prompt, systemPrompt) {
    return generateAIResponse(prompt, {
        systemPrompt,
        strategy: 'bulk',
        urgency: 'low',
        quality: 'standard',
    });
}
