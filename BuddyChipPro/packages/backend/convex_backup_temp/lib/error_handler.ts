/**
 * Production Error Handler
 * 🔐 SECURITY: Sanitizes error messages and prevents information disclosure
 */

import { logger } from './secure_logger';
import { isProduction, isDevelopment } from './production_config';

/**
 * Standard error types with safe production messages
 */
export enum ErrorType {
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  AUTHORIZATION_FAILED = 'AUTHORIZATION_FAILED',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
}

/**
 * Error information for each error type
 */
const ERROR_INFO = {
  [ErrorType.AUTHENTICATION_REQUIRED]: {
    statusCode: 401,
    productionMessage: 'Authentication required. Please sign in.',
    logLevel: 'warn' as const,
  },
  [ErrorType.AUTHORIZATION_FAILED]: {
    statusCode: 403,
    productionMessage: 'Access denied. You do not have permission to perform this action.',
    logLevel: 'warn' as const,
  },
  [ErrorType.VALIDATION_FAILED]: {
    statusCode: 400,
    productionMessage: 'Invalid input. Please check your request and try again.',
    logLevel: 'warn' as const,
  },
  [ErrorType.RATE_LIMIT_EXCEEDED]: {
    statusCode: 429,
    productionMessage: 'Too many requests. Please try again later.',
    logLevel: 'warn' as const,
  },
  [ErrorType.RESOURCE_NOT_FOUND]: {
    statusCode: 404,
    productionMessage: 'The requested resource was not found.',
    logLevel: 'info' as const,
  },
  [ErrorType.EXTERNAL_API_ERROR]: {
    statusCode: 503,
    productionMessage: 'External service is temporarily unavailable. Please try again later.',
    logLevel: 'error' as const,
  },
  [ErrorType.DATABASE_ERROR]: {
    statusCode: 500,
    productionMessage: 'A database error occurred. Please try again later.',
    logLevel: 'error' as const,
  },
  [ErrorType.INTERNAL_ERROR]: {
    statusCode: 500,
    productionMessage: 'An internal error occurred. Please try again later.',
    logLevel: 'error' as const,
  },
  [ErrorType.BAD_REQUEST]: {
    statusCode: 400,
    productionMessage: 'Invalid request. Please check your input and try again.',
    logLevel: 'warn' as const,
  },
  [ErrorType.QUOTA_EXCEEDED]: {
    statusCode: 402,
    productionMessage: 'Usage quota exceeded. Please upgrade your plan or try again later.',
    logLevel: 'warn' as const,
  },
};

/**
 * Custom application error class
 */
export class ApplicationError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: Record<string, any>;

  constructor(
    type: ErrorType,
    message?: string,
    context?: Record<string, any>
  ) {
    const errorInfo = ERROR_INFO[type];
    super(message || errorInfo.productionMessage);
    
    this.type = type;
    this.statusCode = errorInfo.statusCode;
    this.isOperational = true;
    this.context = context;
    
    // Maintain proper stack trace
    Error.captureStackTrace(this, ApplicationError);
  }
}

/**
 * Sanitizes error message for production
 */
export function sanitizeErrorMessage(error: any, fallbackType: ErrorType = ErrorType.INTERNAL_ERROR): string {
  // If it's our custom error, use the type-specific message
  if (error instanceof ApplicationError) {
    const errorInfo = ERROR_INFO[error.type];
    return isProduction() ? errorInfo.productionMessage : error.message;
  }
  
  // Handle common error patterns
  if (error?.message) {
    const message = error.message.toLowerCase();
    
    // Authentication errors
    if (message.includes('authentication') || message.includes('unauthorized')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.AUTHENTICATION_REQUIRED].productionMessage
        : error.message;
    }
    
    // Authorization errors
    if (message.includes('permission') || message.includes('access denied') || message.includes('forbidden')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.AUTHORIZATION_FAILED].productionMessage
        : error.message;
    }
    
    // Validation errors
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.VALIDATION_FAILED].productionMessage
        : error.message;
    }
    
    // Rate limiting errors
    if (message.includes('rate limit') || message.includes('too many')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.RATE_LIMIT_EXCEEDED].productionMessage
        : error.message;
    }
    
    // Not found errors
    if (message.includes('not found') || message.includes('does not exist')) {
      return isProduction() 
        ? ERROR_INFO[ErrorType.RESOURCE_NOT_FOUND].productionMessage
        : error.message;
    }
  }
  
  // For any unhandled error, use the fallback
  const errorInfo = ERROR_INFO[fallbackType];
  return isProduction() ? errorInfo.productionMessage : (error?.message || 'Unknown error');
}

/**
 * Enhanced error handler for Convex functions
 */
export function handleError(
  error: any,
  operation: string,
  context?: Record<string, any>
): never {
  const errorId = generateErrorId();
  
  // Determine error type
  let errorType: ErrorType;
  if (error instanceof ApplicationError) {
    errorType = error.type;
  } else {
    errorType = classifyError(error);
  }
  
  const errorInfo = ERROR_INFO[errorType];
  const sanitizedMessage = sanitizeErrorMessage(error, errorType);
  
  // Log error with appropriate level
  const logContext = {
    errorId,
    operation,
    errorType,
    statusCode: errorInfo.statusCode,
    ...context,
    ...(isDevelopment() && { originalMessage: error?.message }),
  };
  
  switch (errorInfo.logLevel) {
    case 'error':
      logger.error('Operation failed', error, logContext);
      break;
    case 'warn':
      logger.warn('Operation warning', logContext);
      break;
    case 'info':
      logger.info('Operation info', logContext);
      break;
  }
  
  // Create sanitized error to throw
  const productionError = new ApplicationError(errorType, sanitizedMessage, {
    errorId,
    statusCode: errorInfo.statusCode,
  });
  
  throw productionError;
}

/**
 * Classifies unknown errors into appropriate types
 */
function classifyError(error: any): ErrorType {
  if (!error) return ErrorType.INTERNAL_ERROR;
  
  const message = (error.message || '').toLowerCase();
  
  // Network/API errors
  if (message.includes('fetch') || message.includes('network') || message.includes('timeout')) {
    return ErrorType.EXTERNAL_API_ERROR;
  }
  
  // Database errors
  if (message.includes('database') || message.includes('query') || message.includes('transaction')) {
    return ErrorType.DATABASE_ERROR;
  }
  
  // Validation errors
  if (message.includes('invalid') || message.includes('required') || message.includes('format')) {
    return ErrorType.VALIDATION_FAILED;
  }
  
  // Default to internal error
  return ErrorType.INTERNAL_ERROR;
}

/**
 * Generates a unique error ID for tracking
 */
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Wrapper for Convex functions with error handling and retry logic
 */
export function withErrorHandling<T extends any[], R>(
  operation: string,
  handler: (...args: T) => Promise<R>,
  options: {
    retries?: number;
    retryDelay?: number;
    retryCondition?: (error: any) => boolean;
  } = {}
) {
  return async (...args: T): Promise<R> => {
    const { retries = 0, retryDelay = 1000, retryCondition } = options;
    let lastError: any;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await handler(...args);
      } catch (error) {
        lastError = error;
        
        // Check if we should retry
        if (attempt < retries && (retryCondition ? retryCondition(error) : shouldRetryOperation(error))) {
          console.log(`Retrying ${operation} (attempt ${attempt + 1}/${retries + 1}) after error:`, error);
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
          continue;
        }
        
        handleErrorWithMonitoring(error, operation, { attempt: attempt + 1, totalAttempts: retries + 1 });
      }
    }
    
    // This should never be reached, but TypeScript requires it
    throw lastError;
  };
}

/**
 * Determines if an operation should be retried based on error type
 */
function shouldRetryOperation(error: any): boolean {
  if (error instanceof ApplicationError) {
    // Retry on external API errors and certain temporary failures
    return error.type === ErrorType.EXTERNAL_API_ERROR || 
           error.type === ErrorType.RATE_LIMIT_EXCEEDED ||
           (error.type === ErrorType.INTERNAL_ERROR && !error.message.includes('validation'));
  }
  
  const message = error?.message?.toLowerCase() || '';
  
  // Retry network-related errors
  if (message.includes('timeout') || 
      message.includes('network') || 
      message.includes('fetch') ||
      message.includes('rate limit') ||
      message.includes('503') ||
      message.includes('502') ||
      message.includes('504')) {
    return true;
  }
  
  // Don't retry authentication, authorization, or validation errors
  if (message.includes('auth') || 
      message.includes('permission') || 
      message.includes('validation') ||
      message.includes('404') ||
      message.includes('400')) {
    return false;
  }
  
  return false;
}

/**
 * Creates specific error types for common scenarios
 */
export const createError = {
  authenticationRequired: (message?: string) => 
    new ApplicationError(ErrorType.AUTHENTICATION_REQUIRED, message),
    
  authorizationFailed: (message?: string) => 
    new ApplicationError(ErrorType.AUTHORIZATION_FAILED, message),
    
  validationFailed: (message?: string, context?: Record<string, any>) => 
    new ApplicationError(ErrorType.VALIDATION_FAILED, message, context),
    
  rateLimitExceeded: (message?: string) => 
    new ApplicationError(ErrorType.RATE_LIMIT_EXCEEDED, message),
    
  resourceNotFound: (resource: string) => 
    new ApplicationError(ErrorType.RESOURCE_NOT_FOUND, `${resource} not found`),
    
  externalApiError: (service: string, originalError?: any) => 
    new ApplicationError(ErrorType.EXTERNAL_API_ERROR, `${service} API error`, { originalError }),
    
  databaseError: (operation: string, originalError?: any) => 
    new ApplicationError(ErrorType.DATABASE_ERROR, `Database ${operation} failed`, { originalError }),
    
  quotaExceeded: (quotaType: string) => 
    new ApplicationError(ErrorType.QUOTA_EXCEEDED, `${quotaType} quota exceeded`),
};

/**
 * Error monitoring and metrics
 */
interface ErrorMetrics {
  errorCount: number;
  errorsByType: Record<string, number>;
  errorsByOperation: Record<string, number>;
  lastErrors: Array<{
    timestamp: number;
    type: string;
    operation: string;
    message: string;
  }>;
}

const errorMetrics: ErrorMetrics = {
  errorCount: 0,
  errorsByType: {},
  errorsByOperation: {},
  lastErrors: []
};

/**
 * Track error for monitoring and analytics
 */
function trackError(
  errorType: ErrorType,
  operation: string,
  message: string
): void {
  errorMetrics.errorCount++;
  errorMetrics.errorsByType[errorType] = (errorMetrics.errorsByType[errorType] || 0) + 1;
  errorMetrics.errorsByOperation[operation] = (errorMetrics.errorsByOperation[operation] || 0) + 1;
  
  // Keep last 100 errors
  errorMetrics.lastErrors.unshift({
    timestamp: Date.now(),
    type: errorType,
    operation,
    message
  });
  errorMetrics.lastErrors = errorMetrics.lastErrors.slice(0, 100);
}

/**
 * Get error metrics for monitoring
 */
export function getErrorMetrics(): ErrorMetrics {
  return { ...errorMetrics };
}

/**
 * Check if error rate is concerning
 */
export function isErrorRateHigh(timeWindowMs: number = 300000): boolean { // 5 minutes
  const now = Date.now();
  const recentErrors = errorMetrics.lastErrors.filter(
    error => (now - error.timestamp) < timeWindowMs
  );
  
  // Alert if more than 10 errors in 5 minutes
  return recentErrors.length > 10;
}

/**
 * Get error recovery suggestions
 */
export function getErrorRecoverySuggestions(error: any): string[] {
  const suggestions: string[] = [];
  
  if (error instanceof ApplicationError) {
    switch (error.type) {
      case ErrorType.RATE_LIMIT_EXCEEDED:
        suggestions.push("Wait a few minutes before trying again");
        suggestions.push("Consider reducing request frequency");
        break;
      case ErrorType.EXTERNAL_API_ERROR:
        suggestions.push("Check external service status");
        suggestions.push("Verify API credentials and configuration");
        suggestions.push("Try again in a few minutes");
        break;
      case ErrorType.AUTHENTICATION_REQUIRED:
        suggestions.push("Sign in to your account");
        suggestions.push("Check if your session has expired");
        break;
      case ErrorType.QUOTA_EXCEEDED:
        suggestions.push("Consider upgrading your plan");
        suggestions.push("Wait until your quota resets");
        break;
      default:
        suggestions.push("Try refreshing the page");
        suggestions.push("Contact support if the problem persists");
    }
  } else {
    suggestions.push("Try refreshing the page");
    suggestions.push("Check your internet connection");
    suggestions.push("Contact support if the problem persists");
  }
  
  return suggestions;
}

/**
 * Enhanced error handler with monitoring
 */
export function handleErrorWithMonitoring(
  error: any,
  operation: string,
  context?: Record<string, any>
): never {
  const errorId = generateErrorId();
  
  // Determine error type
  let errorType: ErrorType;
  if (error instanceof ApplicationError) {
    errorType = error.type;
  } else {
    errorType = classifyError(error);
  }
  
  const errorInfo = ERROR_INFO[errorType];
  const sanitizedMessage = sanitizeErrorMessage(error, errorType);
  
  // Track error for monitoring
  trackError(errorType, operation, sanitizedMessage);
  
  // Log error with appropriate level
  const logContext = {
    errorId,
    operation,
    errorType,
    statusCode: errorInfo.statusCode,
    ...context,
    ...(isDevelopment() && { originalMessage: error?.message }),
  };
  
  switch (errorInfo.logLevel) {
    case 'error':
      logger.error('Operation failed', error, logContext);
      break;
    case 'warn':
      logger.warn('Operation warning', logContext);
      break;
    case 'info':
      logger.info('Operation info', logContext);
      break;
  }
  
  // Create sanitized error to throw
  const productionError = new ApplicationError(errorType, sanitizedMessage, {
    errorId,
    statusCode: errorInfo.statusCode,
    suggestions: getErrorRecoverySuggestions(error),
  });
  
  throw productionError;
}

/**
 * Validates that an error is safe to return to client
 */
export function isSafeError(error: any): boolean {
  return error instanceof ApplicationError && error.isOperational;
}