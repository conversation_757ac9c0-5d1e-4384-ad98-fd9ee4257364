/**
 * Analysis utilities for tweet processing and validation
 */
export interface AnalysisStructure {
    worthinessScore: number;
    shouldRespond: boolean;
    responseStrategy?: string;
    reasoning?: string;
    topics?: string[];
    sentiment?: 'positive' | 'negative' | 'neutral';
    urgency?: 'low' | 'medium' | 'high';
    engagement?: {
        likes: number;
        retweets: number;
        replies: number;
    };
    enhancedScore?: number;
    semanticRelevance?: number;
}
/**
 * Validate and sanitize analysis structure from AI response
 */
export declare function validateAnalysisStructure(analysis: any): AnalysisStructure;
/**
 * Create fallback analysis when AI response parsing fails
 */
export declare function createFallbackAnalysis(tweet: any, enhancedScore?: number, semanticScore?: number): AnalysisStructure;
/**
 * Extract key insights from content for analysis
 */
export declare function extractContentInsights(content: string): {
    topics: string[];
    sentiment: 'positive' | 'negative' | 'neutral';
    hasQuestion: boolean;
    hasCall2Action: boolean;
    urgencyIndicators: string[];
};
/**
 * Score content quality based on various factors
 */
export declare function scoreContentQuality(content: string, engagement?: any): number;
