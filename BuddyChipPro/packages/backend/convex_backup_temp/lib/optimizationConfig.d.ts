/**
 * Optimization Configuration Management
 * Centralized system for managing performance optimization settings
 */
export interface OptimizationSettings {
    caching: {
        enabled: boolean;
        defaultTtl: number;
        maxEntries: number;
        cleanupInterval: number;
    };
    processing: {
        maxConcurrency: number;
        batchSize: number;
        timeoutMs: number;
        retryAttempts: number;
    };
    viralDetection: {
        enabled: boolean;
        threshold: number;
        realTimeMode: boolean;
        modelConfigs: string[];
    };
    rateLimit: {
        requestsPerMinute: number;
        burstLimit: number;
        cooldownMs: number;
    };
    monitoring: {
        metricsEnabled: boolean;
        detailedLogging: boolean;
        performanceTracking: boolean;
        alertThresholds: {
            errorRate: number;
            responseTime: number;
            queueDepth: number;
        };
    };
}
/**
 * Get optimization configuration with intelligent defaults
 */
export declare const getOptimizationConfig: any;
/**
 * Update optimization configuration
 */
export declare const updateOptimizationConfig: any;
/**
 * Auto-tune optimization settings based on performance metrics
 */
export declare const autoTuneOptimizations: any;
/**
 * Reset optimization config to defaults
 */
export declare const resetOptimizationConfig: any;
/**
 * Get optimization performance report
 */
export declare const getOptimizationReport: any;
