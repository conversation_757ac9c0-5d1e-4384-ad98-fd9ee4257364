/**
 * Fal.ai Client for Advanced Image Generation
 * Provides access to Flux Pro and other advanced models
 */
export interface FalConfig {
    apiKey: string;
    baseURL?: string;
    timeout?: number;
    maxRetries?: number;
}
export interface FalImageGenerationRequest {
    prompt: string;
    image_size?: 'square_hd' | 'square' | 'portrait_4_3' | 'portrait_16_9' | 'landscape_4_3' | 'landscape_16_9';
    num_inference_steps?: number;
    guidance_scale?: number;
    num_images?: number;
    enable_safety_checker?: boolean;
    seed?: number;
    expand_prompt?: boolean;
    format?: 'jpeg' | 'png';
    aspect_ratio?: string;
    raw?: boolean;
}
export interface FalImageGenerationResponse {
    images: Array<{
        url: string;
        width: number;
        height: number;
        content_type: string;
    }>;
    timings: {
        inference: number;
    };
    seed: number;
    has_nsfw_concepts: boolean[];
    prompt: string;
}
export interface FalImageResponse {
    url: string;
    width: number;
    height: number;
    contentType: string;
    seed: number;
    hasNsfwConcepts: boolean;
    inferenceTime: number;
    model: string;
    revisedPrompt?: string;
}
/**
 * Fal.ai client for advanced image generation
 */
export declare class FalClient {
    private config;
    constructor(config: FalConfig);
    /**
     * Generate image using Flux Pro model
     */
    generateImage(prompt: string, options?: Partial<FalImageGenerationRequest>): Promise<FalImageResponse>;
    /**
     * Generate multiple image variations
     */
    generateImageVariations(prompt: string, count?: number, options?: Partial<FalImageGenerationRequest>): Promise<FalImageResponse[]>;
    /**
     * Generate social media optimized image
     */
    generateSocialMediaImage(prompt: string, options?: {
        platform?: 'twitter' | 'instagram' | 'linkedin';
        style?: 'minimal' | 'vibrant' | 'professional' | 'artistic';
        aspectRatio?: 'square' | 'landscape' | 'portrait';
    }): Promise<FalImageResponse>;
    /**
     * Make HTTP request to Fal.ai API
     */
    private makeRequest;
    /**
     * Test connection to Fal.ai
     */
    testConnection(): Promise<boolean>;
    /**
     * Get model capabilities and pricing info
     */
    getModelInfo(): {
        model: string;
        provider: string;
        capabilities: {
            maxResolution: string;
            supportedFormats: string[];
            supportedAspectRatios: string[];
            maxInferenceSteps: number;
            guidanceScaleRange: number[];
            expandPrompt: boolean;
            safetyChecker: boolean;
        };
        pricing: {
            estimatedCostPerImage: number;
            currency: string;
        };
    };
}
/**
 * Initialize Fal.ai client with environment variables
 */
export declare function createFalClient(): FalClient;
export declare function getFalClient(): FalClient;
