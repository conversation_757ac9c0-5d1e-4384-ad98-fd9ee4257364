/**
 * Production-Grade Rate Limiting System
 * 🔐 SECURITY: Prevents API abuse, DDoS attacks, and quota exhaustion
 */
import { logger } from './secure_logger';
import { PRODUCTION_SECURITY_CONFIG } from './production_config';
/**
 * In-memory rate limit store (in production, consider Redis or database)
 */
const rateLimitStore = {};
/**
 * Clean up expired rate limit entries
 */
function cleanupExpiredEntries() {
    const now = Date.now();
    for (const [key, data] of Object.entries(rateLimitStore)) {
        if (data.resetTime < now) {
            delete rateLimitStore[key];
        }
    }
}
/**
 * Get or create rate limit entry
 */
function getRateLimitEntry(key, windowMs) {
    const now = Date.now();
    const entry = rateLimitStore[key];
    if (!entry || entry.resetTime < now) {
        // Create new entry or reset expired entry
        rateLimitStore[key] = {
            count: 0,
            resetTime: now + windowMs,
        };
        return rateLimitStore[key];
    }
    return entry;
}
/**
 * Default key generator using user identity
 */
function defaultKeyGenerator(ctx, operation) {
    const identity = ctx.auth?.getUserIdentity?.();
    const userId = identity?.subject || 'anonymous';
    return `rate_limit:${operation}:${userId}`;
}
/**
 * Creates a rate limiting middleware
 */
export function createRateLimit(config) {
    return async function rateLimitMiddleware(ctx, operation) {
        // Clean up expired entries periodically
        if (Math.random() < 0.01) { // 1% chance to clean up
            cleanupExpiredEntries();
        }
        const key = config.keyGenerator ? config.keyGenerator(ctx) : defaultKeyGenerator(ctx, operation);
        const entry = getRateLimitEntry(key, config.windowMs);
        // Check if rate limit exceeded
        if (entry.count >= config.maxRequests) {
            const timeUntilReset = Math.ceil((entry.resetTime - Date.now()) / 1000);
            const message = config.message || `Rate limit exceeded. Try again in ${timeUntilReset} seconds.`;
            // Log rate limit violation
            logger.security('Rate limit exceeded', {
                operation,
                key: key.replace(/:[^:]+$/, ':***'), // Mask user ID
                count: entry.count,
                maxRequests: config.maxRequests,
                timeUntilReset,
            });
            throw new Error(message);
        }
        // Increment counter
        entry.count++;
        // Log if approaching limit
        if (entry.count > config.maxRequests * 0.8) {
            logger.warn('Approaching rate limit', {
                operation,
                count: entry.count,
                maxRequests: config.maxRequests,
                percentUsed: Math.round((entry.count / config.maxRequests) * 100),
            });
        }
    };
}
/**
 * Pre-configured rate limiters for different operation types
 */
export const rateLimiters = {
    /**
     * Standard API operations
     */
    standard: createRateLimit({
        maxRequests: PRODUCTION_SECURITY_CONFIG.DEFAULT_RATE_LIMIT,
        windowMs: 60 * 1000, // 1 minute
        message: 'Too many requests. Please try again later.',
    }),
    /**
     * Expensive operations (AI calls, external APIs)
     */
    expensive: createRateLimit({
        maxRequests: PRODUCTION_SECURITY_CONFIG.EXPENSIVE_OPERATION_RATE_LIMIT,
        windowMs: 60 * 1000, // 1 minute
        message: 'Too many AI/API requests. Please wait before trying again.',
    }),
    /**
     * xAI search operations with daily limits
     */
    xaiSearch: createRateLimit({
        maxRequests: PRODUCTION_SECURITY_CONFIG.MAX_XAI_SEARCHES_PER_DAY,
        windowMs: 24 * 60 * 60 * 1000, // 24 hours
        message: 'Daily xAI search limit reached. Please try again tomorrow.',
    }),
    /**
     * Twitter API operations with hourly limits
     */
    twitterApi: createRateLimit({
        maxRequests: PRODUCTION_SECURITY_CONFIG.MAX_TWITTER_REQUESTS_PER_HOUR,
        windowMs: 60 * 60 * 1000, // 1 hour
        message: 'Twitter API hourly limit reached. Please try again later.',
    }),
    /**
     * Authentication operations (more restrictive)
     */
    auth: createRateLimit({
        maxRequests: 20,
        windowMs: 15 * 60 * 1000, // 15 minutes
        message: 'Too many authentication attempts. Please try again later.',
    }),
    /**
     * Data mutations (create, update, delete)
     */
    mutation: createRateLimit({
        maxRequests: 50,
        windowMs: 60 * 1000, // 1 minute
        message: 'Too many changes. Please wait before making more updates.',
    }),
};
/**
 * Decorator for applying rate limiting to Convex functions
 */
export function withRateLimit(limiter, operation) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = async function (ctx, args) {
            const operationName = operation || `${target.constructor.name}.${propertyKey}`;
            // Apply rate limiting
            await limiter(ctx, operationName);
            // Call original method
            return originalMethod.call(this, ctx, args);
        };
        return descriptor;
    };
}
/**
 * Helper to apply rate limiting in Convex handlers
 */
export async function checkRateLimit(ctx, operation, type = 'standard') {
    await rateLimiters[type](ctx, operation);
}
/**
 * Get current rate limit status for a user
 */
export function getRateLimitStatus(ctx, operation, type = 'standard') {
    const config = getRateLimitConfig(type);
    const key = defaultKeyGenerator(ctx, operation);
    const entry = rateLimitStore[key];
    if (!entry || entry.resetTime < Date.now()) {
        return {
            remaining: config.maxRequests,
            resetTime: Date.now() + config.windowMs,
            total: config.maxRequests,
        };
    }
    return {
        remaining: Math.max(0, config.maxRequests - entry.count),
        resetTime: entry.resetTime,
        total: config.maxRequests,
    };
}
/**
 * Get rate limit configuration for a type
 */
function getRateLimitConfig(type) {
    const configs = {
        standard: {
            maxRequests: PRODUCTION_SECURITY_CONFIG.DEFAULT_RATE_LIMIT,
            windowMs: 60 * 1000,
        },
        expensive: {
            maxRequests: PRODUCTION_SECURITY_CONFIG.EXPENSIVE_OPERATION_RATE_LIMIT,
            windowMs: 60 * 1000,
        },
        xaiSearch: {
            maxRequests: PRODUCTION_SECURITY_CONFIG.MAX_XAI_SEARCHES_PER_DAY,
            windowMs: 24 * 60 * 60 * 1000,
        },
        twitterApi: {
            maxRequests: PRODUCTION_SECURITY_CONFIG.MAX_TWITTER_REQUESTS_PER_HOUR,
            windowMs: 60 * 60 * 1000,
        },
        auth: {
            maxRequests: 20,
            windowMs: 15 * 60 * 1000,
        },
        mutation: {
            maxRequests: 50,
            windowMs: 60 * 1000,
        },
    };
    return configs[type];
}
/**
 * Reset rate limit for a specific user and operation (admin function)
 */
export function resetRateLimit(userId, operation) {
    const key = `rate_limit:${operation}:${userId}`;
    delete rateLimitStore[key];
    logger.info('Rate limit reset', { operation, userId: userId.slice(0, 8) + '***' });
}
