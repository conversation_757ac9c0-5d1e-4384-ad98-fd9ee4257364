/**
 * 🚀 BANDWIDTH OPTIMIZATION PROJECTION FUNCTIONS
 *
 * These functions transform full database objects into lightweight versions
 * for different UI contexts, reducing bandwidth by 60-80%
 */
import type { Doc } from "../_generated/dataModel";
import type { LightweightMention, MentionSummary, LightweightTweet, TweetSummary, LightweightResponse, ResponseSummary } from "../types/optimized";
/**
 * Transform full mention to lightweight version for list views
 * 🚀 BANDWIDTH REDUCTION: ~2-5KB → ~300-500 bytes (80-90% reduction)
 */
export declare function projectLightweightMention(mention: Doc<"mentions">): LightweightMention;
/**
 * Transform full mention to ultra-lightweight summary for stats
 * 🚀 BANDWIDTH REDUCTION: ~2-5KB → ~100-150 bytes (95-97% reduction)
 */
export declare function projectMentionSummary(mention: Doc<"mentions">): MentionSummary;
/**
 * Batch project mentions to lightweight format
 */
export declare function projectMentionList(mentions: Doc<"mentions">[]): LightweightMention[];
/**
 * Batch project mentions to summary format
 */
export declare function projectMentionSummaries(mentions: Doc<"mentions">[]): MentionSummary[];
/**
 * Transform full tweet to lightweight version for feeds
 * 🚀 BANDWIDTH REDUCTION: ~1-3KB → ~200-400 bytes (70-85% reduction)
 */
export declare function projectLightweightTweet(tweet: Doc<"tweets">): LightweightTweet;
/**
 * Transform full tweet to ultra-lightweight summary for stats
 * 🚀 BANDWIDTH REDUCTION: ~1-3KB → ~80-100 bytes (95-97% reduction)
 */
export declare function projectTweetSummary(tweet: Doc<"tweets">): TweetSummary;
/**
 * Batch project tweets to lightweight format
 */
export declare function projectTweetList(tweets: Doc<"tweets">[]): LightweightTweet[];
/**
 * Batch project tweets to summary format
 */
export declare function projectTweetSummaries(tweets: Doc<"tweets">[]): TweetSummary[];
/**
 * Transform full response to lightweight version for management views
 * 🚀 BANDWIDTH REDUCTION: ~1-2KB → ~250-350 bytes (70-80% reduction)
 */
export declare function projectLightweightResponse(response: Doc<"responses">): LightweightResponse;
/**
 * Transform full response to ultra-lightweight summary for stats
 * 🚀 BANDWIDTH REDUCTION: ~1-2KB → ~50-80 bytes (95-97% reduction)
 */
export declare function projectResponseSummary(response: Doc<"responses">): ResponseSummary;
/**
 * Batch project responses to lightweight format
 */
export declare function projectResponseList(responses: Doc<"responses">[]): LightweightResponse[];
/**
 * Batch project responses to summary format
 */
export declare function projectResponseSummaries(responses: Doc<"responses">[]): ResponseSummary[];
/**
 * Calculate bandwidth savings from projections
 */
export declare function calculateBandwidthSavings(originalCount: number, originalSizePerItem: number, projectedSizePerItem: number): {
    originalTotal: number;
    projectedTotal: number;
    savings: number;
    savingsPercent: number;
};
/**
 * Log bandwidth optimization metrics (for monitoring)
 */
export declare function logProjectionMetrics(operation: string, itemCount: number, originalSize: number, projectedSize: number): void;
/**
 * Smart projection selector based on UI context
 */
export declare const ProjectionSelector: {
    /**
     * Select appropriate mention projection based on context
     */
    selectMentionProjection: (context: "list" | "card" | "stats" | "full") => (mention: Doc<"mentions">) => any;
    /**
     * Select appropriate tweet projection based on context
     */
    selectTweetProjection: (context: "feed" | "card" | "stats" | "full") => (tweet: Doc<"tweets">) => any;
    /**
     * Select appropriate response projection based on context
     */
    selectResponseProjection: (context: "queue" | "management" | "stats" | "full") => (response: Doc<"responses">) => any;
};
