/**
 * API monitoring and usage tracking for Twitter integration
 */

import type { ConvexContext, TwitterClientConfig } from "../types/twitter";

export class TwitterAPIMonitor {
  constructor(
    private config: TwitterClientConfig,
    private ctx?: ConvexContext,
    private enableMonitoring: boolean = true
  ) {}

  /**
   * Check quota limits before making requests
   */
  async checkQuotaLimits(): Promise<boolean> {
    if (!this.enableMonitoring || !this.ctx || !this.config.quotaManagement.enableQuotaTracking) {
      return true; // Skip quota check if monitoring is disabled
    }

    try {
      const quotaCheck = await this.ctx.runQuery("lib/twitter_api_monitor:shouldBlockRequests") as { 
        shouldBlock: boolean; 
        reason?: string;
      };
      
      if (quotaCheck.shouldBlock) {
        throw new Error(`Request blocked due to quota limits: ${quotaCheck.reason}`);
      }
      return true;
    } catch (error) {
      // If quota check fails, log but don't block (fail open)
      console.warn("Quota check failed:", error);
      return true;
    }
  }

  /**
   * Track API usage for monitoring
   */
  async trackUsage(
    endpoint: string,
    startTime: number,
    response: Response,
    requestCount: number = 1,
    error?: Error
  ): Promise<void> {
    if (!this.enableMonitoring || !this.ctx || !this.config.monitoring.enableUsageLogging) {
      return;
    }

    try {
      const duration = Date.now() - startTime;
      const responseSize = response.headers.get("content-length") 
        ? parseInt(response.headers.get("content-length")!) 
        : undefined;

      // Estimate cost based on endpoint and response size
      const estimatedCost = this.estimateCost(endpoint, requestCount, responseSize);

      await this.ctx.runMutation("lib/twitter_api_monitor:trackAPIUsage", {
        endpoint,
        requestCount,
        responseSize,
        duration,
        status: error ? (response.status === 429 ? "rate_limited" : "error") : "success",
        errorMessage: error?.message,
        estimatedCost,
      });
    } catch (trackingError) {
      console.warn("Failed to track API usage:", trackingError);
    }
  }

  /**
   * Estimate the cost of an API call
   */
  private estimateCost(endpoint: string, requestCount: number, responseSize?: number): number {
    // Basic cost estimation - adjust based on TwitterAPI.io pricing
    const baseCostPerRequest = 0.001; // $0.001 per request as example
    
    // Different endpoints may have different costs
    const endpointMultipliers: Record<string, number> = {
      "/twitter/tweet/advanced_search": 2.0, // Search is more expensive
      "/v2/users/by/username": 1.0,
      "/v2/users/{id}/tweets": 1.5,
      "/v2/tweets": 1.0,
    };

    const multiplier = endpointMultipliers[endpoint] || 1.0;
    let cost = baseCostPerRequest * requestCount * multiplier;

    // Add cost based on response size if available
    if (responseSize) {
      const sizeCost = responseSize / 1024 / 1024 * 0.0001; // Small cost per MB
      cost += sizeCost;
    }

    return cost;
  }

  /**
   * Get current usage statistics
   */
  async getUsageStats(timeRange: "hour" | "day" | "week" | "month" = "day"): Promise<any> {
    if (!this.ctx) return null;

    try {
      return await this.ctx.runQuery("lib/twitter_api_monitor:getUsageAnalytics", { 
        timeRange 
      });
    } catch (error) {
      console.warn("Failed to get usage stats:", error);
      return null;
    }
  }

  /**
   * Get quota status
   */
  async getQuotaStatus(): Promise<any> {
    if (!this.ctx) return null;

    try {
      return await this.ctx.runQuery("lib/twitter_api_monitor:getQuotaStatus");
    } catch (error) {
      console.warn("Failed to get quota status:", error);
      return null;
    }
  }

  /**
   * Get cost analysis
   */
  async getCostAnalysis(timeRange: "day" | "week" | "month" = "month"): Promise<any> {
    if (!this.ctx) return null;

    try {
      return await this.ctx.runQuery("lib/twitter_api_monitor:getCostAnalysis", { 
        timeRange 
      });
    } catch (error) {
      console.warn("Failed to get cost analysis:", error);
      return null;
    }
  }

  /**
   * Run health check
   */
  async runHealthCheck(): Promise<any> {
    if (!this.ctx) return null;

    try {
      return await this.ctx.runQuery("lib/twitter_api_monitor:apiHealthCheck");
    } catch (error) {
      console.warn("Failed to run health check:", error);
      return null;
    }
  }

  /**
   * Log performance metrics
   */
  async logPerformanceMetrics(
    endpoint: string,
    duration: number,
    requestSize?: number,
    responseSize?: number
  ): Promise<void> {
    if (!this.enableMonitoring || !this.ctx) {
      return;
    }

    try {
      await this.ctx.runMutation("lib/twitter_api_monitor:logPerformanceMetrics", {
        endpoint,
        duration,
        requestSize,
        responseSize,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.warn("Failed to log performance metrics:", error);
    }
  }

  /**
   * Track error patterns for analysis
   */
  async trackError(
    endpoint: string,
    errorType: string,
    errorMessage: string,
    statusCode?: number
  ): Promise<void> {
    if (!this.enableMonitoring || !this.ctx) {
      return;
    }

    try {
      await this.ctx.runMutation("lib/twitter_api_monitor:trackError", {
        endpoint,
        errorType,
        errorMessage,
        statusCode,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.warn("Failed to track error:", error);
    }
  }

  /**
   * Get error analytics
   */
  async getErrorAnalytics(timeRange: "hour" | "day" | "week" = "day"): Promise<any> {
    if (!this.ctx) return null;

    try {
      return await this.ctx.runQuery("lib/twitter_api_monitor:getErrorAnalytics", { 
        timeRange 
      });
    } catch (error) {
      console.warn("Failed to get error analytics:", error);
      return null;
    }
  }
}