/**
 * Rate limiting logic for Twitter API integration
 */
import type { RateLimitInfo, TwitterClientConfig } from "../types/twitter";
export declare class TwitterRateLimiter {
    private config;
    private rateLimitState;
    constructor(config: TwitterClientConfig);
    /**
     * Check if we're rate limited for a specific endpoint
     */
    isRateLimited(endpoint: string): boolean;
    /**
     * Update rate limit information from response headers
     */
    updateRateLimitInfo(endpoint: string, headers: Headers): void;
    /**
     * Update rate limit info from 429 response
     */
    updateRateLimitFromResponse(endpoint: string, response: Response): void;
    /**
     * Get rate limit info for an endpoint
     */
    getRateLimitInfo(endpoint: string): RateLimitInfo | null;
    /**
     * Get all rate limit status for debugging
     */
    getAllRateLimitStatus(): Record<string, RateLimitInfo>;
    /**
     * Apply exponential backoff for retries
     */
    applyBackoff(attempt: number, baseDelay: number): Promise<void>;
    /**
     * Apply default rate limiting delay
     */
    applyDefaultDelay(): Promise<void>;
    /**
     * Calculate time until rate limit resets
     */
    getTimeUntilReset(endpoint: string): number;
    /**
     * Clear rate limit state for an endpoint (useful for testing)
     */
    clearRateLimitState(endpoint?: string): void;
}
