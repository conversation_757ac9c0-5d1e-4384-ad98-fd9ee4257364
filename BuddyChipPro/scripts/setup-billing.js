#!/usr/bin/env node

/**
 * 🔧 BuddyChip Pro - Billing System Setup Script
 * 
 * This script helps configure the billing system by:
 * 1. Validating environment variables
 * 2. Testing webhook connectivity
 * 3. Verifying Clerk/Stripe configuration
 * 4. Setting up initial data
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Check if required environment variables are set
 */
function checkEnvironmentVariables() {
  log('\n🔍 Checking environment variables...', 'cyan');
  
  const requiredVars = [
    'VITE_CLERK_PUBLISHABLE_KEY',
    'CLERK_SECRET_KEY',
    'CLERK_WEBHOOK_SECRET',
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_SECRET_KEY',
  ];

  const optionalVars = [
    'CLERK_BILLING_PRODUCT_STARTER',
    'CLERK_BILLING_PRODUCT_PRO', 
    'CLERK_BILLING_PRODUCT_ENTERPRISE',
    'STRIPE_PRICE_STARTER_MONTHLY',
    'STRIPE_PRICE_PRO_MONTHLY',
    'STRIPE_PRICE_ENTERPRISE_MONTHLY',
  ];

  let allRequired = true;
  let hasOptional = false;

  // Check required variables
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      logSuccess(`${varName} is set`);
    } else {
      logError(`${varName} is missing`);
      allRequired = false;
    }
  }

  // Check optional variables
  for (const varName of optionalVars) {
    if (process.env[varName]) {
      logSuccess(`${varName} is set`);
      hasOptional = true;
    } else {
      logWarning(`${varName} is not set (optional)`);
    }
  }

  if (!allRequired) {
    logError('Missing required environment variables. Please check your .env.local file.');
    return false;
  }

  if (!hasOptional) {
    logWarning('No optional billing configuration found. You may need to configure Clerk products and Stripe prices.');
  }

  return true;
}

/**
 * Validate Clerk configuration
 */
function validateClerkConfig() {
  log('\n🔐 Validating Clerk configuration...', 'cyan');
  
  const publishableKey = process.env.VITE_CLERK_PUBLISHABLE_KEY;
  const secretKey = process.env.CLERK_SECRET_KEY;
  const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

  // Basic format validation
  if (publishableKey && publishableKey.startsWith('pk_')) {
    logSuccess('Clerk publishable key format is valid');
  } else {
    logError('Clerk publishable key format is invalid');
    return false;
  }

  if (secretKey && secretKey.startsWith('sk_')) {
    logSuccess('Clerk secret key format is valid');
  } else {
    logError('Clerk secret key format is invalid');
    return false;
  }

  if (webhookSecret && webhookSecret.startsWith('whsec_')) {
    logSuccess('Clerk webhook secret format is valid');
  } else {
    logError('Clerk webhook secret format is invalid');
    return false;
  }

  return true;
}

/**
 * Validate Stripe configuration
 */
function validateStripeConfig() {
  log('\n💳 Validating Stripe configuration...', 'cyan');
  
  const publishableKey = process.env.STRIPE_PUBLISHABLE_KEY;
  const secretKey = process.env.STRIPE_SECRET_KEY;

  // Basic format validation
  if (publishableKey && publishableKey.startsWith('pk_')) {
    logSuccess('Stripe publishable key format is valid');
  } else {
    logError('Stripe publishable key format is invalid');
    return false;
  }

  if (secretKey && secretKey.startsWith('sk_')) {
    logSuccess('Stripe secret key format is valid');
  } else {
    logError('Stripe secret key format is invalid');
    return false;
  }

  return true;
}

/**
 * Check if webhook endpoint is accessible
 */
async function testWebhookEndpoint() {
  log('\n🌐 Testing webhook endpoint...', 'cyan');
  
  const webhookUrl = process.env.BILLING_WEBHOOK_ENDPOINT || 'http://localhost:3000/api/billing/webhook';
  
  try {
    // For local development, just check if the endpoint is configured
    if (webhookUrl.includes('localhost')) {
      logInfo('Local webhook endpoint configured');
      logWarning('Make sure your development server is running');
      return true;
    }

    // For production, we could test the actual endpoint
    logInfo(`Webhook endpoint: ${webhookUrl}`);
    logWarning('Webhook endpoint testing requires the server to be running');
    return true;
    
  } catch (error) {
    logError(`Webhook endpoint test failed: ${error.message}`);
    return false;
  }
}

/**
 * Generate billing configuration summary
 */
function generateConfigSummary() {
  log('\n📋 Billing Configuration Summary', 'cyan');
  
  const config = {
    clerk: {
      publishableKey: process.env.VITE_CLERK_PUBLISHABLE_KEY ? '✅ Set' : '❌ Missing',
      secretKey: process.env.CLERK_SECRET_KEY ? '✅ Set' : '❌ Missing',
      webhookSecret: process.env.CLERK_WEBHOOK_SECRET ? '✅ Set' : '❌ Missing',
    },
    stripe: {
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY ? '✅ Set' : '❌ Missing',
      secretKey: process.env.STRIPE_SECRET_KEY ? '✅ Set' : '❌ Missing',
    },
    products: {
      starter: process.env.CLERK_BILLING_PRODUCT_STARTER ? '✅ Set' : '⚠️ Not set',
      pro: process.env.CLERK_BILLING_PRODUCT_PRO ? '✅ Set' : '⚠️ Not set',
      enterprise: process.env.CLERK_BILLING_PRODUCT_ENTERPRISE ? '✅ Set' : '⚠️ Not set',
    },
    webhook: {
      endpoint: process.env.BILLING_WEBHOOK_ENDPOINT || 'Default (localhost)',
    },
  };

  console.table(config);
}

/**
 * Provide setup instructions
 */
function provideSetupInstructions() {
  log('\n📚 Setup Instructions', 'cyan');
  
  log('\n1. Clerk Dashboard Setup:', 'yellow');
  log('   • Go to https://dashboard.clerk.com/');
  log('   • Navigate to Billing section');
  log('   • Enable Stripe integration');
  log('   • Create subscription products');
  log('   • Configure webhook endpoint');
  
  log('\n2. Stripe Dashboard Setup:', 'yellow');
  log('   • Go to https://dashboard.stripe.com/');
  log('   • Create products for each plan');
  log('   • Create price objects for monthly/annual billing');
  log('   • Copy price IDs to environment variables');
  
  log('\n3. Environment Configuration:', 'yellow');
  log('   • Copy .env.example to .env.local');
  log('   • Fill in all required API keys');
  log('   • Configure product and price IDs');
  log('   • Set webhook endpoint URL');
  
  log('\n4. Testing:', 'yellow');
  log('   • Run the development server');
  log('   • Test webhook reception with ngrok');
  log('   • Create test subscriptions');
  log('   • Verify database updates');
}

/**
 * Main setup function
 */
async function main() {
  log('🚀 BuddyChip Pro - Billing System Setup', 'magenta');
  log('=====================================', 'magenta');
  
  // Load environment variables
  require('dotenv').config({ path: '.env.local' });
  
  let allChecksPass = true;
  
  // Run all checks
  allChecksPass &= checkEnvironmentVariables();
  allChecksPass &= validateClerkConfig();
  allChecksPass &= validateStripeConfig();
  allChecksPass &= await testWebhookEndpoint();
  
  // Generate summary
  generateConfigSummary();
  
  // Provide instructions if needed
  if (!allChecksPass) {
    provideSetupInstructions();
    log('\n❌ Setup incomplete. Please address the issues above.', 'red');
    process.exit(1);
  } else {
    log('\n✅ Billing system configuration looks good!', 'green');
    log('🎉 You can now start testing the billing functionality.', 'green');
  }
}

// Run the setup
if (require.main === module) {
  main().catch(error => {
    logError(`Setup failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  checkEnvironmentVariables,
  validateClerkConfig,
  validateStripeConfig,
  testWebhookEndpoint,
};
