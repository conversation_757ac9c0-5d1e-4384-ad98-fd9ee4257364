#!/usr/bin/env node

/**
 * TwitterAPI.io Setup and Validation Script
 * 
 * This script helps users set up and validate their TwitterAPI.io integration.
 * Run with: node scripts/setup-twitter-api.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 TwitterAPI.io Setup and Validation Script\n');

// Configuration
const backendDir = path.join(__dirname, '..', 'packages', 'backend');
const envLocalPath = path.join(backendDir, '.env.local');
const envExamplePath = path.join(backendDir, '.env.example');

// Helper functions
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function readEnvFile(filePath) {
  if (!checkFileExists(filePath)) return {};
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

function validateApiKey(apiKey) {
  if (!apiKey || apiKey === 'your_api_key_here') {
    return { valid: false, message: 'API key is missing or placeholder' };
  }
  
  if (apiKey.length < 20) {
    return { valid: false, message: 'API key appears to be too short' };
  }
  
  return { valid: true, message: 'API key format looks valid' };
}

async function testApiConnection(apiKey) {
  console.log('🔍 Testing API connection...');
  
  try {
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch('https://api.twitterapi.io/v2/users/by/username/twitter', {
      method: 'HEAD',
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json',
      },
    });
    
    if (response.ok) {
      console.log('✅ API connection successful!');
      return { success: true, message: 'Connection successful' };
    } else {
      console.log(`❌ API connection failed: ${response.status} ${response.statusText}`);
      return { success: false, message: `HTTP ${response.status}: ${response.statusText}` };
    }
  } catch (error) {
    console.log(`❌ Network error: ${error.message}`);
    return { success: false, message: error.message };
  }
}

function runConvexValidation() {
  console.log('🔍 Running Convex configuration validation...');
  
  try {
    const result = execSync('npx convex run lib/config_validator:validateEnvironmentSetup', {
      cwd: backendDir,
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log('✅ Convex validation completed');
    return { success: true, output: result };
  } catch (error) {
    console.log('❌ Convex validation failed');
    return { success: false, error: error.message };
  }
}

// Main setup process
async function main() {
  console.log('📁 Checking project structure...');
  
  // Check if we're in the right directory
  if (!checkFileExists(backendDir)) {
    console.log('❌ Backend directory not found. Please run this script from the project root.');
    process.exit(1);
  }
  
  // Check for .env.local file
  if (!checkFileExists(envLocalPath)) {
    console.log('📝 Creating .env.local from template...');
    
    if (checkFileExists(envExamplePath)) {
      fs.copyFileSync(envExamplePath, envLocalPath);
      console.log('✅ Created .env.local from .env.example');
      console.log('⚠️  Please edit .env.local and add your actual API keys');
    } else {
      console.log('❌ .env.example not found. Please create .env.local manually.');
    }
  }
  
  // Read environment configuration
  console.log('\n🔍 Validating environment configuration...');
  const env = readEnvFile(envLocalPath);
  
  let issuesFound = 0;
  const recommendations = [];
  
  // Check TwitterAPI.io key
  const twitterApiKey = env.TWITTERAPI_IO_API_KEY || env.TWEETIO_API_KEY;
  if (!twitterApiKey) {
    console.log('❌ TwitterAPI.io API key not found');
    console.log('   Set TWITTERAPI_IO_API_KEY in your .env.local file');
    console.log('   Get your key from: https://twitterapi.io/');
    issuesFound++;
  } else {
    const keyValidation = validateApiKey(twitterApiKey);
    if (keyValidation.valid) {
      console.log('✅ TwitterAPI.io API key found and format looks valid');
      
      // Test API connection
      const connectionTest = await testApiConnection(twitterApiKey);
      if (!connectionTest.success) {
        console.log(`⚠️  API key present but connection failed: ${connectionTest.message}`);
        recommendations.push('Verify your API key is active and has the correct permissions');
      }
    } else {
      console.log(`❌ TwitterAPI.io API key issue: ${keyValidation.message}`);
      issuesFound++;
    }
  }
  
  // Check for legacy key usage
  if (env.TWEETIO_API_KEY && !env.TWITTERAPI_IO_API_KEY) {
    console.log('⚠️  Using legacy TWEETIO_API_KEY');
    recommendations.push('Consider updating to TWITTERAPI_IO_API_KEY for clarity');
  }
  
  // Check OpenRouter API key
  if (!env.OPENROUTER_API_KEY || env.OPENROUTER_API_KEY === 'your_openrouter_api_key_here') {
    console.log('⚠️  OpenRouter API key not configured');
    recommendations.push('Set OPENROUTER_API_KEY for AI response generation');
  } else {
    console.log('✅ OpenRouter API key found');
  }
  
  // Check Convex configuration
  if (!env.CONVEX_URL) {
    console.log('⚠️  Convex URL not found');
    recommendations.push('Run "bun dev:setup" to configure Convex');
  } else {
    console.log('✅ Convex configuration found');
  }
  
  // Check Node environment
  const nodeEnv = env.NODE_ENV || 'development';
  console.log(`ℹ️  Environment: ${nodeEnv}`);
  
  // Environment-specific checks
  if (nodeEnv === 'production') {
    if (!env.TWITTERAPI_IO_API_KEY) {
      console.log('❌ Production should use TWITTERAPI_IO_API_KEY explicitly');
      issuesFound++;
    }
    
    const dailyLimit = parseInt(env.TWITTERAPI_DAILY_REQUEST_LIMIT || '10000');
    if (dailyLimit < 5000) {
      console.log(`⚠️  Daily request limit (${dailyLimit}) may be low for production`);
      recommendations.push('Consider increasing TWITTERAPI_DAILY_REQUEST_LIMIT for production');
    }
  }
  
  // Check optional but recommended settings
  const recommendedSettings = [
    'TWITTERAPI_ENABLE_QUOTA_TRACKING',
    'TWITTERAPI_ENABLE_USAGE_LOGGING',
    'TWITTERAPI_WARNING_THRESHOLD',
    'TWITTERAPI_EMERGENCY_STOP_THRESHOLD',
  ];
  
  const missingRecommended = recommendedSettings.filter(setting => !env[setting]);
  if (missingRecommended.length > 0) {
    console.log(`ℹ️  ${missingRecommended.length} optional settings not configured`);
    recommendations.push('Consider adding recommended monitoring settings for better control');
  }
  
  // Run Convex validation if possible
  if (env.CONVEX_URL && twitterApiKey) {
    console.log('\n🔍 Running Convex-based validation...');
    const convexResult = runConvexValidation();
    
    if (convexResult.success) {
      try {
        const validationOutput = JSON.parse(convexResult.output);
        if (validationOutput.success) {
          console.log('✅ All Convex validations passed');
        } else {
          console.log('⚠️  Convex validation found issues:');
          console.log(validationOutput.message);
        }
      } catch (e) {
        console.log('ℹ️  Convex validation completed (output format unclear)');
      }
    } else {
      console.log('⚠️  Could not run Convex validation');
      recommendations.push('Ensure Convex is properly configured and running');
    }
  }
  
  // Summary
  console.log('\n📊 Setup Summary:');
  console.log(`   Critical Issues: ${issuesFound}`);
  console.log(`   Recommendations: ${recommendations.length}`);
  
  if (issuesFound === 0) {
    console.log('\n🎉 Configuration looks good! TwitterAPI.io integration should work properly.');
  } else {
    console.log('\n⚠️  Please fix the critical issues before proceeding.');
  }
  
  if (recommendations.length > 0) {
    console.log('\n💡 Recommendations:');
    recommendations.forEach((rec, i) => {
      console.log(`   ${i + 1}. ${rec}`);
    });
  }
  
  console.log('\n📚 For detailed setup instructions, see CLAUDE.md');
  console.log('🔗 Get your TwitterAPI.io key: https://twitterapi.io/');
  
  if (issuesFound > 0) {
    process.exit(1);
  }
}

// Handle unhandled errors
process.on('uncaughtException', (error) => {
  console.error('\n❌ Unexpected error:', error.message);
  console.error('Please check your configuration and try again.');
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('\n❌ Unhandled promise rejection:', reason);
  process.exit(1);
});

// Run the setup
main().catch(error => {
  console.error('\n❌ Setup failed:', error.message);
  process.exit(1);
});