// Simple ESLint configuration for transition period
module.exports = [
  {
    files: ["**/*.{js,ts,tsx}"],
    rules: {
      // Very basic rules only during Convex type transition
      "no-unused-vars": "off",
      "no-undef": "off",
      "prefer-const": "warn",
      "no-var": "error",
    },
  },
  {
    ignores: [
      "node_modules/",
      "dist/",
      ".turbo/",
      "**/_generated/",
      "convex/_generated/",
      "*.config.js",
      "*.config.ts"
    ],
  },
];