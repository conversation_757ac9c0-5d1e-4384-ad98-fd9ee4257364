# BuddyChip Pro - Clerk Billing Implementation Guide

## 🎯 Overview

This guide explains how to implement and use the Clerk billing system in BuddyChip Pro. The system provides subscription-based access control with three tiers: Starter, Pro, and Enterprise.

## 📋 Subscription Plans

### Starter Plan - $19/month
- **Features**: Basic monitoring, 100 AI responses/month, basic AI models
- **Limits**: 3 accounts, 1,000 API requests/day
- **Target**: Individuals getting started

### Pro Plan - $49/month  
- **Features**: Premium AI models, image generation, advanced analytics
- **Limits**: 10 accounts, 500 AI responses/month, 5,000 API requests/day
- **Target**: Professionals and small teams

### Enterprise Plan - $99/month
- **Features**: Unlimited responses, bulk processing, white-label options
- **Limits**: Unlimited accounts, 25,000 API requests/day
- **Target**: Agencies and large organizations

## 🔧 Backend Implementation

### 1. Database Schema

The billing system uses three main tables:

```typescript
// Subscription management
subscriptions: {
  userId: Id<"users">,
  clerkSubscriptionId: string,
  planId: "starter" | "pro" | "enterprise",
  status: "active" | "canceled" | "past_due" | "unpaid" | "incomplete",
  currentPeriodStart: number,
  currentPeriodEnd: number,
  // ... other fields
}

// Usage tracking
usageTracking: {
  userId: Id<"users">,
  date: string, // YYYY-MM-DD
  usage: {
    aiResponses: number,
    imageGenerations: number,
    apiRequests: number,
    // ... other metrics
  },
  limits: { /* corresponding limits */ }
}

// Feature access control
featureAccess: {
  userId: Id<"users">,
  planId: "starter" | "pro" | "enterprise",
  features: {
    basicMonitoring: boolean,
    premiumAi: boolean,
    imageGeneration: boolean,
    // ... other features
  },
  limits: { /* plan-specific limits */ }
}
```

### 2. Access Control Functions

#### Check Feature Access
```typescript
// In your Convex functions
import { requireFeatureAccess } from "../billing/middleware";

export const myProtectedFunction = action({
  handler: async (ctx, args) => {
    // Check if user has premium AI access
    const subscriptionInfo = await requireFeatureAccess(ctx, "premiumAi");
    
    // Function continues only if user has access
    // ...
  }
});
```

#### Check Usage Limits
```typescript
import { requireUsageLimit } from "../billing/middleware";

export const generateAIResponse = action({
  handler: async (ctx, args) => {
    // Check if user can generate AI responses
    await requireUsageLimit(ctx, "aiResponses", 1);
    
    // Generate response...
    
    // Track usage
    await ctx.runMutation(api.billing.usage.trackUsage, {
      feature: "aiResponses",
      amount: 1
    });
  }
});
```

### 3. Middleware Usage

```typescript
// Using decorators (if supported)
@withFeatureAccess("premiumAi")
@withUsageLimit("aiResponses", 1)
export const generatePremiumResponse = action({
  handler: async (ctx, args) => {
    // Function automatically protected
  }
});
```

## 🎨 Frontend Implementation

### 1. Subscription Guards

Protect UI components based on subscription:

```tsx
import { SubscriptionGuard } from '@/components/billing/subscription-guard';

function PremiumFeature() {
  return (
    <SubscriptionGuard feature="premiumAi">
      <PremiumAIComponent />
    </SubscriptionGuard>
  );
}
```

### 2. Usage Limit Guards

Protect actions based on usage limits:

```tsx
import { UsageLimitGuard } from '@/components/billing/subscription-guard';

function GenerateButton() {
  return (
    <UsageLimitGuard feature="aiResponses" amount={1}>
      <Button onClick={generateResponse}>
        Generate AI Response
      </Button>
    </UsageLimitGuard>
  );
}
```

### 3. Plan-based Guards

Protect entire sections based on plan:

```tsx
<SubscriptionGuard plan="pro">
  <AdvancedAnalyticsDashboard />
</SubscriptionGuard>
```

### 4. Subscription Hook

Use the subscription hook for complex logic:

```tsx
import { useSubscription } from '@/hooks/use-subscription';

function MyComponent() {
  const {
    planId,
    hasFeature,
    canUseFeature,
    trackFeatureUsage,
    isNearLimit,
    getRemainingUsage
  } = useSubscription();

  const handleGenerateResponse = async () => {
    const { canUse, remaining } = canUseFeature('aiResponses');
    
    if (!canUse) {
      toast.error('AI response limit reached. Upgrade for more!');
      return;
    }

    // Generate response...
    await trackFeatureUsage('aiResponses', 1);
  };

  return (
    <div>
      <h2>Current Plan: {planId}</h2>
      {hasFeature('premiumAi') && <PremiumFeatures />}
      {isNearLimit('aiResponses') && <UpgradePrompt />}
    </div>
  );
}
```

## 🔔 Webhook Setup

### 1. Configure Clerk Webhook

1. Go to Clerk Dashboard → Webhooks
2. Add endpoint: `https://your-domain.com/api/billing/webhook`
3. Select events: `subscription.*`, `invoice.*`
4. Copy webhook secret to `CLERK_WEBHOOK_SECRET`

### 2. Webhook Handler

The webhook handler is already implemented in `packages/backend/convex/billing/webhooks.ts`:

```typescript
export const clerkBillingWebhook = httpAction(async (ctx, request) => {
  // Handles subscription.created, subscription.updated, etc.
  // Automatically updates database with subscription changes
});
```

## 🎛️ Environment Variables

Add to your `.env.local`:

```bash
# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
CLERK_SECRET_KEY=sk_test_your_key_here
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Configuration (used by Clerk)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_key_here
```

## 📊 Usage Tracking

### Automatic Tracking

Usage is automatically tracked when using the middleware:

```typescript
// This automatically tracks usage
await requireUsageLimit(ctx, "aiResponses", 1);
// ... perform action ...
await ctx.runMutation(api.billing.usage.trackUsage, {
  feature: "aiResponses",
  amount: 1
});
```

### Manual Tracking

Track usage manually when needed:

```typescript
import { useUsageTracker } from '@/hooks/use-subscription';

function MyComponent() {
  const { trackUsage } = useUsageTracker();

  const handleAction = async () => {
    try {
      await trackUsage('imageGenerations', 1);
      // Perform action...
    } catch (error) {
      toast.error('Usage limit exceeded!');
    }
  };
}
```

## 🎨 UI Components

### Subscription Status

Display subscription info in dashboard:

```tsx
import { SubscriptionStatus } from '@/components/billing/subscription-status';

function Dashboard() {
  return (
    <div>
      <SubscriptionStatus />
      {/* Other dashboard content */}
    </div>
  );
}
```

### Pricing Page

The pricing page is available at `/pricing`:

```tsx
// Automatically includes Clerk's PricingTable component
// Shows plan comparison and upgrade options
```

## 🚀 Testing

### 1. Test Subscription Flow

1. Use Stripe test mode
2. Create test subscriptions in Clerk
3. Verify webhook events are received
4. Test feature access and usage limits

### 2. Test Access Control

```typescript
// Test feature access
const hasAccess = await ctx.runQuery(api.billing.accessControl.hasFeatureAccess, {
  feature: "premiumAi"
});

// Test usage limits
const canPerform = await ctx.runQuery(api.billing.usage.canPerformAction, {
  feature: "aiResponses",
  amount: 1
});
```

## 🔧 Customization

### Adding New Features

1. Update the `featureAccess` schema
2. Add feature to plan configurations in `middleware.ts`
3. Create UI guards for the new feature
4. Update pricing page with feature descriptions

### Adding New Usage Metrics

1. Add metric to `usageTracking` schema
2. Update plan limits in `getPlanLimits()`
3. Add tracking calls in relevant functions
4. Create UI components to display usage

## 📈 Analytics

Get usage analytics:

```typescript
const analytics = await ctx.runQuery(api.billing.usage.getUsageAnalytics, {
  startDate: "2024-01-01",
  endDate: "2024-01-31"
});
```

## 🎯 Best Practices

1. **Always check access before expensive operations**
2. **Track usage immediately after successful operations**
3. **Provide clear upgrade prompts when limits are reached**
4. **Use subscription guards to hide unavailable features**
5. **Monitor webhook delivery and handle failures gracefully**
6. **Test thoroughly with Stripe test mode before going live**

## 🆘 Troubleshooting

### Common Issues

1. **Webhook not receiving events**: Check URL and secret
2. **Access denied errors**: Verify feature access configuration
3. **Usage tracking failures**: Check database permissions
4. **UI not updating**: Ensure proper query invalidation

### Debug Tools

Use the subscription hook for debugging:

```tsx
const { isLoading, subscription, features, usage } = useSubscription();
console.log('Subscription Debug:', { subscription, features, usage });
```
