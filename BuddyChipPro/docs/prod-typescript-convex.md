# 📚 Achieving "Prod-Grade" Type-Safety & Stability with Convex + TypeScript

> The goal: **zero runtime surprises**, **fast CI**, and **clean IDE experience** while working with a rapidly-changing Convex backend and a large React/Bun monorepo.
>
> Everything below targets Bun ≥ 1.2, Turbo ≥ 2.5, Convex 1.24.x and TypeScript ≤ 5.3. The principles are tool-agnostic.

---

## 0  Why the Big Explosion Happened

* **TypeScript 5.4** tightened the recursion cap that protects the compiler from runaway conditional-type evaluation.
* Convex 1.24 still ships _very_ deep conditional/union types (validators, `Id`, etc.).
* Result: the compiler hits depth 50 and throws
  ```
  TS2589: Type instantiation is excessively deep and possibly infinite.
  ```

Our strategy: **pin** the compiler to a safe version _and_ **layer** configs so we can harden gradually.

---

## 1  Pin a "Safe" Compiler  — *immediately unblocks everyone*

```bash
# workspace-wide
bun add -D typescript@5.3.3 -w
```

```jsonc
// package.json (root)
{
  "resolutions": {
    "typescript": "5.3.3"
  }
}
```

* TS 5.3 has the old 1 000-level recursion limit, perfect for Convex 1.24.
* You stay current on syntax while waiting for Convex ≥ 1.25 to fix its types.

---

## 2  Layered `tsconfig` Setup

```
repo/
│
├─ apps/web/tsconfig.json           ← React / ShadCN / Vite
├─ packages/backend/tsconfig.json    ← Convex functions
└─ tsconfig.base.json                ← shared rules & aliases
```

### 2.1  Base rules (`tsconfig.base.json`)

```jsonc
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "Bundler",
    "jsx": "react-jsx",

    "strict": true,
    "skipLibCheck": false,
    "forceConsistentCasingInFileNames": true,
    "importsNotUsedAsValues": "error",
    "exactOptionalPropertyTypes": true,

    "types": ["bun-types"]
  },
  "exclude": ["node_modules", "dist"]
}
```

### 2.2  Backend (Convex) override

```jsonc
{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,

    // TEMP – until Convex types are updated
    "skipLibCheck": true,
    "noPropertyAccessFromIndexSignature": false
  },
  "include": ["convex/**/*.ts"]
}
```

### 2.3  Frontend override

```jsonc
{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "isolatedModules": true,
    "noEmit": true
  },
  "references": [{ "path": "../../packages/backend" }]
}
```

---

## 3  Convex-Specific Hardening Steps

1. **Single `convex/tsconfig.json`** (Convex ≥ 1.13)
   ```bash
   cd packages/backend
   npx convex codegen --init
   ```
2. **Return-Value Validators**
   ```ts
   export const listPopular = query({
     args: { limit: v.number() },
     returns: v.array(v.object({
       _id: v.id("generatedImages"),
       downloadCount: v.number(),
       createdAt: v.number()
     })),
     handler: async (ctx, { limit }) => { /* … */ }
   });
   ```
3. **Split Mega-Unions** – factor huge `v.union(v.literal("a"), …)` chains into reusable named validators to avoid deep nesting.

---

## 4  Strict-Mode Error Budget (incremental cleanup)

Even after fixing TS2589 you'll see many *real* errors.

| Phase | Focus                              | Goal |
|-------|------------------------------------|------|
| P0    | Keep `bun tsc --noEmit` green      | 0 new errors |
| P1    | Backend implicit `any`             | −100 |
| P2    | Shape mismatches (`TS2339`, etc.)  | −75  |
| P3    | React surface                      | 0    |

Lock each phase by raising the ESLint rule severity from *warn* → *error*.

---

## 5  ESLint (type-aware) + Bun

```js
module.exports = {
  root: true,
  parser: "@typescript-eslint/parser",
  plugins: ["@typescript-eslint", "import"],
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/strict-type-checked",
    "plugin:@typescript-eslint/stylistic-type-checked"
  ],
  overrides: [
    {
      files: ["packages/backend/convex/**/*.ts"],
      rules: {
        "@typescript-eslint/no-unsafe-member-access": "warn"
      }
    }
  ]
};
```

Run in Turbo:
```json
"lint": { "dependsOn": ["^lint"], "outputs": [] }
```

---

## 6  Testing Strategy

* **Unit**: `convex-test` (≥ 0.0.16) for functions.
* **Integration**: spin local Convex with `npx convex dev --once`.
* **Runner**: Bun's built-in `bun test` (fast).

```ts
import { setupConvex } from "convex-test";

 test("mentions are returned newest-first", async () => {
   const { db } = await setupConvex();
   await seedMentions(db, 3);
   const res = await api.mentions.fetchMentions({});
   expect(res[0]._creationTime).toBeGreaterThan(res[1]._creationTime);
 });
```

---

## 7  CI / CD Pipeline (GitHub Actions)

```yaml
name: CI
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: oven-sh/setup-bun@v1
      with: { bun-version: "1.2.15" }

    - name: Install deps
      run: bun install --frozen-lockfile

    - name: Lint & types
      run: turbo lint check-types

    - name: Tests
      run: bun test

  deploy:
    needs: build
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    - uses: oven-sh/setup-bun@v1
    - run: bun install --production
    - run: bunx convex deploy --deployment production --yes
      env:
        CONVEX_DEPLOYMENT: ${{ secrets.CONVEX_DEPLOYMENT }}
        CONVEX_ADMIN_KEY: ${{ secrets.CONVEX_ADMIN_KEY }}
```

---

## 8  Turbo Remote Cache

```jsonc
// turbo.json
{
  "$schema": "https://turbo.build/schema.json",
  "tasks": {
    "check-types": { "outputs": [] },
    "test": { "outputs": [] },
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**"]
    }
  }
}
```

No outputs on lint & type-check ⇒ perfectly cacheable.

---

## 9  Runtime Health & Observability

* Add a `monitoring/healthCheck.ts` mutation returning `{ status: "ok" }`.
* Call it post-deploy; rollback on failure.
* Convex dashboard → Logs tab now supports drill-down per request (1.24+).

---

## 10  Upgrade Path

1. Watch Convex release notes (≥ 1.25 expected to fix deep recursion).
2. Remove the TypeScript pin, run `bun tsc --noEmit`.
3. Delete backend `skipLibCheck` and tighten remaining flags.

---

## ✅  Final Checklist (TL;DR)

- [ ] TS 5.3.3 pinned; backend `skipLibCheck` true.
- [ ] Three-layer `tsconfig` structure.
- [ ] Convex `returns:` validators in every public function.
- [ ] ESLint strict, phased error budget enforced.
- [ ] `bun test` + `convex-test` coverage ≥ 70 %.
- [ ] GitHub Actions: lint → types → tests → deploy.
- [ ] Turbo remote cache configured.
- [ ] Health-check mutation and rollback plan.
- [ ] Upgrade watchlist for Convex → unpin TS when ready.

Implement these layers and your codebase will compile cleanly, run fast, and stay healthy as both TypeScript and Convex evolve. 